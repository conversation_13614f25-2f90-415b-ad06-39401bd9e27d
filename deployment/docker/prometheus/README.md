# NTA 平台使用 Prometheus 和 Grafana 的监控

本目录包含在 Docker Compose 环境中使用 Prometheus 和 Grafana 监控 NTA 平台的配置文件。

## 组件

1. **Prometheus**: 用于收集和存储 NTA 平台服务和基础设施组件的指标。
2. **Grafana**: 用于可视化 Prometheus 收集的指标。
3. **AlertManager**: 用于根据 Prometheus 收集的指标发送告警。
4. **Node Exporter**: 用于收集主机系统指标。
5. **Redis Exporter**: 用于收集 Redis 指标。
6. **MySQL Exporter**: 用于收集 MySQL 指标。
7. **Elasticsearch Exporter**: 用于收集 Elasticsearch 指标。
8. **Kafka Exporter**: 用于收集 Kafka 指标。
9. **Nebula Exporter**: 用于收集 Nebula Graph 指标。
10. **cAdvisor**: 用于收集容器指标。

## 目录结构

```bash
prometheus/
├── prometheus.yml                # Prometheus 配置文件
├── alertmanager.yml             # AlertManager 配置文件
├── rules/                       # Prometheus 告警规则
│   └── alerts.yml               # NTA 平台的告警规则
├── grafana/                     # Grafana 配置
│   ├── provisioning/            # Grafana 配置管理
│   │   ├── datasources/         # Grafana 数据源配置
│   │   │   └── prometheus.yml   # Prometheus 数据源配置
│   │   └── dashboards/          # Grafana 仪表盘配置
│   │       └── dashboards.yml   # 仪表盘配置管理
│   └── dashboards/              # Grafana 仪表盘
│       ├── nta-services-dashboard.json  # NTA 服务仪表盘
│       ├── infrastructure-dashboard.json # 基础设施仪表盘
│       ├── database-dashboard.json      # 数据库仪表盘
│       ├── messaging-dashboard.json     # 消息系统仪表盘
│       └── nebula-dashboard.json        # Nebula Graph 仪表盘
└── README.md                    # 本文件
```

## 配置

### Prometheus

Prometheus 配置在 `prometheus.yml` 文件中定义。它包括以下抓取配置：

1. **prometheus**: 从 Prometheus 自身抓取指标。
2. **nta-services**: 从所有 NTA 平台服务抓取指标。
3. **kafka**: 从 Kafka 抓取指标。
4. **elasticsearch**: 从 Elasticsearch 抓取指标。
5. **redis**: 从 Redis 抓取指标。
6. **mysql**: 从 MySQL 抓取指标。
7. **nebula**: 从 Nebula Graph 抓取指标。
8. **node-exporter**: 从主机抓取系统指标。

### AlertManager

AlertManager 配置在 `alertmanager.yml` 文件中定义。它包括以下配置：

1. **global**: AlertManager 的全局配置。
2. **route**: 告警路由配置。
3. **receivers**: 告警接收器配置（例如，电子邮件）。
4. **inhibit_rules**: 告警抑制规则。

### 告警规则

告警规则在 `rules/alerts.yml` 文件中定义。它们包括以下告警组：

1. **nta.system.rules**: 系统级告警规则（例如，CPU、内存、磁盘使用率）。
2. **nta.services.rules**: 服务级告警规则（例如，服务可用性、错误率、响应时间）。

### Grafana

Grafana 配置包括以下内容：

1. **datasources**: Prometheus 数据源配置。
2. **dashboards**: 仪表盘配置管理和以下仪表盘：
   - **NTA 服务仪表盘**: 提供所有 NTA 平台服务的指标。
   - **基础设施仪表盘**: 提供系统级指标（CPU、内存、磁盘、网络）。
   - **数据库仪表盘**: 提供 MySQL、Redis 和 Elasticsearch 的指标。
   - **消息系统仪表盘**: 提供 Kafka 的指标。
   - **Nebula Graph 仪表盘**: 提供 Nebula Graph 数据库的指标。

## 使用方法

### 启动监控堆栈

监控堆栈包含在 `docker-compose.yml` 文件中，当你运行以下命令时将自动启动：

```bash
docker-compose up -d
```

### 访问 Prometheus

你可以通过 [http://localhost:9090](http://localhost:9090) 访问 Prometheus。

### 访问 Grafana

你可以通过 [http://localhost:3000](http://localhost:3000) 访问 Grafana。默认用户名和密码在 `docker-compose.yml` 文件中作为环境变量定义：

- 用户名: `${GRAFANA_ADMIN_USER:-admin}`
- 密码: `${GRAFANA_ADMIN_PASSWORD:-admin}`

### 访问 AlertManager

你可以通过 [http://localhost:9093](http://localhost:9093) 访问 AlertManager。

### 访问 cAdvisor

你可以通过 [http://localhost:8080](http://localhost:8080) 访问 cAdvisor 查看容器指标和性能数据。

## 自定义

### 添加新的告警规则

要添加新的告警规则，请将它们添加到 `rules/alerts.yml` 文件中，或在 `rules` 目录中创建一个新文件，并将其添加到 `prometheus.yml` 文件的 `rule_files` 部分。

### 添加新的仪表盘

要添加新的仪表盘，请将它们添加到 `grafana/dashboards` 目录中，它们将被 Grafana 自动加载。

### 自定义 AlertManager

要自定义 AlertManager，请编辑 `alertmanager.yml` 文件。例如，你可以更改电子邮件配置或添加新的接收器。

### 配置导出器

以下导出器用于从各种组件收集指标：

1. **Node Exporter**: 收集主机系统指标（CPU、内存、磁盘、网络）。
2. **Redis Exporter**: 收集 Redis 指标（内存使用、命令、客户端）。
3. **MySQL Exporter**: 收集 MySQL 指标（查询、连接、缓冲池）。
4. **Elasticsearch Exporter**: 收集 Elasticsearch 指标（JVM、索引、集群健康状况）。
5. **Kafka Exporter**: 收集 Kafka 指标（主题、分区、消息）。
6. **Nebula Exporter**: 收集 Nebula Graph 指标（查询、顶点、边）。
7. **cAdvisor**: 收集容器指标（CPU、内存、网络）。

要自定义导出器，请编辑 `docker-compose.yml` 文件中它们各自的部分。

## 故障排除

### Prometheus 没有抓取指标

检查 Prometheus 日志：

```bash
docker-compose logs prometheus
```

### Grafana 没有显示仪表盘

检查 Grafana 日志：

```bash
docker-compose logs grafana
```

### AlertManager 没有发送告警

检查 AlertManager 日志：

```bash
docker-compose logs alertmanager
```
