groups:
- name: nta.system.rules
  rules:
  - alert: HighCPUUsage
    expr: 100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High CPU usage detected"
      description: "CPU usage is above 80% for 5 minutes on {{ $labels.instance }}"
  
  - alert: CriticalCPUUsage
    expr: 100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 90
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "Critical CPU usage detected"
      description: "CPU usage is above 90% for 5 minutes on {{ $labels.instance }}"
  
  - alert: HighMemoryUsage
    expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High memory usage detected"
      description: "Memory usage is above 80% for 5 minutes on {{ $labels.instance }}"
  
  - alert: CriticalMemoryUsage
    expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 90
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "Critical memory usage detected"
      description: "Memory usage is above 90% for 5 minutes on {{ $labels.instance }}"
  
  - alert: HighDiskUsage
    expr: 100 - ((node_filesystem_avail_bytes{mountpoint="/"} * 100) / node_filesystem_size_bytes{mountpoint="/"}) > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High disk usage detected"
      description: "Disk usage is above 80% for 5 minutes on {{ $labels.instance }}"
  
  - alert: CriticalDiskUsage
    expr: 100 - ((node_filesystem_avail_bytes{mountpoint="/"} * 100) / node_filesystem_size_bytes{mountpoint="/"}) > 90
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "Critical disk usage detected"
      description: "Disk usage is above 90% for 5 minutes on {{ $labels.instance }}"

- name: nta.services.rules
  rules:
  - alert: ServiceDown
    expr: up{job=~".*nta.*"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Service {{ $labels.job }} is down"
      description: "Service {{ $labels.job }} has been down for more than 1 minute."
  
  - alert: HighErrorRate
    expr: sum(rate(http_server_requests_seconds_count{status=~"5.."}[5m])) by (service) / sum(rate(http_server_requests_seconds_count[5m])) by (service) * 100 > 5
    for: 1m
    labels:
      severity: warning
    annotations:
      summary: "High error rate detected"
      description: "Service {{ $labels.service }} has a 5xx error rate above 5% for 1 minute."
  
  - alert: CriticalErrorRate
    expr: sum(rate(http_server_requests_seconds_count{status=~"5.."}[5m])) by (service) / sum(rate(http_server_requests_seconds_count[5m])) by (service) * 100 > 10
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Critical error rate detected"
      description: "Service {{ $labels.service }} has a 5xx error rate above 10% for 1 minute."
  
  - alert: SlowResponseTime
    expr: http_server_requests_seconds_sum{quantile="0.95"} / http_server_requests_seconds_count > 1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Slow response time detected"
      description: "Service {{ $labels.service }} has a 95th percentile response time above 1 second for 5 minutes."
  
  - alert: CriticalResponseTime
    expr: http_server_requests_seconds_sum{quantile="0.95"} / http_server_requests_seconds_count > 3
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "Critical response time detected"
      description: "Service {{ $labels.service }} has a 95th percentile response time above 3 seconds for 5 minutes."
