global:
  resolve_timeout: 5m

route:
  group_by: ['alertname', 'job', 'severity']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 12h
  receiver: 'email'
  routes:
  - match:
      severity: critical
    receiver: 'email'
    repeat_interval: 1h

receivers:
- name: 'email'
  email_configs:
  - to: '<EMAIL>'
    from: '<EMAIL>'
    smarthost: 'smtp.example.com:587'
    auth_username: '<EMAIL>'
    auth_password: 'password'
    require_tls: true

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'job']
