global:
  scrape_interval: 15s
  evaluation_interval: 15s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

rule_files:
  - "rules/*.yml"

scrape_configs:
  - job_name: "prometheus"
    static_configs:
      - targets: ["localhost:9090"]

  - job_name: "nta-services"
    metrics_path: /actuator/prometheus
    scrape_interval: 5s
    static_configs:
      - targets:
        - "auth-service:8081"
        - "analysis-service:8082"
        - "graph-service:8083"
        - "search-service:8084"
        - "notification-service:8085"
        - "task-service:8086"
        - "config-service:8087"
        - "system-service:8088"
        - "security-service:8089"

  - job_name: "kafka"
    static_configs:
      - targets:
        - "kafka-exporter:9308"  # Kafka Exporter port

  - job_name: "elasticsearch"
    static_configs:
      - targets:
        - "elasticsearch-exporter:9114"  # Elasticsearch Exporter port

  - job_name: "redis"
    static_configs:
      - targets:
        - "redis-exporter:9121"  # Redis Exporter port

  - job_name: "mysql"
    static_configs:
      - targets:
        - "mysql-exporter:9104"  # MySQL Exporter port

  - job_name: "nebula"
    static_configs:
      - targets:
        - "nebula-exporter:9201"  # Nebula Exporter port

  - job_name: "node-exporter"
    static_configs:
      - targets:
        - "node-exporter:9100"

  - job_name: "cadvisor"
    scrape_interval: 5s
    static_configs:
      - targets:
        - "cadvisor:8080"

  - job_name: "doris"
    scrape_interval: 15s
    static_configs:
      - targets:
        - "doris-exporter:9177"  # Doris Exporter port

  - job_name: "pushgateway"
    honor_labels: true
    static_configs:
      - targets:
        - "pushgateway:9091"
