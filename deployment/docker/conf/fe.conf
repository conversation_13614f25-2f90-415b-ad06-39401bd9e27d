# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

#####################################################################
## FE 配置
#####################################################################

# FE 节点角色，可选：FOLLOWER, OBSERVER, LEADER
role = FOLLOWER

# 元数据目录
meta_dir = /doris/fe/meta

# HTTP 端口
http_port = 8030

# RPC 端口
rpc_port = 9020

# 查询端口
query_port = 9030

# 编辑日志端口
edit_log_port = 9010

# 最小元数据写入副本数
metadata_failure_recovery = true
edit_log_type = bdb
master_sync_policy = SYNC
replica_sync_policy = SYNC
priority_networks = 172.0.0.0/8

# 日志配置
sys_log_level = INFO
sys_log_dir = /doris/fe/log
sys_log_roll_num = 10
sys_log_verbose_modules = org.apache.doris
audit_log_dir = /doris/fe/log
audit_log_modules = slow_query, query
audit_log_roll_num = 10

# 内存限制
jvm_heap_size = -Xmx4096m
