version: '3.8'

services:
  # 基础设施服务开发环境配置
  redis:
    ports:
      - "6379:6379"

  kafka:
    environment:
      # 开发环境下使用 HOST_IP 变量
      - KAFKA_CFG_ADVERTISED_LISTENERS=PLAINTEXT://${HOST_IP:-127.0.0.1}:9092,INTERNAL://kafka:9094

  elasticsearch:
    environment:
      - ELASTICSEARCH_HEAP_SIZE=2g
    ports:
      - "9200:9200"

  mysql:
    ports:
      - "3306:3306"

  nebula-metad:
    ports:
      - "9559:9559"
      - "19559:19559"

  nebula-storaged:
    ports:
      - "9779:9779"
      - "19779:19779"

  nebula-graphd:
    ports:
      - "9669:9669"
      - "19669:19669"

  # 微服务开发环境配置
  auth-service:
    build:
      context: ../../services/auth-service
    environment:
      - SPRING_PROFILES_ACTIVE=dev
    ports:
      - "8081:8081"
    volumes:
      - ../../services/auth-service/src:/app/src
      - ../../services/auth-service/target:/app/target

  analysis-service:
    build:
      context: ../../services/analysis-service
    environment:
      - SPRING_PROFILES_ACTIVE=dev
    ports:
      - "8082:8082"
    volumes:
      - ../../services/analysis-service/src:/app/src
      - ../../services/analysis-service/target:/app/target

  graph-service:
    build:
      context: ../../services/graph-service
    environment:
      - SPRING_PROFILES_ACTIVE=dev
    ports:
      - "8083:8083"
    volumes:
      - ../../services/graph-service/src:/app/src
      - ../../services/graph-service/target:/app/target

  search-service:
    build:
      context: ../../services/search-service
    environment:
      - SPRING_PROFILES_ACTIVE=dev
    ports:
      - "8085:8085"
    volumes:
      - ../../services/search-service/src:/app/src
      - ../../services/search-service/target:/app/target

  notification-service:
    build:
      context: ../../services/notification-service
    environment:
      - SPRING_PROFILES_ACTIVE=dev
    ports:
      - "8086:8086"
    volumes:
      - ../../services/notification-service/src:/app/src
      - ../../services/notification-service/target:/app/target

  task-service:
    build:
      context: ../../services/task-service
    environment:
      - SPRING_PROFILES_ACTIVE=dev
    ports:
      - "8087:8087"
    volumes:
      - ../../services/task-service/src:/app/src
      - ../../services/task-service/target:/app/target

  config-service:
    build:
      context: ../../services/config-service
    environment:
      - SPRING_PROFILES_ACTIVE=dev
    ports:
      - "8088:8088"
    volumes:
      - ../../services/config-service/src:/app/src
      - ../../services/config-service/target:/app/target

  system-service:
    build:
      context: ../../services/system-service
    environment:
      - SPRING_PROFILES_ACTIVE=dev
    ports:
      - "8089:8089"
    volumes:
      - ../../services/system-service/src:/app/src
      - ../../services/system-service/target:/app/target

  security-service:
    build:
      context: ../../services/security-service
    environment:
      - SPRING_PROFILES_ACTIVE=dev
    ports:
      - "8090:8090"
    volumes:
      - ../../services/security-service/src:/app/src
      - ../../services/security-service/target:/app/target

  # 前端开发环境配置
  frontend:
    build:
      context: ../../frontend
    ports:
      - "80:80"
      - "8000:8000"  # 开发服务器端口
    volumes:
      - ../../frontend/src:/app/src
      - ../../frontend/public:/app/public
