# NTA Platform 3.0 部署指南

本目录包含 NTA Platform 3.0 的完整部署配置文件，支持使用 Docker Compose 和 Kubernetes (Helm) 两种方式部署。

## 部署架构

NTA Platform 3.0 采用云原生微服务架构，基于 Kubernetes 和 Istio 服务网格，包含以下关键组件：

### 应用层微服务

| 服务名称 | 端口 | 说明 | 依赖组件 |
|---------|------|------|---------|
| frontend | 80 | Vue.js 前端应用 | Nginx |
| auth-service | 8081 | 认证授权服务 | MySQL, Redis |
| analysis-service | 8082 | 数据分析服务 | MySQL, Elasticsearch |
| graph-service | 8083 | 图谱管理服务 | MySQL, Nebula Graph |
| search-service | 8084 | 搜索服务 | MySQL, Elasticsearch |
| notification-service | 8085 | 通知推送服务 | MySQL, Kafka |
| task-service | 8086 | 任务管理服务 | MySQL |
| config-service | 8087 | 配置管理服务 | MySQL |
| system-service | 8088 | 系统管理服务 | MySQL |
| security-service | 8090 | 安全分析服务 | MySQL |

### Flink 流处理作业

| 作业名称 | 说明 | 依赖组件 |
|---------|------|---------|
| data-warehouse-processor | 数据仓库处理器 | Kafka, Doris |
| certificate-analyzer | 证书分析器 | Kafka, Elasticsearch |
| graph-builder | 图谱构建器 | Kafka, Nebula Graph |
| threat-detector | 威胁检测器 | Kafka, Elasticsearch |

### 基础设施组件

| 组件 | 版本 | 说明 | 用途 |
|------|------|------|------|
| MySQL | 8.0.33 | 关系型数据库 | 业务数据存储 |
| Redis | 7.0.12 | 内存缓存 | 缓存和会话存储 |
| Elasticsearch | 7.17.14 | 搜索引擎 | 全文搜索和日志存储 |
| Nebula Graph | 3.8.0 | 图数据库 | 网络关系图谱存储 |
| Apache Kafka | 3.9.0 | 消息队列 | 流数据传输（KRaft 模式） |
| Apache Doris FE | 2.1.7 | 数据仓库前端 | OLAP 分析和数据仓库管理节点 |
| Apache Doris BE | 2.1.7 | 数据仓库后端 | OLAP 分析和数据仓库存储节点 |
| MinIO | 最新版 | 对象存储 | 文件和对象存储 |
| Nginx | 1.25.3 | Web 服务器 | 反向代理和负载均衡 |

### 运维和监控组件

| 组件 | 版本 | 端口 | 说明 |
|------|------|------|------|
| Prometheus | 2.50.1 | 9090 | 监控指标收集 |
| Grafana | 10.4.0 | 3000 | 监控数据可视化 |
| AlertManager | 0.27.0 | 9093 | 告警管理 |
| Pushgateway | 1.7.0 | 9091 | 指标推送网关 |
| Node Exporter | 1.7.0 | 9100 | 节点监控 |
| Redis Exporter | 1.58.0 | 9121 | Redis 监控 |
| MySQL Exporter | 0.15.1 | 9104 | MySQL 监控 |
| Elasticsearch Exporter | 1.5.0 | 9114 | Elasticsearch 监控 |
| Kafka Exporter | 1.7.0 | 9308 | Kafka 监控 |
| Nebula Exporter | 3.6.0 | 9201 | Nebula Graph 监控 |
| Doris Exporter | 0.5.0 | 9177 | Doris 监控 |
| cAdvisor | 0.47.2 | 8080 | 容器监控 |

### Kubernetes Operators

| Operator | 版本 | 说明 |
|----------|------|------|
| Flink Kubernetes Operator | 1.11.0 | Flink 作业管理 |
| Strimzi Kafka Operator | 0.45.0 | Kafka 集群管理 |
| Elastic Cloud on Kubernetes | 2.16.1 | Elasticsearch 集群管理 |
| Nebula Operator | 1.8.2 | Nebula Graph 集群管理 |
| Doris Operator | 25.4.0 | Doris 集群管理 |
| MinIO Operator | 7.1.1 | MinIO 集群管理 |

## 部署方式

NTA Platform 3.0 支持两种部署方式：

### 1. Docker Compose 部署 (`docker/`)

**适用场景**：
- 开发环境和测试环境
- 小规模部署和快速验证
- 本地开发和调试

**特点**：
- 快速启动，一键部署
- 资源需求相对较低
- 配置简单，易于调试
- 包含完整的监控栈

**详见**：[Docker 部署指南](./docker/README.md)

### 2. Kubernetes (Helm) 部署 (`helm/`)

**适用场景**：
- 生产环境和预生产环境
- 大规模部署和高可用部署
- 需要自动扩缩容和故障恢复

**特点**：
- 云原生架构，支持水平扩展
- 基于 Istio 服务网格，提供流量管理和安全策略
- 集成多种 Kubernetes Operators，自动化运维
- 支持滚动更新和蓝绿部署
- 完整的监控、日志和告警体系

**详见**：[Helm 部署指南](./helm/README.md)

## 目录结构

```bash
deployment/
├── docker/                          # Docker Compose 部署
│   ├── docker-compose.yml           # 生产环境配置
│   ├── docker-compose.dev.yml       # 开发环境配置
│   ├── conf/                        # 配置文件目录
│   │   ├── elasticsearch.yml        # Elasticsearch 配置
│   │   ├── flink-conf.yaml          # Flink 配置
│   │   ├── my_custom.cnf            # MySQL 配置
│   │   ├── nginx.conf               # Nginx 配置
│   │   └── ssl/                     # SSL 证书目录
│   ├── prometheus/                  # 监控配置
│   │   ├── prometheus.yml           # Prometheus 配置
│   │   ├── alertmanager.yml         # AlertManager 配置
│   │   ├── rules/                   # 告警规则
│   │   └── grafana/                 # Grafana 配置和仪表板
│   └── scripts/                     # 部署脚本
│       ├── build-images.sh          # 镜像构建脚本
│       ├── start.sh                 # 启动脚本
│       └── stop.sh                  # 停止脚本
│
├── helm/                            # Helm Chart 部署
│   ├── Chart.yaml                   # Chart 元数据
│   ├── values.yaml                  # 主配置文件
│   ├── values/                      # 分模块配置文件
│   │   ├── values-global.yaml       # 全局配置
│   │   ├── values-services.yaml     # 微服务配置
│   │   ├── values-infrastructure.yaml # 基础设施配置
│   │   ├── values-flink.yaml        # Flink 作业配置
│   │   ├── values-istio.yaml        # Istio 配置
│   │   ├── values-monitoring.yaml   # 监控配置
│   │   └── values-operators.yaml    # Operators 配置
│   ├── templates/                   # Kubernetes 模板
│   │   ├── deployments/             # 微服务部署模板
│   │   ├── services/                # 服务模板
│   │   ├── configmaps/              # 配置映射模板
│   │   ├── secrets/                 # 密钥模板
│   │   ├── jobs/                    # 初始化作业模板
│   │   ├── istio/                   # Istio 配置模板
│   │   ├── monitoring/              # 监控配置模板
│   │   ├── elasticsearch/           # Elasticsearch 集群模板
│   │   ├── nebula/                  # Nebula Graph 集群模板
│   │   ├── doris/                   # Doris 集群模板
│   │   ├── minio/                   # MinIO 集群模板
│   │   └── strimzi/                 # Kafka 集群模板
│   ├── files/                       # 配置文件和脚本
│   │   ├── sql/                     # 数据库初始化脚本
│   │   │   ├── mysql/               # MySQL 初始化脚本
│   │   │   └── doris/               # Doris 初始化脚本
│   │   ├── es-templates/            # Elasticsearch 模板
│   │   ├── es-data/                 # Elasticsearch 初始化数据
│   │   └── csv/                     # CSV 数据文件
│   └── docs/                        # 文档
│       └── features.md              # 功能特性说明
│
└── pom.xml                          # Maven 构建配置
```

## 环境要求

### 硬件要求

#### 最小配置（开发环境）
- **CPU**: 4 核心
- **内存**: 16 GB
- **存储**: 100 GB SSD
- **网络**: 1 Gbps

#### 推荐配置（生产环境）
- **CPU**: 16 核心
- **内存**: 64 GB
- **存储**: 500 GB SSD
- **网络**: 10 Gbps

### 软件要求

#### Docker Compose 部署
- **操作系统**: Linux/macOS/Windows
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **可用端口**: 80, 443, 3000, 3306, 6379, 8030, 8040, 8080, 9030, 9060, 9090-9093, 9100, 9104, 9114, 9121, 9177, 9200, 9201, 9308, 9669, 8081-8090

#### Kubernetes 部署
- **Kubernetes**: 1.19+
- **Helm**: 3.0+
- **kubectl**: 与 Kubernetes 版本兼容
- **存储类**: 支持动态卷供应
- **负载均衡器**: 支持 LoadBalancer 类型服务（可选）

## 快速开始

### Docker Compose 快速部署

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd nta_3.0/deployment/docker
   ```

2. **配置环境变量**
   ```bash
   cp .env.example .env
   # 编辑 .env 文件，设置必要的环境变量
   ```

3. **启动服务**
   ```bash
   # 启动所有服务
   docker-compose up -d

   # 或使用脚本启动
   ./scripts/start.sh

   # 构建并启动（包含镜像构建）
   ./scripts/build-images.sh && ./scripts/start.sh
   ```

4. **验证部署**
   ```bash
   # 检查服务状态
   docker-compose ps

   # 访问前端界面
   open http://localhost
   ```

### Kubernetes 快速部署

1. **添加 Helm 仓库**
   ```bash
   # 添加必要的 Helm 仓库
   helm repo add bitnami https://charts.bitnami.com/bitnami
   helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
   helm repo add grafana https://grafana.github.io/helm-charts
   helm repo add elastic https://helm.elastic.co
   helm repo add strimzi https://strimzi.io/charts/
   helm repo update
   ```

2. **部署 NTA Platform**
   ```bash
   cd nta_3.0/deployment/helm

   # 使用默认配置部署
   helm install nta . -n nta --create-namespace

   # 或使用自定义配置
   helm install nta . -n nta --create-namespace \
     -f values/values-global.yaml \
     -f values/values-infrastructure.yaml
   ```

3. **验证部署**
   ```bash
   # 检查 Pod 状态
   kubectl get pods -n nta

   # 检查服务状态
   kubectl get svc -n nta
   ```

## 配置说明

### 环境变量配置

主要环境变量说明：

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `REGISTRY` | `hb.gs.lan` | Docker 镜像仓库地址 |
| `TAG` | `latest` | 镜像标签 |
| `TZ` | `Asia/Shanghai` | 时区设置 |
| `MYSQL_ROOT_PASSWORD` | `password` | MySQL root 密码 |
| `PERMANENT_DATA_PATH` | `/data/nta` | 持久化数据路径 |
| `REMOVABLE_DATA_PATH` | `/data/nta` | 可移除数据路径 |

### 资源配置

#### 微服务资源配置
```yaml
resources:
  requests:
    memory: "512Mi"
    cpu: "250m"
  limits:
    memory: "2Gi"
    cpu: "1000m"
```

#### Flink 作业资源配置
```yaml
jobManager:
  resource:
    memory: "2048m"
    cpu: 1
taskManager:
  resource:
    memory: "4096m"
    cpu: 2
```

## 维护和故障排除

### 日志查看

#### Docker Compose 环境
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f auth-service

# 查看最近的日志
docker-compose logs --tail=100 analysis-service
```

#### Kubernetes 环境
```bash
# 查看 Pod 日志
kubectl logs -f deployment/nta-auth -n nta

# 查看多个 Pod 日志
kubectl logs -f -l app=auth-service -n nta

# 查看 Flink 作业日志
kubectl logs -f deployment/data-warehouse-processor-jobmanager -n nta
```

### 健康检查

#### 服务健康检查
```bash
# Docker Compose
curl http://localhost:8081/actuator/health

# Kubernetes
kubectl get pods -n nta
kubectl describe pod <pod-name> -n nta
```

#### 基础设施健康检查
```bash
# MySQL
docker-compose exec mysql mysqladmin ping

# Elasticsearch
curl http://localhost:9200/_cluster/health

# Kafka
docker-compose exec kafka kafka-topics.sh --list --bootstrap-server localhost:9094

# Doris FE
curl http://localhost:8030/api/bootstrap

# Doris BE
curl http://localhost:8040/api/health
```

### 常见问题

#### 1. 服务启动失败
**症状**: 容器或 Pod 无法启动
**排查步骤**:
```bash
# 查看详细日志
docker-compose logs <service-name>
kubectl describe pod <pod-name> -n nta

# 检查资源使用
docker stats
kubectl top pods -n nta

# 检查配置
docker-compose config
helm template . --debug
```

#### 2. 数据库连接失败
**症状**: 微服务无法连接数据库
**排查步骤**:
```bash
# 检查数据库状态
docker-compose exec mysql mysqladmin ping
kubectl exec -it mysql-0 -n nta -- mysqladmin ping

# 检查网络连通性
docker-compose exec auth-service ping mysql
kubectl exec -it deployment/nta-auth -n nta -- ping mysql
```

#### 3. Flink 作业失败
**症状**: Flink 作业状态为 FAILED
**排查步骤**:
```bash
# 查看 JobManager 日志
kubectl logs -f deployment/data-warehouse-processor-jobmanager -n nta

# 查看 TaskManager 日志
kubectl logs -f deployment/data-warehouse-processor-taskmanager -n nta

# 检查 Checkpoint 状态
kubectl exec -it deployment/data-warehouse-processor-jobmanager -n nta -- \
  curl http://localhost:8081/jobs
```

#### 4. 性能问题
**症状**: 系统响应缓慢
**优化建议**:
- 增加微服务副本数
- 调整 JVM 堆内存大小
- 优化数据库查询
- 增加 Flink 作业并行度

### 监控和告警

#### Prometheus 指标
访问 Prometheus Web UI：
- Docker Compose: http://localhost:9090
- Kubernetes: kubectl port-forward svc/prometheus 9090:9090 -n nta

#### Grafana 仪表板
访问 Grafana Web UI：
- Docker Compose: http://localhost:3000 (admin/admin)
- Kubernetes: kubectl port-forward svc/grafana 3000:3000 -n nta

#### 关键监控指标
- **服务可用性**: up 指标
- **响应时间**: http_request_duration_seconds
- **错误率**: http_requests_total{status=~"5.."}
- **JVM 内存**: jvm_memory_used_bytes
- **数据库连接**: hikaricp_connections_active

## 备份和恢复

### 数据备份

#### MySQL 备份
```bash
# Docker Compose
docker-compose exec mysql mysqldump -u root -p nta > backup.sql

# Kubernetes
kubectl exec -it mysql-0 -n nta -- mysqldump -u root -p nta > backup.sql
```

#### Elasticsearch 备份
```bash
# 创建快照仓库
curl -X PUT "localhost:9200/_snapshot/backup_repo" -H 'Content-Type: application/json' -d'
{
  "type": "fs",
  "settings": {
    "location": "/backup"
  }
}'

# 创建快照
curl -X PUT "localhost:9200/_snapshot/backup_repo/snapshot_1"
```

#### Nebula Graph 备份
```bash
# 使用 nebula-br 工具进行备份
kubectl exec -it nebula-metad-0 -n nta -- \
  nebula-br backup --meta=nebula-metad:9559 --storage=nebula-storaged:9779
```

#### Doris 备份
```bash
# 创建备份
mysql -h localhost -P 9030 -u root -e "
BACKUP SNAPSHOT nta.snapshot_$(date +%Y%m%d_%H%M%S)
TO 's3://backup-bucket/doris/'
PROPERTIES ('type' = 'full');
"

# 查看备份状态
mysql -h localhost -P 9030 -u root -e "SHOW BACKUP;"
```

### 数据恢复

详细的数据恢复步骤请参考各组件的官方文档。

## 版本升级

### 升级前准备

1. **备份数据**
   ```bash
   # 执行完整数据备份
   ./scripts/backup.sh
   ```

2. **检查兼容性**
   - 查看版本更新日志
   - 确认配置文件变更
   - 验证依赖组件版本

### 执行升级

#### Docker Compose 升级
```bash
# 拉取新镜像
docker-compose pull

# 重启服务
docker-compose up -d
```

#### Kubernetes 升级
```bash
# 更新 Helm Chart
helm upgrade nta . -n nta

# 滚动重启服务
kubectl rollout restart deployment/nta-auth -n nta
```

### 升级后验证

1. **检查服务状态**
2. **验证数据完整性**
3. **执行功能测试**
4. **监控系统性能**

## 安全配置

### 网络安全
- 使用 HTTPS 加密通信
- 配置防火墙规则
- 启用 Istio mTLS（Kubernetes 部署）

### 认证和授权
- 配置强密码策略
- 启用多因素认证
- 定期轮换密钥和证书

### 数据安全
- 加密敏感数据存储
- 配置数据库访问控制
- 定期安全审计

## 性能调优

### JVM 调优
```bash
# 推荐的 JVM 参数
-Xms2g -Xmx4g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+HeapDumpOnOutOfMemoryError
```

### 数据库调优
```sql
-- MySQL 配置优化
SET GLOBAL innodb_buffer_pool_size = 8G;
SET GLOBAL max_connections = 1000;
```

### Flink 调优
```yaml
# 并行度配置
parallelism.default: 4
taskmanager.numberOfTaskSlots: 2

# 状态后端配置
state.backend: rocksdb
state.checkpoints.dir: s3://checkpoints/
```

## 联系方式

- **技术支持**: <EMAIL>
- **问题反馈**: 请通过 GitHub Issues 提交
- **文档更新**: 请提交 Pull Request

---

**注意**: 本部署指南基于 NTA Platform 3.0 版本编写，不同版本可能存在差异。部署前请确认版本兼容性。
