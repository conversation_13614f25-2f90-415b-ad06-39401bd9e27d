# values-monitoring.yaml - 监控配置文件
# 包含Prometheus和Grafana的配置信息

# 监控配置
# 控制 Prometheus 和 Grafana 的部署和配置
# 由 features.monitoring.enabled 控制
monitoring:
  # Prometheus Stack 配置
  prometheus-stack:
    # 服务配置
    prometheus:
      prometheusSpec:
        retention: 15d
        resources:
          requests:
            memory: "512Mi"
            cpu: "300m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        storageSpec:
          volumeClaimTemplate:
            spec:
              storageClassName: standard
              accessModes: ["ReadWriteOnce"]
              resources:
                requests:
                  storage: 20Gi

    # AlertManager 配置
    alertmanager:
      alertmanagerSpec:
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        storage:
          volumeClaimTemplate:
            spec:
              storageClassName: standard
              accessModes: ["ReadWriteOnce"]
              resources:
                requests:
                  storage: 5Gi

    # ServiceMonitor 配置
    serviceMonitorSelectorNilUsesHelmValues: false
    serviceMonitorSelector: {}
    serviceMonitorNamespaceSelector: {}

    # 告警规则配置
    defaultRules:
      create: true
      rules:
        alertmanager: true
        etcd: true
        general: true
        k8s: true
        kubeApiserver: true
        kubePrometheusNodeAlerting: true
        kubePrometheusNodeRecording: true
        kubernetesAbsent: true
        kubernetesApps: true
        kubernetesResources: true
        kubernetesStorage: true
        kubernetesSystem: true
        node: true
        prometheus: true
        time: true

  # Grafana 配置
  grafana:
    # 管理员账号配置
    adminUser: admin
    adminPassword: "admin"
    # 持久化配置
    persistence:
      enabled: true
      size: "5Gi"
      storageClassName: "standard"
    # 资源配置
    resources:
      requests:
        memory: "128Mi"
        cpu: "100m"
      limits:
        memory: "256Mi"
        cpu: "200m"
    # 仪表盘配置
    dashboardProviders:
      dashboardproviders.yaml:
        apiVersion: 1
        providers:
        - name: 'default'
          orgId: 1
          folder: ''
          type: file
          disableDeletion: false
          editable: true
          options:
            path: /var/lib/grafana/dashboards/default
    # 数据源配置
    datasources:
      datasources.yaml:
        apiVersion: 1
        datasources:
        - name: Prometheus
          type: prometheus
          url: http://prometheus-operated:9090
          access: proxy
          isDefault: true
