# values-services.yaml - 服务配置文件
# 包含所有微服务的配置信息

# Service-specific configurations
# Each service can override global settings and has its own specific configuration
services:
  frontend:
    enabled: true
    name: frontend
    port: 80
    replicas: 2
    image:
      repository: nta/frontend
    resources: {}
    # 启用自动缩扩容 - 前端服务需要根据用户访问量自动扩缩容
    autoscaling:
      enabled: true
      minReplicas: 2
      maxReplicas: 10
      metrics:
        cpu: 70
        memory: 70
      # 自定义指标 - 可选，基于请求速率进行扩缩容
      custom:
        - name: http_requests_per_second
          targetValue: 100
      behavior:
        scaleUp:
          stabilizationWindowSeconds: 60  # 快速响应用户访问增长
        scaleDown:
          stabilizationWindowSeconds: 300  # 缓慢缩容，避免频繁波动

    # 前端服务的PDB配置 - 确保至少有50%的Pod可用
    pdb:
      enabled: true
      minAvailable: "50%"  # 确保至少有一半的Pod可用

  auth-service:
    enabled: true
    name: auth-service
    port: 8081
    replicas: 2
    image:
      repository: nta/auth-service
    database: auth_db
    resources: {}
    jwt:
      # 使用环境变量或Secret引用，而不是硬编码
      secret:
        secretName: "auth-jwt-secret"
        secretKey: "jwt-secret"
        value: "${AUTH_JWT_SECRET}"  # 从环境变量获取，部署时需要设置此环境变量

  analysis-service:
    enabled: true
    name: analysis-service
    port: 8082
    replicas: 2
    image:
      repository: nta/analysis-service
    database: analysis_db
    resources: {}
    # 启用自动缩扩容
    autoscaling:
      enabled: true
      minReplicas: 2
      maxReplicas: 6
      metrics:
        cpu: 75
        memory: 80
      behavior:
        scaleUp:
          stabilizationWindowSeconds: 180  # 更快响应流量增长
          podsValue: 2
        scaleDown:
          stabilizationWindowSeconds: 300
    extraEnv:
      - name: KAFKA_CLIENT_USER
        valueFrom:
          secretKeyRef:
            name: kafka-credentials
            key: username
      - name: KAFKA_CLIENT_PASSWORD
        valueFrom:
          secretKeyRef:
            name: kafka-credentials
            key: password

  graph-service:
    name: graph-service
    port: 8083
    replicas: 2
    image:
      repository: nta/graph-service
    database: graph_db
    resources: {}
    extraEnv:
      - name: KAFKA_CLIENT_USER
        valueFrom:
          secretKeyRef:
            name: kafka-credentials
            key: username
      - name: KAFKA_CLIENT_PASSWORD
        valueFrom:
          secretKeyRef:
            name: kafka-credentials
            key: password
      - name: NEBULA_HOSTS
        value: "{{ .Release.Name }}-nebula-graphd-0.{{ .Release.Name }}-nebula-graphd.{{ .Release.Namespace }}.svc:{{ .Values.infrastructure.nebula.graphd.port }}"
      - name: NEBULA_META_HOSTS
        value: "{{ .Release.Name }}-nebula-metad-0.{{ .Release.Name }}-nebula-metad.{{ .Release.Namespace }}.svc:{{ .Values.infrastructure.nebula.metad.port }}"
      - name: NEBULA_STORAGE_HOSTS
        value: "{{ .Release.Name }}-nebula-storaged-0.{{ .Release.Name }}-nebula-storaged.{{ .Release.Namespace }}.svc:{{ .Values.infrastructure.nebula.storaged.port }},{{ .Release.Name }}-nebula-storaged-1.{{ .Release.Name }}-nebula-storaged.{{ .Release.Namespace }}.svc:{{ .Values.infrastructure.nebula.storaged.port }},{{ .Release.Name }}-nebula-storaged-2.{{ .Release.Name }}-nebula-storaged.{{ .Release.Namespace }}.svc:{{ .Values.infrastructure.nebula.storaged.port }}"
      - name: NEBULA_USERNAME
        value: "{{ .Values.infrastructure.nebula.credentials.username }}"
      - name: NEBULA_PASSWORD
        valueFrom:
          secretKeyRef:
            name: nebula-credentials
            key: password
      - name: NEBULA_SPACE
        value: "{{ .Values.infrastructure.nebula.space.name }}"

  search-service:
    enabled: true
    name: search-service
    port: 8084
    replicas: 2
    image:
      repository: nta/search-service
    database: search_db
    resources: {}
    # 启用自动缩扩容 - 搜索服务可能会有突发流量
    autoscaling:
      enabled: true
      minReplicas: 2
      maxReplicas: 8
      metrics:
        cpu: 70
        memory: 75
      behavior:
        scaleUp:
          stabilizationWindowSeconds: 60  # 快速响应搜索流量增长
          podsValue: 2
        scaleDown:
          stabilizationWindowSeconds: 300  # 缓慢缩容
    extraEnv:
      - name: ELASTICSEARCH_HOST
        value: "elasticsearch"
      - name: ELASTICSEARCH_PORT
        value: "9200"

  notification-service:
    enabled: true
    name: notification-service
    port: 8085
    replicas: 2
    image:
      repository: nta/notification-service
    database: notification_db
    resources: {}
    extraEnv:
      - name: KAFKA_CLIENT_USER
        valueFrom:
          secretKeyRef:
            name: kafka-credentials
            key: username
      - name: KAFKA_CLIENT_PASSWORD
        valueFrom:
          secretKeyRef:
            name: kafka-credentials
            key: password

  task-service:
    enabled: true
    name: task-service
    port: 8086
    replicas: 2
    image:
      repository: nta/task-service
    database: task_db
    resources: {}

  config-service:
    enabled: true
    name: config-service
    port: 8087
    replicas: 2
    image:
      repository: nta/config-service
    database: config_db
    resources: {}

  system-service:
    enabled: true
    name: system-service
    port: 8088
    replicas: 2
    image:
      repository: nta/system-service
    database: system_db
    resources: {}

  security-service:
    enabled: true
    name: security-service
    port: 8089
    replicas: 2
    image:
      repository: nta/security-service
    database: security_db
    resources: {}
