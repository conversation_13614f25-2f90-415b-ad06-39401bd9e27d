# values-infrastructure.yaml - 基础设施配置文件
# 包含所有基础设施组件的配置信息

# Infrastructure configurations
# 基础设施配置 - 仅包含连接信息和应用程序需要的配置
infrastructure:
  mysql:
    enabled: true
    # 连接配置 - 应用程序使用
    host: "mysql"
    port: "3306"
    credentials:
      secretName: "mysql-credentials"
      usernameKey: "username"
      passwordKey: "password"
      # 敏感信息处理：使用环境变量或外部密钥管理系统
      # 在部署时，请确保这些环境变量已经设置或使用 --set 参数覆盖
      rootPassword:
        value: "${MYSQL_ROOT_PASSWORD}"

    # 使用 initdbScripts 直接在 MySQL 启动时初始化数据库
    initdbScripts:
      # 创建数据库和表结构
      01-create-databases.sql: |
        CREATE DATABASE IF NOT EXISTS push_database;
        CREATE DATABASE IF NOT EXISTS auth_db;
        CREATE DATABASE IF NOT EXISTS th_analysis;

        USE push_database;
        -- push_database 表结构
        SOURCE /docker-entrypoint-initdb.d/sql/01-push_database.sql;


        USE th_analysis;
        -- th_analysis 表结构
        SOURCE /docker-entrypoint-initdb.d/sql/02-th_analysis.sql;

      # 导入 CSV 数据
      02-import-csv-data.sh: |
        #!/bin/bash
        echo "开始导入 CSV 数据..."

        # CSV 文件目录
        CSV_DIR="/docker-entrypoint-initdb.d/csv"

        # 导入所有表数据
        for csv_file in $CSV_DIR/*.csv; do
          table_name=$(basename "$csv_file" .csv)
          echo "导入 $table_name 表数据..."
          mysql -u$MYSQL_USER -p$MYSQL_PASSWORD -e "LOAD DATA LOCAL INFILE '$csv_file' INTO TABLE th_analysis.$table_name FIELDS TERMINATED BY ',' ENCLOSED BY '\"' LINES TERMINATED BY '\n' IGNORE 1 LINES;"
        done

        echo "CSV 数据导入完成"
    # 主节点配置 - 只保留必要的自定义配置
    primary:
      # 持久化存储
      persistence:
        size: "10Gi"

      # 资源配置
      resources:
        requests:
          memory: "1Gi"
          cpu: "500m"
        limits:
          memory: "2Gi"
          cpu: "1000m"

      # 初始化容器，用于复制 SQL 和 CSV 文件
      initContainers:
        - name: copy-init-files
          image: "{{ .Values.global.registry }}/busybox:1.36"
          command:
            - /bin/sh
            - -c
            - |
              echo "复制 SQL 和 CSV 文件..."
              mkdir -p /bitnami/mysql-init/sql
              mkdir -p /bitnami/mysql-init/csv

              # 复制 MySQL SQL 文件
              cp /helm-files/sql/mysql/*.sql /bitnami/mysql-init/sql/

              # 复制 CSV 文件
              cp /helm-files/csv/*.csv /bitnami/mysql-init/csv/

              echo "文件复制完成"
          volumeMounts:
            - name: helm-files
              mountPath: /helm-files
            - name: mysql-init-files
              mountPath: /bitnami/mysql-init

      # 额外的卷挂载
      extraVolumeMounts:
        - name: mysql-init-files
          mountPath: /docker-entrypoint-initdb.d/sql
          subPath: sql
        - name: mysql-init-files
          mountPath: /docker-entrypoint-initdb.d/csv
          subPath: csv

      # 额外的卷
      extraVolumes:
        - name: helm-files
          hostPath:
            path: {{ .Values.global.helmFilesPath | default "files" }}
        - name: mysql-init-files
          emptyDir: {}

    # 指标监控
    metrics:
      enabled: true
      serviceMonitor:
        enabled: true

  redis:
    enabled: true
    # 连接配置
    host: "redis"
    port: "6379"
    credentials:
      # 敏感信息处理：使用环境变量或外部密钥管理系统
      password:
        secretName: "redis-credentials"
        secretKey: "password"
        value: "${REDIS_PASSWORD}"
    # 数据库配置
    databases:
      - name: default
        db: 0
      - name: alarm
        db: 1
      - name: cert_import
        db: 2
      - name: nebula
        db: 2

  elasticsearch:
    enabled: true
    # 连接配置 - 应用程序使用
    host: "elasticsearch-es-http"  # ECK创建的服务名称
    port: "9200"
    credentials:
      username: "elastic"  # ECK创建的默认用户
      # 敏感信息处理：使用环境变量或外部密钥管理系统
      password:
        secretName: "elasticsearch-es-elastic-user"  # ECK创建的Secret
        secretKey: "elastic"
        value: "${ELASTICSEARCH_PASSWORD}"
    # 索引模板列表 - 用于创建IndexTemplate资源
    templates:
      - name: cert_template
      - name: connect_template
      - name: ssl_template
      - name: http_template
      - name: dns_template
      - name: alarm_template
      - name: es_index_template
      - name: ssh_template
    # 集群基本信息
    cluster:
      name: "elasticsearch"
      version: "7.17.14"

  nebula:
    # Nebula Graph 数据库
    # 通用配置
    credentials:
      username: "root"
      # 敏感信息处理：使用环境变量或外部密钥管理系统
      password:
        secretName: "nebula-credentials"
        secretKey: "password"
        value: "${NEBULA_PASSWORD}"

    # 图空间配置
    space:
      name: "nta_analysis_graph"
      partitionNum: 3
      replicaFactor: 3
      vidType: "FIXED_STRING(64)"

    # 服务端口
    graphd:
      host: "nebula-graphd"
      port: 9669
      replicas: 1
    metad:
      host: "nebula-metad"
      port: 9559
      replicas: 1
    storaged:
      host: "nebula-storaged"
      port: 9779
      replicas: 3

    # 连接池配置
    pool:
      maxConnSize: 1000
      minConnSize: 50
      idleTime: 180000
      timeout: 300000
    sessionSize: 100

    # 批处理配置
    batch:
      size: 100
      interval: 1000

  doris:
    enabled: true
    # 通用配置
    credentials:
      username: "root"
      # 敏感信息处理：使用环境变量或外部密钥管理系统
      password:
        secretName: "doris-credentials"
        secretKey: "password"
        value: "${DORIS_PASSWORD}"

    # DorisCluster配置
    cluster:
      name: "doris-cluster"
      # 启用CN组件
      enabledCn: false
      # 启用Broker组件
      enabledBroker: false

    # Doris初始化作业配置
    initJob:
      image:
        repository: mysql
        tag: 8.0
        pullPolicy: IfNotPresent
      resources:
        requests:
          memory: "256Mi"
          cpu: "100m"
        limits:
          memory: "512Mi"
          cpu: "200m"

    # FE配置
    fe:
      httpPort: 8030
      queryPort: 9030
      rpcPort: 9020
      editLogPort: 9010
      replicas: 3
      image:
        repository: apache/doris
        tag: fe-2.1.7
      resources:
        requests:
          memory: "4Gi"
          cpu: "2000m"
        limits:
          memory: "8Gi"
          cpu: "4000m"
      persistentVolumeClaim:
        metaPersistentVolume:
          storage: "20Gi"
          storageClassName: "standard"
        logsPersistentVolume:
          storage: "10Gi"
          storageClassName: "standard"
      configMap:
        "fe.conf": |
          http_port = 8030
          rpc_port = 9020
          query_port = 9030
          edit_log_port = 9010
          enable_fqdn_mode = true

    # BE配置
    be:
      httpPort: 8040
      bePort: 9060
      heartbeatPort: 9050
      brpcPort: 8060
      replicas: 3
      image:
        repository: apache/doris
        tag: be-2.1.7
      resources:
        requests:
          memory: "8Gi"
          cpu: "4000m"
        limits:
          memory: "16Gi"
          cpu: "8000m"
      persistentVolumeClaim:
        dataPersistentVolume:
          storage: "100Gi"
          storageClassName: "standard"
        logsPersistentVolume:
          storage: "10Gi"
          storageClassName: "standard"
      configMap:
        "be.conf": |
          be_port = 9060
          webserver_port = 8040
          heartbeat_service_port = 9050
          brpc_port = 8060
          storage_root_path = /opt/apache-doris/be/storage

    # CN配置
    cn:
      httpPort: 8040
      bePort: 9060
      heartbeatPort: 9050
      brpcPort: 8060
      replicas: 3
      image:
        repository: apache/doris
        tag: be-2.1.7
      resources:
        requests:
          memory: "8Gi"
          cpu: "4000m"
        limits:
          memory: "16Gi"
          cpu: "8000m"
      persistentVolumeClaim:
        dataPersistentVolume:
          storage: "100Gi"
          storageClassName: "standard"
        logsPersistentVolume:
          storage: "10Gi"
          storageClassName: "standard"
      configMap:
        "be.conf": |
          be_port = 9060
          webserver_port = 8040
          heartbeat_service_port = 9050
          brpc_port = 8060
          storage_root_path = /opt/apache-doris/be/storage
          be_node_role = computation

    # Broker配置
    broker:
      replicas: 1
      image:
        repository: apache/doris
        tag: broker-2.1.7
      resources:
        requests:
          memory: "1Gi"
          cpu: "500m"
        limits:
          memory: "2Gi"
          cpu: "1000m"
      persistentVolumeClaim:
        logsPersistentVolume:
          storage: "10Gi"
          storageClassName: "standard"
      configMap:
        "apache_hdfs_broker.conf": |
          broker_ipc_port = 8000
          client_expire_seconds = 3600

    # 监控配置
    metrics:
      enabled: true
      serviceMonitor:
        enabled: true

  kafka:
    enabled: true
    # 连接配置 - 应用程序使用
    host: "kafka"
    port: "9092"
    # Kafka主题配置 - 包含主题名称和初始化配置
    topics:
      # 主题名称映射 - 用于应用程序引用
      cert: "certfile"
      systemBuiltInCertificates: "certfile_system"
      meta: "meta"
      modelSwitch: "model_switch"
      modelConfig: "model_config"
      # 主题初始化配置 - 用于创建Kafka主题
      definitions:
        - name: "certfile"
          partitions: 1
          replicas: 2
          retention_ms: 604800000  # 7天
          segment_bytes: 1073741824  # 1GB
        - name: "certfile_system"
          partitions: 1
          replicas: 2
        - name: "meta"
          partitions: 1
          replicas: 2
        - name: "model_switch"
          partitions: 1
          replicas: 2
        - name: "model_config"
          partitions: 1
          replicas: 2
          config:
            cleanup.policy: "delete"
            min.insync.replicas: 2
        # 协议元数据主题 - 用于data-warehouse-processor和graph-builder之间传递数据
        - name: "connect-info"
          partitions: 4
          replicas: 2
          retention_ms: 604800000  # 7天
        - name: "http-info"
          partitions: 4
          replicas: 2
          retention_ms: 604800000  # 7天
        - name: "dns-info"
          partitions: 4
          replicas: 2
          retention_ms: 604800000  # 7天
        - name: "ssl-info"
          partitions: 4
          replicas: 2
          retention_ms: 604800000  # 7天
        - name: "ssh-info"
          partitions: 4
          replicas: 2
          retention_ms: 604800000  # 7天
        - name: "rlogin-info"
          partitions: 4
          replicas: 2
          retention_ms: 604800000  # 7天
        - name: "telnet-info"
          partitions: 4
          replicas: 2
          retention_ms: 604800000  # 7天
        - name: "rdp-info"
          partitions: 4
          replicas: 2
          retention_ms: 604800000  # 7天
        - name: "vnc-info"
          partitions: 4
          replicas: 2
          retention_ms: 604800000  # 7天
        - name: "xdmcp-info"
          partitions: 4
          replicas: 2
          retention_ms: 604800000  # 7天
        - name: "ntp-info"
          partitions: 4
          replicas: 2
          retention_ms: 604800000  # 7天
        - name: "icmp-info"
          partitions: 4
          replicas: 2
          retention_ms: 604800000  # 7天
    # 安全配置 - 应用程序使用
    security:
      protocol: "SASL_PLAINTEXT"
      mechanism: "SCRAM-SHA-512"
    # 认证配置
    credentials:
      username: "user"
      # 敏感信息处理：使用环境变量或外部密钥管理系统
      password:
        secretName: "kafka-credentials"
        secretKey: "password"
        value: "${KAFKA_PASSWORD}"
    # 集群基本信息
    cluster:
      name: "kafka"
      version: "3.9.0"

  # MinIO 对象存储配置
  minio:
    enabled: true
    # 连接配置 - 应用程序使用
    host: "minio"
    port: "9000"
    # S3 API 访问凭证
    credentials:
      accessKey: "minioadmin"
      # 敏感信息处理：使用环境变量或外部密钥管理系统
      secretKey:
        secretName: "minio-credentials"
        secretKey: "secretKey"
        value: "${MINIO_SECRET_KEY}"
    # 存储配置
    storage:
      # 存储池配置
      pools:
        - servers: 4                     # MinIO 服务器数量
          volumesPerServer: 4            # 每个服务器的卷数
          size: 100Gi                    # 每个卷的大小
          storageClassName: standard     # 存储类名称
          resources:
            requests:
              memory: 2Gi
              cpu: 1
            limits:
              memory: 4Gi
              cpu: 2
    # 网络配置
    service:
      type: ClusterIP                    # 服务类型 (ClusterIP, LoadBalancer, NodePort)
    # TLS 配置
    certificate:
      requestAutoCert: true              # 自动生成 TLS 证书
    # 控制台配置
    console:
      enabled: true                      # 启用 MinIO 控制台
    # 初始化桶配置
    buckets:
      # Flink 相关存储桶
      - name: flink-checkpoints          # Flink 检查点存储桶
        policy: none
        purge: false
      - name: flink-savepoints           # Flink 保存点存储桶
        policy: none
        purge: false

      # 数据存储桶
      - name: nta-data                   # NTA 通用数据存储桶
        policy: none
        purge: false
      - name: nta-application-data       # 应用服务数据存储桶
        policy: none
        purge: false

      # 证书存储桶
      - name: certificates               # 证书文件存储桶
        policy: none
        purge: false
