# NTA Platform 功能模块配置指南

NTA Platform 支持通过配置文件启用或禁用特定的功能模块。这些功能模块是从业务角度封装的原子模块，包含多个相关的组件。

## 功能模块列表

### 1. 图谱功能 (`features.graph`)

图谱功能提供网络流量的图形化分析能力，展示网络实体之间的关系，帮助分析网络行为和威胁。

**包含组件：**
- Nebula Graph 数据库
- Graph Service 服务
- Graph Builder Flink 作业

**配置方式：**

在 `values.yaml` 中：
```yaml
features:
  graph:
    enabled: true  # 设置为 false 可禁用图谱功能
```

或者使用 Helm 命令行：
```bash
helm install nta . -n nta --set features.graph.enabled=false
```

**禁用图谱功能的影响：**
- Nebula Graph 数据库不会被部署
- Graph Service 服务不会被部署
- Graph Builder Flink 作业不会被部署
- 前端界面中的图谱相关功能将不可用

**资源节省：**
禁用图谱功能可以节省以下资源：
- 内存: 约 8-12 GB
- CPU: 约 4-6 核
- 存储: 约 50-100 GB

### 2. 监控功能 (`features.monitoring`)

监控功能提供系统和服务的健康状态监控、性能指标收集和告警通知能力。

**包含组件：**
- Prometheus 监控系统
- Grafana 可视化仪表盘
- AlertManager 告警管理器
- ServiceMonitor 监控配置

**配置方式：**

在 `values.yaml` 中：
```yaml
features:
  monitoring:
    enabled: true  # 设置为 false 可禁用监控功能
```

或者使用 Helm 命令行：
```bash
helm install nta . -n nta --set features.monitoring.enabled=false
```

**禁用监控功能的影响：**
- Prometheus 和 Grafana 不会被部署
- 服务健康状态和性能指标将不可见
- 系统不会生成告警通知

**资源节省：**
禁用监控功能可以节省以下资源：
- 内存: 约 1-2 GB
- CPU: 约 1-2 核
- 存储: 约 25-30 GB

## 添加新的功能模块

如果需要添加新的功能模块，请按照以下步骤操作：

1. 在 `values.yaml` 的 `features` 部分添加新的功能配置
2. 修改相关组件的条件判断，使其依赖于新的功能开关
3. 更新本文档，添加新功能模块的说明

## 最佳实践

- 在资源受限的环境中，可以禁用不需要的功能模块以节省资源
- 在开发和测试环境中，可以只启用正在开发或测试的功能模块
- 在生产环境中，建议根据实际业务需求配置功能模块
