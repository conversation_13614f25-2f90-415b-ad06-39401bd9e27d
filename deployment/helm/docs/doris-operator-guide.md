# Doris Operator 使用指南

本文档提供了在NTA项目中使用Doris Operator管理Apache Doris集群的详细指南。

## 概述

Doris Operator是Apache Doris官方提供的Kubernetes Operator，用于在Kubernetes环境中部署和管理Apache Doris OLAP数据库集群。在NTA项目中，我们使用Doris Operator 25.4.0来管理Doris集群，实现了声明式配置和自动化的生命周期管理。

我们使用的Apache Doris版本是2.1.7，通过DorisCluster CR的`spec.version: 2.1.7`指定。

## 系统组件

当前系统中的主要组件包括：

- **Doris Operator**：作为Helm依赖引入，负责管理Doris集群
- **DorisCluster CR**：用于声明式定义Doris集群
- **Frontend (FE)**：前端节点，负责查询解析、元数据管理和查询调度
- **Backend (BE)**：后端节点，负责数据存储和查询执行

## 配置说明

在`values.yaml`中，Doris Operator的配置位于`doris-operator`部分：

```yaml
doris-operator:
  enabled: true
  dorisOperator:
    image:
      repository: apache/doris
      tag: operator-latest
      imagePullPolicy: IfNotPresent
    nodeSelector: {}
    nodeAffinity: {}
    enableWebhook: true
```

Doris集群配置位于`infrastructure.doris`部分：

```yaml
infrastructure:
  doris:
    enabled: true
    version: "2.1.7"
    fe:
      replicas: 3
      resources:
        requests:
          memory: "2Gi"
          cpu: "1000m"
        limits:
          memory: "4Gi"
          cpu: "2000m"
      storage:
        size: "20Gi"
      config:
        query_timeout: 300
        max_connections: 4096
    be:
      replicas: 3
      resources:
        requests:
          memory: "4Gi"
          cpu: "2000m"
        limits:
          memory: "8Gi"
          cpu: "4000m"
      storage:
        size: "100Gi"
      config:
        be_port: 9060
        webserver_port: 8040
        heartbeat_service_port: 9050
```

## 常用操作

### 查看Doris集群状态

```bash
kubectl get dorisclusters -n nta
```

### 查看Doris集群详细信息

```bash
kubectl describe doriscluster doris -n nta
```

### 查看Doris Pod

```bash
# 查看所有Doris相关Pod
kubectl get pods -l app.kubernetes.io/component=doris -n nta

# 分别查看FE和BE Pod
kubectl get pods -l app.kubernetes.io/component=fe -n nta
kubectl get pods -l app.kubernetes.io/component=be -n nta
```

### 查看Doris服务

```bash
kubectl get svc -l app.kubernetes.io/component=doris -n nta
```

### 连接到Doris

```bash
# 端口转发到FE服务
kubectl port-forward svc/doris-fe 9030:9030 -n nta

# 使用MySQL客户端连接（在另一个终端）
mysql -h 127.0.0.1 -P 9030 -u root
```

### 查看Doris日志

```bash
# 查看FE日志
kubectl logs doris-fe-0 -n nta

# 查看BE日志
kubectl logs doris-be-0 -n nta
```

### 扩展Doris集群

```bash
# 扩展BE节点
kubectl patch doriscluster doris -n nta --type=merge -p '{"spec":{"beSpec":{"replicas":5}}}'

# 扩展FE节点（注意：FE节点数量应为奇数）
kubectl patch doriscluster doris -n nta --type=merge -p '{"spec":{"feSpec":{"replicas":5}}}'
```

## 数据库和表管理

### 创建数据库

```sql
-- 连接到Doris后执行
CREATE DATABASE nta;
USE nta;
```

### 创建表

```sql
-- 创建ODS层表
CREATE TABLE ods_network_flow (
    id BIGINT,
    src_ip VARCHAR(45),
    dst_ip VARCHAR(45),
    src_port INT,
    dst_port INT,
    protocol VARCHAR(10),
    bytes_sent BIGINT,
    bytes_received BIGINT,
    timestamp DATETIME,
    INDEX idx_timestamp (timestamp) USING INVERTED
) DUPLICATE KEY(id)
DISTRIBUTED BY HASH(src_ip) BUCKETS 32
PROPERTIES (
    "replication_num" = "2",
    "storage_format" = "V2"
);

-- 创建DWD层表
CREATE TABLE dwd_network_connection (
    connection_id VARCHAR(64),
    src_ip VARCHAR(45),
    dst_ip VARCHAR(45),
    protocol VARCHAR(10),
    start_time DATETIME,
    end_time DATETIME,
    total_bytes BIGINT,
    packet_count INT,
    INDEX idx_start_time (start_time) USING INVERTED
) DUPLICATE KEY(connection_id)
DISTRIBUTED BY HASH(connection_id) BUCKETS 32
PROPERTIES (
    "replication_num" = "2"
);
```

### 数据导入

```sql
-- 使用Stream Load导入数据
curl -v --location-trusted -u root: \
    -H "format: json" \
    -H "read_json_by_line: true" \
    -H "label: load_$(date +%Y%m%d_%H%M%S)" \
    -T data.json \
    http://localhost:8030/api/nta/ods_network_flow/_stream_load

-- 使用INSERT INTO导入数据
INSERT INTO ods_network_flow VALUES 
(1, '***********', '***********', 80, 8080, 'TCP', 1024, 2048, '2024-01-01 10:00:00');
```

### 查询数据

```sql
-- 基础查询
SELECT src_ip, dst_ip, COUNT(*) as connection_count
FROM ods_network_flow
WHERE timestamp >= '2024-01-01 00:00:00'
GROUP BY src_ip, dst_ip
ORDER BY connection_count DESC
LIMIT 10;

-- 聚合查询
SELECT 
    DATE_FORMAT(timestamp, '%Y-%m-%d %H:00:00') as hour,
    protocol,
    SUM(bytes_sent + bytes_received) as total_bytes
FROM ods_network_flow
WHERE timestamp >= CURDATE()
GROUP BY hour, protocol
ORDER BY hour, total_bytes DESC;
```

## 监控和故障排除

### 查看Doris Operator日志

```bash
kubectl logs -l app.kubernetes.io/name=doris-operator -n doris-operator-system
```

### 查看集群状态

```sql
-- 查看FE节点状态
SHOW FRONTENDS;

-- 查看BE节点状态
SHOW BACKENDS;

-- 查看集群负载
SHOW PROC '/cluster_balance';
```

### 检查表状态

```sql
-- 查看表信息
SHOW TABLES;

-- 查看表结构
DESC ods_network_flow;

-- 查看表分区信息
SHOW PARTITIONS FROM ods_network_flow;

-- 查看表副本状态
SHOW REPLICA STATUS FROM ods_network_flow;
```

### 常见问题排查

1. **Pod无法启动**
   - 检查PVC是否创建成功
   - 检查资源限制是否合理
   - 查看Pod事件和日志

2. **查询性能问题**
   - 检查表分区策略
   - 优化查询SQL
   - 检查索引使用情况

3. **数据导入失败**
   - 检查数据格式
   - 查看导入日志
   - 检查存储空间

## 备份和恢复

### 创建备份

```sql
-- 创建备份
BACKUP SNAPSHOT nta.snapshot_$(date +%Y%m%d_%H%M%S) 
TO 's3://backup-bucket/doris/' 
PROPERTIES ('type' = 'full');

-- 查看备份状态
SHOW BACKUP;
```

### 恢复数据

```sql
-- 恢复数据
RESTORE SNAPSHOT nta.snapshot_20240101_100000 
FROM 's3://backup-bucket/doris/'
PROPERTIES ('backup_timestamp' = '2024-01-01-10-00-00');

-- 查看恢复状态
SHOW RESTORE;
```

## 性能调优

### FE配置优化

```yaml
spec:
  feSpec:
    config:
      query_timeout: 300
      max_connections: 4096
      qe_max_connection: 1024
      max_query_retry_time: 2
```

### BE配置优化

```yaml
spec:
  beSpec:
    config:
      be_port: 9060
      webserver_port: 8040
      storage_root_path: "/opt/apache-doris/be/storage"
      max_tablet_version_num: 1000
      default_num_rows_per_column_file_block: 1024
```

### 表设计优化

```sql
-- 使用合适的分桶键
DISTRIBUTED BY HASH(user_id) BUCKETS 32

-- 使用分区表
PARTITION BY RANGE(date_col) (
    PARTITION p20240101 VALUES [('2024-01-01'), ('2024-01-02')),
    PARTITION p20240102 VALUES [('2024-01-02'), ('2024-01-03'))
)

-- 使用倒排索引
INDEX idx_keyword (keyword) USING INVERTED
```

## 最佳实践

1. **使用声明式配置**：所有Doris配置都应该在DorisCluster CR中定义
2. **版本控制**：将DorisCluster CR纳入版本控制系统
3. **资源规划**：为Doris设置合理的内存和存储资源
4. **监控**：配置Prometheus和Grafana监控Doris集群
5. **备份策略**：定期备份重要数据
6. **表设计**：合理设计分区和分桶策略

## Doris Operator的主要特性

| 功能 | 描述 |
|------|------|
| 集群管理 | 通过Kubernetes自定义资源进行声明式管理 |
| 配置管理 | 集中在DorisCluster CR中，便于维护 |
| 扩缩容 | 支持动态扩缩容FE和BE节点 |
| 升级策略 | 支持滚动升级和版本管理 |
| 监控集成 | 与Prometheus和Grafana集成 |
| 故障恢复 | 自动故障检测和恢复 |
| 存储管理 | 自动管理持久化存储 |

## 参考文档

- [Doris Operator官方文档](https://doris.apache.org/docs/install/k8s-deploy/install-doris-operator)
- [Apache Doris官方文档](https://doris.apache.org/docs/get-starting/quick-start)
- [Doris Operator GitHub](https://github.com/apache/doris-operator)
- [Apache Doris SQL参考](https://doris.apache.org/docs/sql-manual/sql-reference/sql-statements/Data-Definition-Statements/Create/CREATE-TABLE)
