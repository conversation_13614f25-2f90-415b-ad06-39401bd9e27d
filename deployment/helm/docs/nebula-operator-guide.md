# Nebula Operator 使用指南

本文档提供了在NTA项目中使用Nebula Operator管理Nebula Graph集群的详细指南。

## 概述

Nebula Operator是VEsoft官方提供的Kubernetes Operator，用于在Kubernetes环境中部署和管理Nebula Graph分布式图数据库集群。在NTA项目中，我们使用Nebula Operator 1.8.2来管理Nebula Graph集群，实现了声明式配置和自动化的生命周期管理。

我们使用的Nebula Graph版本是3.8.0，通过NebulaCluster CR的`spec.nebula.version: v3.8.0`指定。

## 系统组件

当前系统中的主要组件包括：

- **Nebula Operator**：作为Helm依赖引入，负责管理Nebula Graph集群
- **NebulaCluster CR**：用于声明式定义Nebula Graph集群
- **Metad**：元数据服务，管理集群元信息
- **Storaged**：存储服务，负责数据存储
- **Graphd**：查询服务，处理图查询请求

## 配置说明

在`values.yaml`中，Nebula Operator的配置位于`nebula-operator`部分：

```yaml
nebula-operator:
  enabled: true
  controllerManager:
    resources:
      requests:
        memory: "256Mi"
        cpu: "200m"
      limits:
        memory: "512Mi"
        cpu: "500m"
  admissionWebhook:
    create: true
    resources:
      requests:
        memory: "128Mi"
        cpu: "100m"
      limits:
        memory: "256Mi"
        cpu: "200m"
  scheduler:
    create: true
    resources:
      requests:
        memory: "128Mi"
        cpu: "100m"
      limits:
        memory: "256Mi"
        cpu: "200m"
```

Nebula Graph集群配置位于`infrastructure.nebula`部分：

```yaml
infrastructure:
  nebula:
    enabled: true
    version: "v3.8.0"
    metad:
      replicas: 3
      resources:
        requests:
          memory: "1Gi"
          cpu: "500m"
        limits:
          memory: "2Gi"
          cpu: "1000m"
      storage:
        size: "10Gi"
    storaged:
      replicas: 3
      resources:
        requests:
          memory: "2Gi"
          cpu: "1000m"
        limits:
          memory: "4Gi"
          cpu: "2000m"
      storage:
        size: "50Gi"
    graphd:
      replicas: 2
      resources:
        requests:
          memory: "1Gi"
          cpu: "500m"
        limits:
          memory: "2Gi"
          cpu: "1000m"
```

## 常用操作

### 查看Nebula集群状态

```bash
kubectl get nebulaclusters -n nta
```

### 查看Nebula集群详细信息

```bash
kubectl describe nebulacluster nebula -n nta
```

### 查看Nebula Pod

```bash
# 查看所有Nebula相关Pod
kubectl get pods -l app.kubernetes.io/cluster=nebula -n nta

# 分别查看各组件Pod
kubectl get pods -l app.kubernetes.io/component=metad -n nta
kubectl get pods -l app.kubernetes.io/component=storaged -n nta
kubectl get pods -l app.kubernetes.io/component=graphd -n nta
```

### 查看Nebula服务

```bash
kubectl get svc -l app.kubernetes.io/cluster=nebula -n nta
```

### 连接到Nebula Graph

```bash
# 端口转发到Graphd服务
kubectl port-forward svc/nebula-graphd 9669:9669 -n nta

# 使用nebula-console连接（在另一个终端）
# 需要先安装nebula-console客户端
nebula-console -addr 127.0.0.1 -port 9669 -u root -p nebula
```

### 查看Nebula日志

```bash
# 查看Metad日志
kubectl logs nebula-metad-0 -n nta

# 查看Storaged日志
kubectl logs nebula-storaged-0 -n nta

# 查看Graphd日志
kubectl logs nebula-graphd-0 -n nta
```

### 扩展Nebula集群

```bash
# 扩展Storaged节点
kubectl patch nebulacluster nebula -n nta --type=merge -p '{"spec":{"storaged":{"replicas":5}}}'

# 扩展Graphd节点
kubectl patch nebulacluster nebula -n nta --type=merge -p '{"spec":{"graphd":{"replicas":3}}}'
```

## 图空间和数据管理

### 创建图空间

```bash
# 连接到Nebula Graph后执行
CREATE SPACE nta_graph(partition_num=10, replica_factor=1, vid_type=FIXED_STRING(64));
USE nta_graph;
```

### 创建标签和边类型

```bash
# 创建顶点标签
CREATE TAG ip(name string, country string, city string);
CREATE TAG domain(name string, tld string);
CREATE TAG url(path string, method string);
CREATE TAG cert(subject string, issuer string, valid_from datetime, valid_to datetime);

# 创建边类型
CREATE EDGE connects(protocol string, port int, timestamp datetime);
CREATE EDGE resolves(timestamp datetime);
CREATE EDGE requests(timestamp datetime, status_code int);
CREATE EDGE uses_cert(timestamp datetime);
```

### 插入数据示例

```bash
# 插入顶点
INSERT VERTEX ip(name, country, city) VALUES "***********":("Server", "China", "Beijing");
INSERT VERTEX domain(name, tld) VALUES "example.com":("example", "com");

# 插入边
INSERT EDGE connects(protocol, port, timestamp) VALUES "***********" -> "***********":("TCP", 80, datetime("2024-01-01T10:00:00"));
```

## 监控和故障排除

### 查看Nebula Operator日志

```bash
kubectl logs -l control-plane=nebula-operator-controller-manager -n nebula-operator-system
```

### 查看集群状态

```bash
# 通过Nebula Console检查集群状态
SHOW HOSTS;
SHOW SPACES;
SHOW STATS;
```

### 检查存储状态

```bash
# 检查存储服务状态
SHOW HOSTS STORAGE;

# 检查分片分布
SHOW PARTS;
```

### 常见问题排查

1. **Pod无法启动**
   - 检查PVC是否创建成功
   - 检查资源限制是否合理
   - 查看Pod事件和日志

2. **集群无法连接**
   - 检查Graphd服务状态
   - 检查网络连接
   - 验证用户名密码

3. **数据写入失败**
   - 检查Storaged服务状态
   - 检查存储空间
   - 查看错误日志

## 备份和恢复

### 创建备份

```bash
# 使用nebula-br工具进行备份
kubectl exec -it nebula-metad-0 -n nta -- \
  nebula-br backup \
  --meta=nebula-metad:9559 \
  --storage=nebula-storaged:9779 \
  --backup_name=backup_$(date +%Y%m%d_%H%M%S)
```

### 恢复数据

```bash
# 恢复数据
kubectl exec -it nebula-metad-0 -n nta -- \
  nebula-br restore \
  --meta=nebula-metad:9559 \
  --storage=nebula-storaged:9779 \
  --backup_name=backup_20240101_100000
```

## 性能调优

### 内存配置

```yaml
spec:
  storaged:
    config:
      "memory_tracker_limit_ratio": "0.8"
      "memory_tracker_untracked_reserved_memory_mb": "50"
  graphd:
    config:
      "session_idle_timeout_secs": "28800"
      "client_idle_timeout_secs": "28800"
```

### 存储配置

```yaml
spec:
  storaged:
    config:
      "rocksdb_db_options": '{"max_subcompactions":4,"max_background_jobs":4}'
      "rocksdb_column_family_options": '{"write_buffer_size":67108864,"max_write_buffer_number":4}'
```

## 最佳实践

1. **使用声明式配置**：所有Nebula配置都应该在NebulaCluster CR中定义
2. **版本控制**：将NebulaCluster CR纳入版本控制系统
3. **资源规划**：为Nebula设置合理的内存和存储资源
4. **监控**：配置Prometheus和Grafana监控Nebula集群
5. **备份策略**：定期备份图数据
6. **分片策略**：合理设置分片数量和副本因子

## Nebula Operator的主要特性

| 功能 | 描述 |
|------|------|
| 集群管理 | 通过Kubernetes自定义资源进行声明式管理 |
| 配置管理 | 集中在NebulaCluster CR中，便于维护 |
| 扩缩容 | 支持动态扩缩容Graphd和Storaged节点 |
| 升级策略 | 支持滚动升级和版本管理 |
| 监控集成 | 与Prometheus和Grafana集成 |
| 故障恢复 | 自动故障检测和恢复 |
| 存储管理 | 自动管理持久化存储 |

## 参考文档

- [Nebula Operator官方文档](https://docs.nebula-graph.io/3.8.0/nebula-operator/)
- [Nebula Operator GitHub](https://github.com/vesoft-inc/nebula-operator)
- [Nebula Graph 3.8.0文档](https://docs.nebula-graph.io/3.8.0/)
- [Nebula Graph查询语言nGQL](https://docs.nebula-graph.io/3.8.0/3.ngql-guide/1.nGQL-overview/1.overview/)
