# MinIO Operator 使用指南

本文档提供了在NTA项目中使用MinIO Operator管理MinIO对象存储集群的详细指南。

## 概述

MinIO Operator是MinIO官方提供的Kubernetes Operator，用于在Kubernetes环境中部署和管理MinIO对象存储集群。在NTA项目中，我们使用MinIO Operator 7.1.1来管理MinIO集群，实现了声明式配置和自动化的生命周期管理。

MinIO提供了与Amazon S3兼容的对象存储服务，用于存储文件、日志、备份和其他非结构化数据。

## 系统组件

当前系统中的主要组件包括：

- **MinIO Operator**：作为Helm依赖引入，负责管理MinIO集群
- **Tenant CR**：用于声明式定义MinIO租户（集群实例）
- **MinIO Server**：对象存储服务器节点
- **MinIO Console**：Web管理界面

## 配置说明

在`values.yaml`中，MinIO Operator的配置位于`minio-operator`部分：

```yaml
minio-operator:
  enabled: true
  resources:
    requests:
      memory: "256Mi"
      cpu: "200m"
    limits:
      memory: "512Mi"
      cpu: "500m"
  nodeSelector: {}
  affinity: {}
  tolerations: []
```

MinIO租户配置位于`infrastructure.minio`部分：

```yaml
infrastructure:
  minio:
    enabled: true
    tenant:
      name: "nta-minio"
      pools:
        - servers: 4
          volumesPerServer: 2
          size: "50Gi"
          storageClass: ""
      resources:
        requests:
          memory: "1Gi"
          cpu: "500m"
        limits:
          memory: "2Gi"
          cpu: "1000m"
      console:
        enabled: true
        replicas: 2
      buckets:
        - name: "nta-data"
          versioning: true
        - name: "nta-logs"
          versioning: false
        - name: "nta-backups"
          versioning: true
```

## 常用操作

### 查看MinIO租户状态

```bash
kubectl get tenants -n nta
```

### 查看MinIO租户详细信息

```bash
kubectl describe tenant nta-minio -n nta
```

### 查看MinIO Pod

```bash
# 查看所有MinIO相关Pod
kubectl get pods -l app=minio -n nta

# 查看MinIO Console Pod
kubectl get pods -l app=console -n nta
```

### 查看MinIO服务

```bash
kubectl get svc -l app=minio -n nta
```

### 获取MinIO访问凭据

```bash
# 获取MinIO root用户密码
kubectl get secret nta-minio-user-1 -n nta -o jsonpath='{.data.CONSOLE_ACCESS_KEY}' | base64 -d
kubectl get secret nta-minio-user-1 -n nta -o jsonpath='{.data.CONSOLE_SECRET_KEY}' | base64 -d
```

### 访问MinIO Console

```bash
# 端口转发到Console服务
kubectl port-forward svc/nta-minio-console 9090:9090 -n nta

# 在浏览器中访问 http://localhost:9090
```

### 使用MinIO客户端

```bash
# 安装mc客户端
wget https://dl.min.io/client/mc/release/linux-amd64/mc
chmod +x mc

# 配置mc客户端
mc alias set nta-minio http://localhost:9000 <ACCESS_KEY> <SECRET_KEY>

# 列出存储桶
mc ls nta-minio

# 创建存储桶
mc mb nta-minio/new-bucket

# 上传文件
mc cp local-file.txt nta-minio/nta-data/

# 下载文件
mc cp nta-minio/nta-data/local-file.txt ./downloaded-file.txt
```

## 存储桶管理

### 创建存储桶

```bash
# 使用mc客户端创建存储桶
mc mb nta-minio/analytics-data

# 设置存储桶策略（公共读取）
mc policy set public nta-minio/analytics-data

# 设置存储桶版本控制
mc version enable nta-minio/analytics-data
```

### 存储桶生命周期管理

```bash
# 创建生命周期配置文件
cat > lifecycle.json << EOF
{
    "Rules": [
        {
            "ID": "DeleteOldVersions",
            "Status": "Enabled",
            "Filter": {
                "Prefix": "logs/"
            },
            "NoncurrentVersionExpiration": {
                "NoncurrentDays": 30
            }
        },
        {
            "ID": "DeleteIncompleteUploads",
            "Status": "Enabled",
            "AbortIncompleteMultipartUpload": {
                "DaysAfterInitiation": 7
            }
        }
    ]
}
EOF

# 应用生命周期配置
mc ilm import nta-minio/nta-logs < lifecycle.json
```

### 存储桶通知

```bash
# 配置Kafka通知
mc event add nta-minio/nta-data arn:minio:sqs::primary:kafka --event put,delete

# 查看通知配置
mc event list nta-minio/nta-data
```

## 监控和故障排除

### 查看MinIO Operator日志

```bash
kubectl logs -l app.kubernetes.io/name=operator -n minio-operator
```

### 查看MinIO服务器日志

```bash
kubectl logs nta-minio-pool-0-0 -n nta
```

### 检查集群状态

```bash
# 使用mc客户端检查集群信息
mc admin info nta-minio

# 检查服务器状态
mc admin service status nta-minio

# 检查磁盘使用情况
mc admin info nta-minio --json | jq '.info.servers[].disks'
```

### 性能测试

```bash
# 使用mc客户端进行性能测试
mc speedtest nta-minio

# 详细性能测试
mc speedtest nta-minio --duration 60s --size 64MB
```

### 常见问题排查

1. **Pod无法启动**
   - 检查PVC是否创建成功
   - 检查资源限制是否合理
   - 查看Pod事件和日志

2. **访问权限问题**
   - 检查访问密钥是否正确
   - 验证存储桶策略
   - 检查网络连接

3. **存储空间问题**
   - 检查磁盘使用情况
   - 清理过期数据
   - 扩展存储容量

## 备份和恢复

### 数据备份

```bash
# 使用mc客户端进行备份
mc mirror nta-minio/nta-data /backup/minio-data/

# 增量备份
mc mirror --overwrite nta-minio/nta-data /backup/minio-data/

# 备份到另一个MinIO实例
mc mirror nta-minio/nta-data backup-minio/nta-data-backup/
```

### 数据恢复

```bash
# 从备份恢复数据
mc mirror /backup/minio-data/ nta-minio/nta-data-restored/

# 从另一个MinIO实例恢复
mc mirror backup-minio/nta-data-backup/ nta-minio/nta-data/
```

### 配置备份

```bash
# 导出MinIO配置
mc admin config export nta-minio > minio-config.json

# 导入MinIO配置
mc admin config import nta-minio < minio-config.json
```

## 安全配置

### 用户和策略管理

```bash
# 创建新用户
mc admin user add nta-minio newuser newpassword

# 创建策略文件
cat > readonly-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:ListBucket"
            ],
            "Resource": [
                "arn:aws:s3:::nta-data/*",
                "arn:aws:s3:::nta-data"
            ]
        }
    ]
}
EOF

# 添加策略
mc admin policy add nta-minio readonly-policy readonly-policy.json

# 为用户分配策略
mc admin policy set nta-minio readonly-policy user=newuser
```

### TLS配置

```yaml
spec:
  requestAutoCert: true
  certConfig:
    commonName: "nta-minio.example.com"
    organizationName: ["GeekSec"]
    dnsNames:
      - "nta-minio.example.com"
      - "*.nta-minio.example.com"
```

## 性能调优

### 存储配置优化

```yaml
spec:
  pools:
  - servers: 4
    volumesPerServer: 4  # 增加每服务器卷数
    size: "100Gi"
    storageClass: "fast-ssd"  # 使用高性能存储类
```

### 资源配置优化

```yaml
spec:
  resources:
    requests:
      memory: "2Gi"
      cpu: "1000m"
    limits:
      memory: "4Gi"
      cpu: "2000m"
```

### 网络优化

```bash
# 配置MinIO服务器参数
mc admin config set nta-minio api requests_max=1600
mc admin config set nta-minio api requests_deadline=10s

# 重启服务使配置生效
mc admin service restart nta-minio
```

## 最佳实践

1. **使用声明式配置**：所有MinIO配置都应该在Tenant CR中定义
2. **版本控制**：将Tenant CR纳入版本控制系统
3. **资源规划**：为MinIO设置合理的内存和存储资源
4. **监控**：配置Prometheus和Grafana监控MinIO集群
5. **备份策略**：定期备份重要数据和配置
6. **安全性**：启用TLS和访问控制

## MinIO Operator的主要特性

| 功能 | 描述 |
|------|------|
| 租户管理 | 通过Kubernetes自定义资源进行声明式管理 |
| 配置管理 | 集中在Tenant CR中，便于维护 |
| 扩缩容 | 支持动态扩缩容存储池 |
| 升级策略 | 支持滚动升级和版本管理 |
| 监控集成 | 与Prometheus和Grafana集成 |
| 安全管理 | 支持TLS、IAM和访问策略 |
| 多租户 | 支持多租户隔离 |

## 参考文档

- [MinIO Operator官方文档](https://min.io/docs/minio/kubernetes/upstream/index.html)
- [MinIO Operator GitHub](https://github.com/minio/operator)
- [MinIO客户端文档](https://min.io/docs/minio/linux/reference/minio-mc.html)
- [MinIO管理员指南](https://min.io/docs/minio/linux/administration/minio-admin.html)
