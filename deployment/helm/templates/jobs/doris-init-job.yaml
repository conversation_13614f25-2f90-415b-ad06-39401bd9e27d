{{- if and .Values.infrastructure.doris.enabled .Values.infrastructure.doris.operator.enabled .Values.initialization.doris.enabled }}
apiVersion: batch/v1
kind: Job
metadata:
  name: doris-init-job
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: doris-init-job
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-weight": "10"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
spec:
  backoffLimit: 5
  template:
    metadata:
      labels:
        app.kubernetes.io/name: doris-init-job
        app.kubernetes.io/instance: {{ .Release.Name }}
    spec:
      restartPolicy: OnFailure
      containers:
      - name: doris-init
        image: {{ .Values.infrastructure.doris.initJob.image.repository }}:{{ .Values.infrastructure.doris.initJob.image.tag }}
        imagePullPolicy: {{ .Values.infrastructure.doris.initJob.image.pullPolicy }}
        command:
        - /bin/bash
        - -c
        - |
          set -e
          echo "Waiting for Doris FE to be ready..."
          until mysql -h{{ .Values.infrastructure.doris.fe.service.name }} -P9030 -u{{ .Values.infrastructure.doris.credentials.username }} -p{{ .Values.infrastructure.doris.credentials.password.value }} -e "SHOW DATABASES;" > /dev/null 2>&1; do
            echo "Doris FE is not ready yet. Waiting..."
            sleep 10
          done
          echo "Doris FE is ready. Initializing database..."
          
          # Execute all SQL scripts in order
          for script in $(ls -1 /scripts/*.sql | sort); do
            echo "Executing $script..."
            mysql -h{{ .Values.infrastructure.doris.fe.service.name }} -P9030 -u{{ .Values.infrastructure.doris.credentials.username }} -p{{ .Values.infrastructure.doris.credentials.password.value }} < $script
            echo "Completed $script"
          done
          
          echo "Doris initialization completed successfully."
        volumeMounts:
        - name: doris-init-scripts
          mountPath: /scripts
        resources:
          {{- toYaml .Values.infrastructure.doris.initJob.resources | nindent 10 }}
      volumes:
      - name: doris-init-scripts
        configMap:
          name: doris-init-scripts
{{- end }}
