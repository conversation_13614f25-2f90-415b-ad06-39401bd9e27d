{{- define "nta.initJob" -}}
{{- $name := .name -}}
{{- $namespace := .namespace -}}
{{- $image := .image -}}
{{- $registry := .registry -}}
{{- $tag := .tag -}}
{{- $fullImage := .fullImage -}}
{{- $resources := .resources -}}
{{- $command := .command -}}
{{- $args := .args -}}
{{- $configMapName := .configMapName -}}
{{- $configMountPath := .configMountPath -}}
{{- $dependencies := .dependencies -}}
{{- $backoffLimit := .backoffLimit | default 3 -}}
{{- $activeDeadlineSeconds := .activeDeadlineSeconds | default 600 -}}
{{- $restartPolicy := .restartPolicy | default "OnFailure" -}}

apiVersion: batch/v1
kind: Job
metadata:
  name: {{ $name }}
  namespace: {{ $namespace }}
  labels:
    {{- include "nta.labels" $ | nindent 4 }}
    app: {{ $name }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
    {{- if .dependsOn }}
    "nta.geeksec.com/depends-on": {{ .dependsOn | join "," | quote }}
    {{- end }}
spec:
  backoffLimit: {{ $backoffLimit }}
  activeDeadlineSeconds: {{ $activeDeadlineSeconds }}
  template:
    metadata:
      labels:
        app: {{ $name }}
        {{- include "nta.selectorLabels" $ | nindent 8 }}
    spec:
      restartPolicy: {{ $restartPolicy }}
      {{- if $dependencies }}
      initContainers:
      {{- range $dependency := $dependencies }}
      - name: wait-for-{{ $dependency.name }}
        image: {{ $registry }}/proxy_cache/busybox:1.36
        command: ['sh', '-c', 'until nc -z {{ $dependency.host }} {{ $dependency.port }}; do echo waiting for {{ $dependency.name }}; sleep 2; done;']
      {{- end }}
      {{- end }}
      containers:
      - name: {{ $name }}
        image: {{ if $fullImage }}{{ $fullImage }}{{ else }}{{ $registry }}/{{ $image.repository }}:{{ $tag }}{{ end }}
        {{- if $command }}
        command:
        {{- range $cmd := $command }}
        - {{ $cmd | quote }}
        {{- end }}
        {{- end }}
        {{- if $args }}
        args:
        {{- range $arg := $args }}
        - {{ $arg | quote }}
        {{- end }}
        {{- end }}
        env:
        - name: TZ
          value: "Asia/Shanghai"
        {{- if .env }}
        {{- range $key, $value := .env }}
        - name: {{ $key }}
          value: {{ $value | quote }}
        {{- end }}
        {{- end }}
        {{- if .envFrom }}
        envFrom:
        {{- range $ref := .envFrom }}
        - {{ $ref.type }}:
            name: {{ $ref.name }}
        {{- end }}
        {{- end }}
        resources:
          {{- toYaml $resources | nindent 10 }}
        {{- if $configMapName }}
        volumeMounts:
        - name: config-volume
          mountPath: {{ $configMountPath }}
        {{- end }}
      {{- if $configMapName }}
      volumes:
      - name: config-volume
        configMap:
          name: {{ $configMapName }}
      {{- end }}
{{- end -}}
