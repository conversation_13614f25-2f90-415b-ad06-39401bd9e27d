{{- if and .Values.infrastructure.flink.enabled .Values.infrastructure.flink.operator.enabled -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: flink
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: flink
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
rules:
  - apiGroups:
      - ""
    resources:
      - pods
      - configmaps
      - services
    verbs:
      - get
      - list
      - watch
      - create
      - delete
      - update
      - patch
  - apiGroups:
      - apps
    resources:
      - deployments
    verbs:
      - get
      - list
      - watch
      - create
      - delete
      - update
      - patch
  - apiGroups:
      - flink.apache.org
    resources:
      - flinkdeployments
      - flinksessionjobs
    verbs:
      - get
      - list
      - watch
      - create
      - delete
      - update
      - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: flink
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: flink
subjects:
  - kind: ServiceAccount
    name: flink
    namespace: {{ .Values.global.namespace }}
{{- end -}}
