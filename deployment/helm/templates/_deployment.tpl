{{- define "nta.deployment" -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .name }}
  namespace: {{ .namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
spec:
  replicas: {{ .replicas }}
  selector:
    matchLabels:
      app: {{ .name }}
      {{- include "nta.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        app: {{ .name }}
        {{- include "nta.selectorLabels" . | nindent 8 }}
    spec:
      containers:
      - name: {{ .name }}
        image: {{ .registry }}/{{ .image.repository }}:{{ .tag }}
        ports:
        - containerPort: {{ .port }}
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: {{ .springProfiles | quote }}
        - name: SPRING_CONFIG_IMPORT
          value: "configtree:/config/"
        - name: MYSQL_USERNAME
          valueFrom:
            secretKeyRef:
              name: {{ include "nta.credentialsSecretName" (dict "type" "mysql" "root" $) }}
              key: {{ include "nta.credentialsUsernameSecretKey" (dict "type" "mysql" "root" $) }}
        - name: MYSQL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ include "nta.credentialsSecretName" (dict "type" "mysql" "root" $) }}
              key: {{ include "nta.credentialsSecretKey" (dict "type" "mysql" "root" $) }}
        {{- if eq .name "auth-service" }}
        - name: JWT_SECRET
          {{- if kindIs "string" .jwt.secret }}
          value: {{ .jwt.secret | quote }}
          {{- else }}
          valueFrom:
            secretKeyRef:
              name: {{ .jwt.secret.secretName }}
              key: {{ .jwt.secret.secretKey }}
          {{- end }}
        {{- end }}
        {{- if .extraEnv }}
        {{- include "nta.updateCredentials" . | nindent 8 }}
        {{- end }}
        resources:
          {{- if .resources }}
          {{- toYaml .resources | nindent 10 }}
          {{- else }}
          {{- toYaml $.Values.global.resources | nindent 10 }}
          {{- end }}
        command: ["java", {{ .javaOpts | quote }}, "-jar", "/app/app.jar"]
        volumeMounts:
        - name: config-volume
          mountPath: /config
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: {{ .port }}
          initialDelaySeconds: 30
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: {{ .port }}
          initialDelaySeconds: 60
          periodSeconds: 15
      volumes:
      - name: config-volume
        configMap:
          name: {{ .name }}-config
{{- end -}}
