{{- if and .Values.infrastructure.elasticsearch.enabled .Values.infrastructure.elasticsearch.operator.enabled -}}
apiVersion: elasticsearch.k8s.elastic.co/v1
kind: Elasticsearch
metadata:
  name: {{ .Values.infrastructure.elasticsearch.cluster.name }}
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
spec:
  version: {{ .Values.infrastructure.elasticsearch.cluster.version }}
  nodeSets:
  {{- range .Values.infrastructure.elasticsearch.cluster.nodeSets }}
  - name: {{ .name }}
    count: {{ .count }}
    config:
      {{- toYaml .config | nindent 6 }}
    podTemplate:
      spec:
        {{- if .podTemplate.spec }}
        {{- toYaml .podTemplate.spec | nindent 8 }}
        {{- end }}
    volumeClaimTemplates:
    {{- range .volumeClaimTemplates }}
    - metadata:
        name: {{ .metadata.name }}
      spec:
        accessModes:
        {{- range .spec.accessModes }}
        - {{ . }}
        {{- end }}
        resources:
          requests:
            storage: {{ .spec.resources.requests.storage }}
        storageClassName: {{ .spec.storageClassName }}
    {{- end }}
  {{- end }}
{{- end -}}
