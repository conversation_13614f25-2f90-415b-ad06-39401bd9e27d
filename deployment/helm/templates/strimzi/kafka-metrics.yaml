{{- if and .Values.infrastructure.kafka.enabled .Values.infrastructure.kafka.operator.enabled .Values.infrastructure.kafka.operator.kafka.metricsConfig.enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: kafka-metrics
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
data:
  kafka-metrics-config.yml: |
    # JMX exporter configuration for Kafka brokers
    lowercaseOutputName: true
    lowercaseOutputLabelNames: true
    rules:
    # Special cases and very specific rules
    - pattern: kafka.server<type=(.+), name=(.+), clientId=(.+), topic=(.+), partition=(.*)><>Value
      name: kafka_server_$1_$2
      type: GAUGE
      labels:
        clientId: "$3"
        topic: "$4"
        partition: "$5"
    - pattern: kafka.server<type=(.+), name=(.+), clientId=(.+), brokerHost=(.+), brokerPort=(.+)><>Value
      name: kafka_server_$1_$2
      type: GAUGE
      labels:
        clientId: "$3"
        broker: "$4:$5"

    # Generic per-topic metrics
    - pattern: kafka.server<type=(.+), name=(.+), topic=(.+)><>Value
      name: kafka_server_$1_$2
      type: GAUGE
      labels:
        topic: "$3"

    # Generic metrics
    - pattern: kafka.server<type=(.+), name=(.+)><>Value
      name: kafka_server_$1_$2
      type: GAUGE

    # Quotas
    - pattern: kafka.server<type=(.+), user=(.+), client-id=(.+)><>Value
      name: kafka_server_$1
      type: GAUGE
      labels:
        user: "$2"
        clientId: "$3"

    # JVM metrics
    - pattern: java.lang<type=(.+), name=(.+)><>Value
      name: java_lang_$1_$2
      type: GAUGE

    # Memory metrics
    - pattern: java.lang<type=Memory><HeapMemoryUsage>(.+)
      name: java_lang_memory_heap_$1
      type: GAUGE
    - pattern: java.lang<type=Memory><NonHeapMemoryUsage>(.+)
      name: java_lang_memory_nonheap_$1
      type: GAUGE

    # GC metrics
    - pattern: java.lang<type=GarbageCollector, name=(.+)><>CollectionCount
      name: java_lang_gc_$1_collection_count
      type: COUNTER
    - pattern: java.lang<type=GarbageCollector, name=(.+)><>CollectionTime
      name: java_lang_gc_$1_collection_time_ms
      type: COUNTER

    # Thread metrics
    - pattern: java.lang<type=Threading><>ThreadCount
      name: java_lang_threading_thread_count
      type: GAUGE
    - pattern: java.lang<type=Threading><>DaemonThreadCount
      name: java_lang_threading_daemon_thread_count
      type: GAUGE
    - pattern: java.lang<type=Threading><>PeakThreadCount
      name: java_lang_threading_peak_thread_count
      type: GAUGE

    # Kafka consumer group metrics
    - pattern: kafka.consumer<type=ConsumerFetchManager, client-id=(.+)><>MaxLag
      name: kafka_consumer_max_lag
      type: GAUGE
      labels:
        client_id: "$1"
    - pattern: kafka.consumer<type=ConsumerFetchManager, client-id=(.+)><>MinFetchRate
      name: kafka_consumer_min_fetch_rate
      type: GAUGE
      labels:
        client_id: "$1"
{{- end -}}
