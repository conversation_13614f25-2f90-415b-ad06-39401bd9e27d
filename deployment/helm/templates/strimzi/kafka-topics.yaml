{{- if and .Values.infrastructure.kafka.enabled .Values.infrastructure.kafka.operator.enabled -}}
{{- range .Values.infrastructure.kafka.topics.definitions }}
---
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  name: {{ .name }}
  namespace: {{ $.Values.global.namespace }}
  labels:
    strimzi.io/cluster: {{ $.Values.infrastructure.kafka.operator.kafka.name }}
    {{- include "nta.labels" $ | nindent 4 }}
spec:
  partitions: {{ .partitions }}
  replicas: {{ $.Values.infrastructure.kafka.operator.kafka.config.default_replication_factor | default 2 }}
  config:
    # 默认配置，可以在values.yaml中的topic定义中覆盖
    retention.ms: {{ .retention_ms | default 604800000 }}  # 默认7天
    segment.bytes: {{ .segment_bytes | default 1073741824 }}  # 默认1GB
    {{- if .config }}
    {{- toYaml .config | nindent 4 }}
    {{- end }}
{{- end }}
{{- end -}}
