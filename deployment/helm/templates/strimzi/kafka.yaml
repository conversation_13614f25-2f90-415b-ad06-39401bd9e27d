{{- if and .Values.infrastructure.kafka.enabled .Values.infrastructure.kafka.operator.enabled -}}
apiVersion: kafka.strimzi.io/v1beta2
kind: Kafka
metadata:
  name: {{ .Values.infrastructure.kafka.operator.kafka.name }}
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
spec:
  kafka:
    version: {{ .Values.infrastructure.kafka.operator.kafka.version }}
    replicas: {{ .Values.infrastructure.kafka.operator.kafka.replicas }}
    listeners:
      {{- range .Values.infrastructure.kafka.operator.kafka.listeners }}
      - name: {{ .name }}
        port: {{ .port }}
        type: {{ .type }}
        tls: {{ .tls }}
        {{- if .authentication }}
        authentication:
          type: {{ .authentication.type }}
        {{- end }}
      {{- end }}
    config:
      {{- range $key, $value := .Values.infrastructure.kafka.operator.kafka.config }}
      {{ $key }}: {{ $value }}
      {{- end }}
    storage:
      type: {{ .Values.infrastructure.kafka.operator.kafka.storage.type }}
      volumes:
      {{- range .Values.infrastructure.kafka.operator.kafka.storage.volumes }}
      - id: {{ .id }}
        type: {{ .type }}
        size: {{ .size }}
        deleteClaim: {{ .deleteClaim }}
      {{- end }}
    resources:
      {{- toYaml .Values.infrastructure.kafka.operator.kafka.resources | nindent 6 }}
    {{- if .Values.infrastructure.kafka.operator.kafka.metricsConfig.enabled }}
    metricsConfig:
      type: jmxPrometheusExporter
      valueFrom:
        configMapKeyRef:
          name: kafka-metrics
          key: kafka-metrics-config.yml
    {{- end }}
  # Use KRaft mode (no ZooKeeper)
  # For Strimzi 0.40.0+, KRaft mode is the default
  # No need to specify ZooKeeper section
{{- end -}}
