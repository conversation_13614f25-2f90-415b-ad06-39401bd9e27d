{{- if and .Values.infrastructure.doris.enabled .Values.infrastructure.doris.operator.enabled .Values.initialization.doris.enabled }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ .Release.Name }}-doris-init
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: doris-init
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": hook-succeeded
spec:
  template:
    metadata:
      labels:
        app.kubernetes.io/name: doris-init
        app.kubernetes.io/instance: {{ .Release.Name }}
    spec:
      restartPolicy: OnFailure
      volumes:
        - name: sql-scripts
          configMap:
            name: doris-init-scripts
      containers:
        - name: doris-init
          image: mysql:8.0
          command:
            - /bin/bash
            - -c
            - |
              echo "Waiting for <PERSON> FE to be ready..."
              until mysql -h {{ .Values.infrastructure.doris.cluster.name }}-fe.{{ .Release.Namespace }}.svc -P {{ .Values.infrastructure.doris.fe.queryPort }} -u {{ .Values.infrastructure.doris.credentials.username }} -p{{ .Values.infrastructure.doris.credentials.password.value }} -e "SELECT 1" > /dev/null 2>&1; do
                echo "Doris FE is not ready yet. Waiting..."
                sleep 10
              done
              echo "Doris FE is ready. Initializing..."
              # 按文件名排序执行所有SQL脚本
              for sql_file in $(find /sql-scripts -name "[0-9]*.sql" | sort); do
                if [ -f "$sql_file" ]; then
                  echo "Executing $sql_file..."
                  mysql -h {{ .Values.infrastructure.doris.cluster.name }}-fe.{{ .Release.Namespace }}.svc -P {{ .Values.infrastructure.doris.fe.queryPort }} -u {{ .Values.infrastructure.doris.credentials.username }} -p{{ .Values.infrastructure.doris.credentials.password.value }} < "$sql_file"
                fi
              done
              echo "Doris initialization completed."
          volumeMounts:
            - name: sql-scripts
              mountPath: /sql-scripts
          resources:
            {{- toYaml .Values.initialization.resources | nindent 12 }}
      {{- with .Values.initialization.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
