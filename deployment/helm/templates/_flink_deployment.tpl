{{- define "nta.flinkDeployment" -}}
{{- $name := .name -}}
{{- $namespace := .namespace -}}
{{- $image := .image -}}
{{- $registry := .registry -}}
{{- $tag := .tag -}}
{{- $className := .className -}}
{{- $parallelism := .parallelism -}}
{{- $resources := .resources -}}
{{- $savepointDir := "/opt/flink/savepoints" -}}
{{- $checkpointDir := "/opt/flink/checkpoints" -}}

apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  name: {{ $name }}
  namespace: {{ $namespace }}
  labels:
    {{- include "nta.labels" $ | nindent 4 }}
    app: {{ $name }}
spec:
  image: {{ $registry }}/{{ $image.repository }}:{{ $tag }}
  flinkVersion: v1_20
  serviceAccount: flink
  mode: native
  jobManager:
    replicas: 1
    resource:
      memory: "{{ $resources.requests.memory }}"
      cpu: {{ $resources.requests.cpu }}
  taskManager:
    resource:
      memory: "{{ $resources.limits.memory }}"
      cpu: {{ $resources.limits.cpu }}
  flinkConfiguration:
    # 基本配置
    taskmanager.numberOfTaskSlots: "{{ $parallelism }}"

    # 存储路径
    state.savepoints.dir: "{{ $savepointDir }}/{{ $name }}"
    state.checkpoints.dir: "{{ $checkpointDir }}/{{ $name }}"

    # Checkpoint配置
    execution.checkpointing.interval: "{{ $.Values.infrastructure.flink.configuration.checkpoint.interval }}"
    execution.checkpointing.mode: "{{ $.Values.infrastructure.flink.configuration.checkpoint.mode }}"
    execution.checkpointing.min-pause-between-checkpoints: "{{ $.Values.infrastructure.flink.configuration.checkpoint.min-pause }}"
    execution.checkpointing.tolerable-failed-checkpoints: "{{ $.Values.infrastructure.flink.configuration.checkpoint.tolerable-failed-checkpoints }}"
    execution.checkpointing.externalized-checkpoint-retention: "{{ $.Values.infrastructure.flink.configuration.checkpoint.externalized-checkpoint-retention }}"
    execution.checkpointing.unaligned.enabled: "{{ $.Values.infrastructure.flink.configuration.checkpoint.unaligned.enabled }}"

    # 重启策略
    restart-strategy: "{{ $.Values.infrastructure.flink.configuration.restart.strategy }}"
    restart-strategy.fixed-delay.attempts: "{{ $.Values.infrastructure.flink.configuration.restart.attempts }}"
    restart-strategy.fixed-delay.delay: "{{ $.Values.infrastructure.flink.configuration.restart.delay }}"
    # 允许失败重试
    execution.failover-strategy: "region"
    # 允许任务失败重试
    restart-strategy.failure-rate.max-failures-per-interval: "3"
    restart-strategy.failure-rate.failure-rate-interval: "5min"
    restart-strategy.failure-rate.delay: "10s"

    # Kubernetes配置
    kubernetes.container.image.pull-policy: "{{ $.Values.infrastructure.flink.configuration.kubernetes.imagePullPolicy }}"

    # 高可用性配置
    {{- if $.Values.infrastructure.flink.configuration.highAvailability.enabled }}
    high-availability: "{{ $.Values.infrastructure.flink.configuration.highAvailability.type }}"
    high-availability.storageDir: "{{ $checkpointDir }}/{{ $name }}/ha"
    {{- end }}

    # Savepoint配置
    kubernetes.operator.periodic.savepoint.interval: "{{ $.Values.infrastructure.flink.configuration.savepoint.interval }}"
    kubernetes.operator.savepoint.history.max.count: "{{ $.Values.infrastructure.flink.configuration.savepoint.history.maxCount }}"
    kubernetes.operator.savepoint.history.max.age: "{{ $.Values.infrastructure.flink.configuration.savepoint.history.maxAge }}"
    kubernetes.operator.savepoint.cleanup.enabled: "{{ $.Values.infrastructure.flink.configuration.savepoint.cleanup.enabled }}"
    kubernetes.operator.savepoint.dispose-on-delete: "{{ $.Values.infrastructure.flink.configuration.savepoint.disposeOnDelete }}"

    # 资源清理策略
    kubernetes.operator.job.cleanup.enabled: "true"
    kubernetes.operator.job.cleanup.on-cancel: "true"
    kubernetes.operator.job.cleanup.on-success: "true"
    kubernetes.operator.job.cleanup.on-suspended: "false"
    kubernetes.operator.job.cleanup.on-application-error: "false"
    kubernetes.operator.job.cleanup.on-startup-failure: "false"

    # Flink Operator 高级配置
    kubernetes.operator.job.upgrade.inplace-scaling.enabled: "{{ $.Values.infrastructure.flink.configuration.kubernetes.operator.jobUpgradeInplaceScalingEnabled }}"
    kubernetes.operator.cluster.health-check.enabled: "{{ $.Values.infrastructure.flink.configuration.kubernetes.operator.clusterHealthCheckEnabled }}"
    kubernetes.operator.job.upgrade.last-state-fallback.enabled: "{{ $.Values.infrastructure.flink.configuration.kubernetes.operator.jobUpgradeLastStateFallbackEnabled }}"

    # Java 17+ 支持
    env.java.opts.all: "{{ $.Values.infrastructure.flink.configuration.javaOptions }}"

    # 日志配置
    log.file: "/opt/flink/log/flink-jobmanager.log"
    log.file.taskmanager: "/opt/flink/log/flink-taskmanager.log"
    log.level.root: "INFO"
    log.level.org.apache.flink: "INFO"
    log.level.org.apache.kafka: "WARN"
    log.level.org.apache.zookeeper: "WARN"
    log.level.akka: "WARN"
    log.level.org.jboss.netty: "WARN"
    log.level.org.apache.hadoop: "WARN"
    log.level.org.apache.flink.kubernetes: "INFO"

    {{- if eq $name "data-warehouse-processor" }}
    pipeline.serialization.kryo.registrations.ZMPNMsg: "com.geeksec.proto.ZMPNMsg:com.twitter.chill.protobuf.ProtobufSerializer"
    {{- end }}

    {{- if eq $name "graph-builder" }}
    # 序列化配置
    pipeline.serialization-config: "[com.geeksec.nebulaEntity.vertex.LabelTagInfo: {type: kryo}, com.geeksec.nebulaEntity.vertex.TaskTagInfo: {type: kryo}, com.geeksec.nebulaEntity.edge.BaseEdge: {type: kryo}, com.geeksec.nebulaEntity.vertex.BaseVertex: {type: kryo}]"
    # 启用对象重用
    pipeline.object-reuse: "true"
    {{- end }}
  job:
    jarURI: local:///opt/flink/usrlib/{{ $name }}.jar
    parallelism: {{ $parallelism }}
    entryClass: {{ $className }}

  # 升级模式和参数
  upgradeMode: savepoint
  args: []
  podTemplate:
    spec:
      volumes:
        - name: config-volume
          configMap:
            name: {{ $name }}-config
        - name: flink-data
          persistentVolumeClaim:
            claimName: flink-data-pvc
      containers:
        - name: flink-main-container
          env:
            - name: TZ
              value: "{{ $.Values.global.timezone }}"
            - name: CONFIG_PATH
              value: "/opt/flink/usrlib/classes/config.properties"
            # Kafka凭据
            - name: KAFKA_CLIENT_USER
              valueFrom:
                secretKeyRef:
                  name: {{ include "nta.credentialsSecretName" (dict "type" "kafka" "root" $) }}
                  key: {{ include "nta.credentialsUsernameSecretKey" (dict "type" "kafka" "root" $) }}
            - name: KAFKA_CLIENT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "nta.credentialsSecretName" (dict "type" "kafka" "root" $) }}
                  key: {{ include "nta.credentialsSecretKey" (dict "type" "kafka" "root" $) }}
            - name: KAFKA_SECURITY_PROTOCOL
              value: "{{ $.Values.infrastructure.kafka.security.protocol }}"
            - name: KAFKA_SASL_MECHANISM
              value: "{{ $.Values.infrastructure.kafka.security.mechanism }}"

            # MySQL凭据
            - name: MYSQL_USERNAME
              valueFrom:
                secretKeyRef:
                  name: {{ include "nta.credentialsSecretName" (dict "type" "mysql" "root" $) }}
                  key: {{ include "nta.credentialsUsernameSecretKey" (dict "type" "mysql" "root" $) }}
            - name: MYSQL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "nta.credentialsSecretName" (dict "type" "mysql" "root" $) }}
                  key: {{ include "nta.credentialsSecretKey" (dict "type" "mysql" "root" $) }}

            # Doris凭据
            - name: DORIS_USERNAME
              valueFrom:
                secretKeyRef:
                  name: {{ include "nta.credentialsSecretName" (dict "type" "doris" "root" $) }}
                  key: {{ include "nta.credentialsUsernameSecretKey" (dict "type" "doris" "root" $) }}
            - name: DORIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "nta.credentialsSecretName" (dict "type" "doris" "root" $) }}
                  key: {{ include "nta.credentialsSecretKey" (dict "type" "doris" "root" $) }}

            # MinIO凭据
            - name: MINIO_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "nta.credentialsSecretName" (dict "type" "minio" "root" $) }}
                  key: {{ include "nta.credentialsUsernameSecretKey" (dict "type" "minio" "root" $) }}
            - name: MINIO_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "nta.credentialsSecretName" (dict "type" "minio" "root" $) }}
                  key: {{ include "nta.credentialsSecretKey" (dict "type" "minio" "root" $) }}

            # Elasticsearch凭据
            - name: ELASTICSEARCH_USERNAME
              valueFrom:
                secretKeyRef:
                  name: {{ include "nta.credentialsSecretName" (dict "type" "elasticsearch" "root" $) }}
                  key: {{ include "nta.credentialsUsernameSecretKey" (dict "type" "elasticsearch" "root" $) }}
            - name: ELASTICSEARCH_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "nta.credentialsSecretName" (dict "type" "elasticsearch" "root" $) }}
                  key: {{ include "nta.credentialsSecretKey" (dict "type" "elasticsearch" "root" $) }}

            # Redis凭据
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "nta.credentialsSecretName" (dict "type" "redis" "root" $) }}
                  key: {{ include "nta.credentialsSecretKey" (dict "type" "redis" "root" $) }}

            # Nebula凭据
            - name: NEBULA_USERNAME
              valueFrom:
                secretKeyRef:
                  name: {{ include "nta.credentialsSecretName" (dict "type" "nebula" "root" $) }}
                  key: {{ include "nta.credentialsUsernameSecretKey" (dict "type" "nebula" "root" $) }}
            - name: NEBULA_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "nta.credentialsSecretName" (dict "type" "nebula" "root" $) }}
                  key: {{ include "nta.credentialsSecretKey" (dict "type" "nebula" "root" $) }}
          # 添加健康检查
          readinessProbe:
            httpGet:
              path: /jobmanager/config
              port: 8081
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /jobmanager/config
              port: 8081
            initialDelaySeconds: 60
            periodSeconds: 15
            timeoutSeconds: 5
            failureThreshold: 5
          volumeMounts:
            # 配置文件挂载
            # 将 ConfigMap 挂载到 /opt/flink/usrlib/classes 目录
            # 其中 config.properties 文件将位于 /opt/flink/usrlib/classes/config.properties
            # 这个路径与 CONFIG_PATH 环境变量一致
            - name: config-volume
              mountPath: /opt/flink/usrlib/classes
            - name: flink-data
              mountPath: /opt/flink/savepoints
              subPath: savepoints
            - name: flink-data
              mountPath: /opt/flink/checkpoints
              subPath: checkpoints
{{- end -}}
