{{- if .Values.features.graph.enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: graph-service-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
data:
  application.yml: |
    # 环境特定配置 - 这些配置会覆盖JAR包内的默认配置

    # 服务器配置
    server:
      port: 8083
      servlet:
        context-path: /atlas

    # 应用名称
    spring:
      application:
        name: graph-service

      # 数据库连接配置
      datasource:
        url: jdbc:mysql://{{ .Values.infrastructure.mysql.host }}:{{ .Values.infrastructure.mysql.port }}/{{ .Values.services.graph-service.database }}?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai
        username: ${MYSQL_USERNAME}
        password: ${MYSQL_PASSWORD}

      # Redis配置
      redis:
        host: {{ .Values.infrastructure.redis.host }}
        port: {{ .Values.infrastructure.redis.port }}
        database: 0
        timeout: 10000

      # Kafka配置
      kafka:
        bootstrap-servers: {{ .Values.infrastructure.kafka.host }}:{{ .Values.infrastructure.kafka.port }}
        consumer:
          group-id: graph-service
          auto-offset-reset: earliest
          key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
          value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
        properties:
          security.protocol: {{ .Values.infrastructure.kafka.security.protocol }}
          sasl.mechanism: {{ .Values.infrastructure.kafka.security.mechanism }}
          sasl.jaas.config: org.apache.kafka.common.security.plain.PlainLoginModule required username="${KAFKA_CLIENT_USER}" password="${KAFKA_CLIENT_PASSWORD}";

    # MyBatis类型别名包配置
    mybatis-plus:
      type-aliases-package: com.geeksec.graph.entity

    # Nebula Graph配置
    nebula:
      hosts: ${NEBULA_HOSTS}
      username: ${NEBULA_USERNAME}
      password: ${NEBULA_PASSWORD}
      space: ${NEBULA_SPACE}
      pool-size: 10
      timeout: 30000

    # Doris配置
    doris:
      jdbc-url: jdbc:mysql://{{ .Values.infrastructure.doris.cluster.name }}-fe.{{ .Values.global.namespace }}.svc:{{ .Values.infrastructure.doris.fe.queryPort }}/nta_analysis?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai
      username: ${DORIS_USERNAME}
      password: ${DORIS_PASSWORD}
      pool-size: 10
      timeout: 30000

    # 生产环境特定配置
    knife4j:
      production: {{ eq .Values.global.springProfiles "prod" }}
{{- end -}}
