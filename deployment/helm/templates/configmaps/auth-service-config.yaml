{{- if .Values.services.auth-service.enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: auth-service-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
data:
  application.yml: |
    # 环境特定配置 - 这些配置会覆盖JAR包内的默认配置

    # 服务器配置
    server:
      port: 8081
      servlet:
        context-path: /auth

    # 应用名称
    spring:
      application:
        name: auth-service

      # 数据库连接配置
      datasource:
        url: jdbc:mysql://{{ include "nta.mysqlConfig" . | fromYaml | get "host" }}:{{ include "nta.mysqlConfig" . | fromYaml | get "port" }}/{{ .Values.services.auth-service.database }}?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai
        username: ${MYSQL_USERNAME}
        password: ${MYSQL_PASSWORD}

      # Redis配置
      redis:
        host: {{ include "nta.redisConfig" . | fromYaml | get "host" }}
        port: {{ include "nta.redisConfig" . | fromYaml | get "port" }}
        database: 0
        timeout: 10000

    # MyBatis类型别名包配置
    mybatis-plus:
      type-aliases-package: com.geeksec.auth.entity

    # JWT配置
    jwt:
      secret: ${JWT_SECRET}
      expiration: 86400000  # 24小时
      header: Authorization
      token-prefix: Bearer

    # 生产环境特定配置
    knife4j:
      production: {{ eq .Values.global.springProfiles "prod" }}

    # 服务配置
    services:
      analysis-service:
        url: http://analysis-service:8082
      graph-service:
        url: http://graph-service:8083
      search-service:
        url: http://search-service:8084
{{- end -}}
