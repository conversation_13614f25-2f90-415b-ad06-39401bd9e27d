{{- if .Values.services.system-service.enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: system-service-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
data:
  application.yml: |
    server:
      port: 8088
      servlet:
        context-path: /system

    spring:
      datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: jdbc:mysql://{{ include "nta.mysqlConfig" . | fromYaml | get "host" }}:{{ include "nta.mysqlConfig" . | fromYaml | get "port" }}/{{ .Values.services.system-service.database }}?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai
        username: ${MYSQL_USERNAME}
        password: ${MYSQL_PASSWORD}
        druid:
          initial-size: 5
          min-idle: 5
          max-active: 20
          max-wait: 60000
          time-between-eviction-runs-millis: 60000
          min-evictable-idle-time-millis: 300000
          validation-query: SELECT 1 FROM DUAL
          test-while-idle: true
          test-on-borrow: false
          test-on-return: false
          pool-prepared-statements: true
          max-pool-prepared-statement-per-connection-size: 20
          filters: stat,wall
      redis:
        host: {{ include "nta.redisConfig" . | fromYaml | get "host" }}
        port: {{ include "nta.redisConfig" . | fromYaml | get "port" }}
        database: 0
        timeout: 10000
      application:
        name: system-service
      config:
        import: "configtree:/config/"

    mybatis-plus:
      mapper-locations: classpath:mapper/*.xml
      type-aliases-package: com.geeksec.system.entity
      configuration:
        map-underscore-to-camel-case: true
        log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
      global-config:
        db-config:
          id-type: auto
          logic-delete-field: deleted
          logic-delete-value: 1
          logic-not-delete-value: 0

    knife4j:
      enable: true
      production: false
      basic:
        enable: false
      setting:
        language: zh-CN

    management:
      endpoints:
        web:
          exposure:
            include: health,info,metrics
      endpoint:
        health:
          show-details: always
{{- end -}}
