{{- if .Values.services.analysis-service.enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: analysis-service-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
data:
  application.yml: |
    # 环境特定配置 - 这些配置会覆盖JAR包内的默认配置

    # 服务器配置
    server:
      port: 8082
      servlet:
        context-path: /analyze

    # 应用名称
    spring:
      application:
        name: analysis-service

      # 数据库连接配置
      datasource:
        url: jdbc:mysql://{{ include "nta.mysqlConfig" . | fromYaml | get "host" }}:{{ include "nta.mysqlConfig" . | fromYaml | get "port" }}/{{ .Values.services.analysis-service.database }}?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai
        username: ${MYSQL_USERNAME}
        password: ${MYSQL_PASSWORD}

      # Redis配置
      redis:
        host: {{ include "nta.redisConfig" . | fromYaml | get "host" }}
        port: {{ include "nta.redisConfig" . | fromYaml | get "port" }}
        database: 0
        timeout: 10000

      # Kafka配置
      kafka:
        bootstrap-servers: {{ include "nta.kafkaConfig" . | fromYaml | get "host" }}:{{ include "nta.kafkaConfig" . | fromYaml | get "port" }}
        consumer:
          group-id: analysis-service
          auto-offset-reset: earliest
          key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
          value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
        producer:
          key-serializer: org.apache.kafka.common.serialization.StringSerializer
          value-serializer: org.apache.kafka.common.serialization.StringSerializer
        properties:
          security.protocol: {{ include "nta.kafkaConfig" . | fromYaml | get "security" | get "protocol" }}
          sasl.mechanism: {{ include "nta.kafkaConfig" . | fromYaml | get "security" | get "mechanism" }}
          sasl.jaas.config: org.apache.kafka.common.security.plain.PlainLoginModule required username="${KAFKA_CLIENT_USER}" password="${KAFKA_CLIENT_PASSWORD}";

    # MyBatis类型别名包配置
    mybatis-plus:
      type-aliases-package: com.geeksec.analysis.entity

    # 生产环境特定配置
    knife4j:
      production: {{ eq .Values.global.springProfiles "prod" }}

    # 服务配置
    services:
      graph-service:
        url: http://graph-service:8083
      search-service:
        url: http://search-service:8084
{{- end -}}
