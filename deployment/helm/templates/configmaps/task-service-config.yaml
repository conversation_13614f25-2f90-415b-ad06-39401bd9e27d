{{- if .Values.services.task-service.enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: task-service-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
data:
  application.yml: |
    server:
      port: 8086
      servlet:
        context-path: /task

    spring:
      datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: jdbc:mysql://{{ include "nta.mysqlConfig" . | fromYaml | get "host" }}:{{ include "nta.mysqlConfig" . | fromYaml | get "port" }}/{{ .Values.services.task-service.database }}?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai
        username: ${MYSQL_USERNAME}
        password: ${MYSQL_PASSWORD}
        druid:
          initial-size: 5
          min-idle: 5
          max-active: 20
          max-wait: 60000
          time-between-eviction-runs-millis: 60000
          min-evictable-idle-time-millis: 300000
          validation-query: SELECT 1 FROM DUAL
          test-while-idle: true
          test-on-borrow: false
          test-on-return: false
          pool-prepared-statements: true
          max-pool-prepared-statement-per-connection-size: 20
          filters: stat,wall
      redis:
        host: {{ include "nta.redisConfig" . | fromYaml | get "host" }}
        port: {{ include "nta.redisConfig" . | fromYaml | get "port" }}
        database: 0
        timeout: 10000
      application:
        name: task-service
      config:
        import: "configtree:/config/"
      quartz:
        job-store-type: jdbc
        jdbc:
          initialize-schema: never
        properties:
          org.quartz.scheduler.instanceName: NTAScheduler
          org.quartz.scheduler.instanceId: AUTO
          org.quartz.jobStore.class: org.quartz.impl.jdbcjobstore.JobStoreTX
          org.quartz.jobStore.driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
          org.quartz.jobStore.tablePrefix: QRTZ_
          org.quartz.jobStore.isClustered: true
          org.quartz.jobStore.clusterCheckinInterval: 10000
          org.quartz.threadPool.class: org.quartz.simpl.SimpleThreadPool
          org.quartz.threadPool.threadCount: 10
          org.quartz.threadPool.threadPriority: 5

    mybatis-plus:
      mapper-locations: classpath:mapper/*.xml
      type-aliases-package: com.geeksec.task.entity
      configuration:
        map-underscore-to-camel-case: true
        log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
      global-config:
        db-config:
          id-type: auto
          logic-delete-field: deleted
          logic-delete-value: 1
          logic-not-delete-value: 0

    knife4j:
      enable: true
      production: false
      basic:
        enable: false
      setting:
        language: zh-CN

    management:
      endpoints:
        web:
          exposure:
            include: health,info,metrics
      endpoint:
        health:
          show-details: always

    # 服务配置
    services:
      analysis-service:
        url: http://analysis-service:8082
      graph-service:
        url: http://graph-service:8083
{{- end -}}
