{{- if .Values.features.graph.enabled -}}
apiVersion: apps.nebula-graph.io/v1alpha1
kind: NebulaCluster
metadata:
  name: {{ .Release.Name }}-nebula
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
spec:
  graphd:
    resources:
      requests:
        cpu: "500m"
        memory: "1Gi"
      limits:
        cpu: "1000m"
        memory: "2Gi"
    replicas: {{ .Values.infrastructure.nebula.graphd.replicas }}
    image: vesoft/nebula-graphd
    version: v3.8.0
    service:
      type: ClusterIP
    logVolumeClaim:
      resources:
        requests:
          storage: 1Gi
      storageClassName: standard

  metad:
    resources:
      requests:
        cpu: "500m"
        memory: "1Gi"
      limits:
        cpu: "1000m"
        memory: "2Gi"
    replicas: {{ .Values.infrastructure.nebula.metad.replicas }}
    image: vesoft/nebula-metad
    version: v3.8.0
    dataVolumeClaim:
      resources:
        requests:
          storage: 5Gi
      storageClassName: standard
    logVolumeClaim:
      resources:
        requests:
          storage: 1Gi
      storageClassName: standard

  storaged:
    resources:
      requests:
        cpu: "1000m"
        memory: "2Gi"
      limits:
        cpu: "2000m"
        memory: "4Gi"
    replicas: {{ .Values.infrastructure.nebula.storaged.replicas }}
    image: vesoft/nebula-storaged
    version: v3.8.0
    dataVolumeClaims:
    - resources:
        requests:
          storage: 10Gi
      storageClassName: standard
    logVolumeClaim:
      resources:
        requests:
          storage: 1Gi
      storageClassName: standard

  reference:
    name: statefulsets.apps
    version: v1
  schedulerName: default-scheduler
  imagePullPolicy: IfNotPresent
  enablePVReclaim: false
  logRotate:
    rotate: 5
    size: "100M"
{{- end -}}
