{{- if and .Values.infrastructure.nebula.enabled .Values.infrastructure.nebula.credentials.password }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.infrastructure.nebula.cluster.name }}-nebula-user
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: nebula-credentials
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
type: Opaque
data:
  username: {{ .Values.infrastructure.nebula.credentials.username | b64enc }}
  password: {{ .Values.infrastructure.nebula.credentials.password.value | b64enc }}
{{- end }}
