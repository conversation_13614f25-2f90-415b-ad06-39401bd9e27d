Thank you for installing {{ .Chart.Name }}.

Your release is named {{ .Release.Name }} in namespace {{ .Release.Namespace }}.

## 流量管理

NTA 平台使用 Istio 进行流量管理。系统使用 Istio Gateway 和 VirtualService 进行流量路由。

您可以通过以下命令获取 Istio Gateway 的外部 IP 或主机名：

  $ kubectl get svc -n {{ .Release.Namespace }} istio-ingressgateway -o jsonpath='{.status.loadBalancer.ingress[0].ip}'

或者：

  $ kubectl get svc -n {{ .Release.Namespace }} istio-ingressgateway -o jsonpath='{.status.loadBalancer.ingress[0].hostname}'

## 检查部署状态

您可以使用以下命令检查部署状态：

  $ kubectl get pods -n {{ .Release.Namespace }}

## 查看服务日志

您可以使用以下命令查看服务日志：

  $ kubectl logs -f <pod-name> -n {{ .Release.Namespace }}

## 访问应用

应用可通过以下 URL 访问：

  http://{{ .Values.global.domain }}

更多信息，请参阅文档：https://github.com/geeksec/nta/tree/main/nta-platform/deployment/helm
