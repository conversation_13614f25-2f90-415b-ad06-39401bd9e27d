{"order": 0, "index_patterns": ["cert_*"], "settings": {"index": {"number_of_shards": "5", "number_of_replicas": "0", "max_result_window": 100000}}, "mappings": {"properties": {"SAN": {"index": true, "type": "keyword", "ignore_above": 8192}, "Usage": {"index": true, "type": "keyword", "ignore_above": 1024}, "PemMD5": {"index": true, "type": "keyword"}, "CertID": {"index": true, "type": "keyword"}, "TaskId": {"index": true, "type": "integer"}, "BatchId": {"index": true, "type": "integer"}, "iocIP": {"index": true, "type": "keyword"}, "iocDomain": {"index": true, "type": "keyword"}, "UserType": {"index": true, "type": "keyword"}, "IssuerArea": {"index": true, "type": "keyword"}, "SubjectArea": {"index": true, "type": "keyword"}, "CertSource": {"index": true, "type": "keyword"}, "ThreatLevel": {"index": true, "type": "keyword"}, "BusinessType": {"index": true, "type": "keyword"}, "CAType": {"index": true, "type": "keyword"}, "IndustryType": {"index": true, "type": "keyword"}, "ASN1SHA256": {"index": true, "type": "keyword"}, "Issuer": {"properties": {"ST": {"index": true, "type": "keyword"}, "EMAIL_ADDRESS": {"index": true, "type": "keyword"}, "C": {"index": true, "type": "keyword"}, "SERIAL_NUMBER": {"index": true, "type": "keyword"}, "O": {"index": true, "type": "keyword"}, "OU": {"index": true, "type": "keyword"}, "CN": {"index": true, "type": "keyword"}, "IssuerCN": {"index": true, "type": "keyword"}, "L": {"index": true, "type": "keyword"}, "UNSTRUCTURED_NAME": {"index": true, "type": "keyword"}, "DOMAIN_COMPONENT": {"index": true, "type": "keyword"}, "STREET_ADDRESS": {"index": true, "type": "keyword"}, "SURNAME": {"index": true, "type": "keyword"}, "GIVEN_NAME": {"index": true, "type": "keyword"}, "TITLE": {"index": true, "type": "keyword"}, "GENERATION_QUALIFIER": {"index": true, "type": "keyword"}, "X500_UNIQUE_IDENTIFIER": {"index": true, "type": "keyword"}, "DN_QUALIFIER": {"index": true, "type": "keyword"}, "PSEUDONYM": {"index": true, "type": "keyword"}, "USER_ID": {"index": true, "type": "keyword"}, "JURISDICTION_COUNTRY_NAME": {"index": true, "type": "keyword"}, "JURISDICTION_LOCALITY_NAME": {"index": true, "type": "keyword"}, "JURISDICTION_STATE_OR_PROVINCE_NAME": {"index": true, "type": "keyword"}, "BUSINESS_CATEGORY": {"index": true, "type": "keyword"}, "POSTAL_ADDRESS": {"index": true, "type": "keyword"}, "POSTAL_CODE": {"index": true, "type": "keyword"}, "INN": {"index": true, "type": "keyword"}, "OGRN": {"index": true, "type": "keyword"}, "SNILS": {"index": true, "type": "keyword"}, "unk": {"index": true, "type": "keyword"}}}, "PublicKey": {"index": true, "type": "keyword", "ignore_above": 8192}, "IssuerMD5": {"index": true, "type": "keyword"}, "BlackList": {"index": true, "type": "integer"}, "WhiteList": {"index": true, "type": "integer"}, "PemSHA256": {"index": true, "type": "keyword"}, "Method": {"index": true, "type": "keyword"}, "ParseStatus": {"index": true, "type": "keyword"}, "IsDedupCert": {"index": true, "type": "keyword"}, "WhiteCert": {"index": true, "type": "keyword"}, "Duration": {"index": true, "type": "long"}, "SignatureAlgorithm": {"index": true, "type": "keyword"}, "CN": {"index": true, "type": "keyword"}, "NotAfter": {"index": true, "type": "keyword"}, "Subject": {"properties": {"ST": {"index": true, "type": "keyword"}, "unk": {"index": true, "type": "keyword"}, "EMAIL_ADDRESS": {"index": true, "type": "keyword"}, "C": {"index": true, "type": "keyword"}, "SERIAL_NUMBER": {"index": true, "type": "keyword"}, "O": {"index": true, "type": "keyword"}, "OU": {"index": true, "type": "keyword"}, "CN": {"index": true, "type": "keyword"}, "L": {"index": true, "type": "keyword"}, "UNSTRUCTURED_NAME": {"index": true, "type": "keyword"}, "DOMAIN_COMPONENT": {"index": true, "type": "keyword"}, "STREET_ADDRESS": {"index": true, "type": "keyword"}, "SURNAME": {"index": true, "type": "keyword"}, "GIVEN_NAME": {"index": true, "type": "keyword"}, "TITLE": {"index": true, "type": "keyword"}, "GENERATION_QUALIFIER": {"index": true, "type": "keyword"}, "X500_UNIQUE_IDENTIFIER": {"index": true, "type": "keyword"}, "DN_QUALIFIER": {"index": true, "type": "keyword"}, "PSEUDONYM": {"index": true, "type": "keyword"}, "USER_ID": {"index": true, "type": "keyword"}, "JURISDICTION_COUNTRY_NAME": {"index": true, "type": "keyword"}, "JURISDICTION_LOCALITY_NAME": {"index": true, "type": "keyword"}, "JURISDICTION_STATE_OR_PROVINCE_NAME": {"index": true, "type": "keyword"}, "BUSINESS_CATEGORY": {"index": true, "type": "keyword"}, "POSTAL_ADDRESS": {"index": true, "type": "keyword"}, "POSTAL_CODE": {"index": true, "type": "keyword"}, "INN": {"index": true, "type": "keyword"}, "OGRN": {"index": true, "type": "keyword"}, "SNILS": {"index": true, "type": "keyword"}}}, "PemSHA1": {"index": true, "type": "keyword"}, "ASN1MD5": {"index": true, "type": "keyword"}, "Extension": {"properties": {"nsCertType": {"index": true, "type": "keyword"}, "authorityKeyIdentifier": {"index": true, "type": "keyword"}, "policyConstraints": {"index": true, "type": "keyword"}, "nsCaRevocationUrl": {"index": true, "type": "keyword"}, "subjectKeyIdentifier": {"index": true, "type": "keyword"}, "policyMappings": {"index": true, "type": "keyword"}, "keyUsage": {"index": true, "type": "keyword"}, "sct": {"index": true, "type": "keyword", "ignore_above": 8192}, "preSct": {"index": true, "type": "keyword", "ignore_above": 8192}, "authorityInfoAccess": {"index": true, "type": "keyword", "ignore_above": 8192}, "nsComment": {"index": true, "type": "keyword"}, "certificatePolicies": {"index": true, "type": "keyword", "ignore_above": 8192}, "inhibitAnyPolicy": {"index": true, "type": "keyword"}, "basicConstraints": {"index": true, "type": "keyword"}, "preCertPoison": {"index": true, "type": "keyword"}, "tlsFeature": {"index": true, "type": "keyword"}, "extendedKeyUsage": {"index": true, "type": "keyword", "ignore_above": 1024}, "nameConstraints": {"index": true, "type": "keyword", "ignore_above": 8192}, "subjectInfoAccess": {"index": true, "type": "keyword", "ignore_above": 8192}, "issuerAltName": {"index": true, "type": "keyword", "ignore_above": 8192}, "privateKeyUsagePeriod": {"index": true, "type": "keyword"}, "crlDistributionPoints": {"index": true, "type": "keyword", "ignore_above": 8192}, "freshestCRL": {"index": true, "type": "keyword", "ignore_above": 8192}, "ocspNoCheck": {"index": true, "type": "keyword"}, "subjectAltName": {"index": true, "type": "keyword", "ignore_above": 8192}}}, "SubjectMD5": {"index": true, "type": "keyword"}, "Format": {"index": true, "type": "keyword"}, "SerialNumber": {"index": true, "type": "keyword"}, "Version": {"index": true, "type": "keyword"}, "NotBefore": {"index": true, "type": "keyword"}, "ASN1SHA1": {"index": true, "type": "keyword"}, "FatherCertID": {"index": true, "type": "keyword"}, "FatherCertIDList": {"index": true, "type": "keyword"}, "ImportTime": {"index": true, "type": "long"}, "PublicKeyAlgorithmLength": {"index": true, "type": "keyword"}, "PublicKeyAlgorithm": {"index": true, "type": "keyword"}, "IsError": {"index": true, "type": "keyword"}, "PositiveHash": {"index": true, "type": "keyword"}, "NegativeHash": {"index": true, "type": "keyword"}, "correctASN1SHA1": {"index": true, "type": "keyword"}, "UserIDList": {"index": true, "type": "keyword"}, "Labels": {"index": true, "type": "keyword"}, "AssociateDomain": {"index": true, "type": "keyword"}, "AssociateIP": {"index": true, "type": "keyword"}, "KnowledgeCollisionResult": {"index": true, "type": "keyword"}, "UncommonOIDs": {"index": true, "type": "keyword"}, "SPKISHA256": {"index": true, "type": "keyword"}, "PubAlgOid": {"index": true, "type": "keyword"}, "PubAlgParamOid": {"index": true, "type": "keyword"}, "PublicKeyAlgorithmParameter": {"index": true, "type": "keyword"}, "sigAlgName": {"index": true, "type": "keyword"}, "sigAlgOid": {"index": true, "type": "keyword"}}}, "aliases": {}}