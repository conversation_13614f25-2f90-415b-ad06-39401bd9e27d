{"order": 0, "index_patterns": ["ssh*"], "settings": {"index": {"max_result_window": 1000000, "max_terms_count": 1000000, "number_of_shards": "1", "translog.durability": "async", "translog.flush_threshold_size": "1024mb", "translog.sync_interval": "120s", "number_of_replicas": "0"}}, "mappings": {"properties": {"BatchNum": {"type": "long"}, "Client": {"properties": {"CompressionAlgorithmsC2S": {"type": "keyword", "index": "true"}, "CompressionAlgorithmsS2C": {"type": "keyword", "index": "true"}, "Cookie": {"type": "keyword", "index": "true"}, "EncryptionAlgorithmsC2S": {"type": "keyword", "index": "true"}, "EncryptionAlgorithmsS2C": {"type": "keyword", "index": "true"}, "KexAlgorithms": {"type": "keyword", "index": "true"}, "MacAlgorithmsC2S": {"type": "keyword", "index": "true"}, "MacAlgorithmsS2C": {"type": "keyword", "index": "true"}, "Protocol": {"type": "keyword", "index": "true"}, "ServerHostKeyAlgorithms": {"type": "keyword", "index": "true"}}}, "CreateTime": {"type": "long"}, "Hkey": {"type": "keyword", "index": "true"}, "Server": {"properties": {"CompressionAlgorithmsC2S": {"type": "keyword", "index": "true"}, "CompressionAlgorithmsS2C": {"type": "keyword", "index": "true"}, "Cookie": {"type": "keyword", "index": "true"}, "EncryptionAlgorithmsC2S": {"type": "keyword", "index": "true"}, "EncryptionAlgorithmsS2C": {"type": "keyword", "index": "true"}, "KexAlgorithms": {"type": "keyword", "index": "true"}, "MacAlgorithmsC2S": {"type": "keyword", "index": "true"}, "MacAlgorithmsS2C": {"type": "keyword", "index": "true"}, "Protocol": {"type": "keyword", "index": "true"}, "ServerHostKeyAlgorithms": {"type": "keyword", "index": "true"}}}, "ServerIP": {"type": "keyword", "index": "true"}, "SessionId": {"type": "keyword", "index": "true"}, "StartNSec": {"type": "long"}, "StartTime": {"type": "long"}, "TaskId": {"type": "long"}, "dIp": {"type": "ip"}, "dPort": {"type": "long"}, "es_key": {"type": "keyword", "index": "true"}, "sIp": {"type": "ip"}, "sPort": {"type": "long"}}}, "aliases": {"add": {"index": "ssh*", "alias": "ssh"}}}