{"order": 0, "index_patterns": ["connectinfo_*"], "settings": {"index": {"max_result_window": 1000000, "max_terms_count": 1000000, "refresh_interval": "30s", "number_of_shards": "1", "translog.durability": "async", "translog.flush_threshold_size": "1024mb", "translog.sync_interval": "120s", "number_of_replicas": "0"}}, "mappings": {"properties": {"AppName": {"type": "keyword", "index": true}, "DNS": {"properties": {"Domain": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "DomainIp": {"type": "keyword", "index": true}}}, "Duration": {"type": "long"}, "EndTime": {"type": "long"}, "FirstProto": {"type": "long"}, "HTTP": {"properties": {"Act": {"type": "keyword", "index": true}, "User-Agent": {"type": "keyword", "index": true}, "Host": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "Response": {"type": "keyword", "index": true}, "Url": {"type": "keyword", "index": true}}}, "Hkey": {"type": "keyword", "index": true}, "ip_port_ipport_appid": {"type": "keyword", "index": true}, "dIpSubdivisions": {"type": "keyword", "index": true}, "dIpCity": {"type": "keyword", "index": true}, "dIpCountry": {"type": "keyword", "index": true}, "sIpSubdivisions": {"type": "keyword", "index": true}, "sIpCity": {"type": "keyword", "index": true}, "sIpCountry": {"type": "keyword", "index": true}, "ProxyIP": {"type": "keyword", "index": true}, "Labels": {"type": "long"}, "ProName": {"type": "keyword", "index": true}, "SSL": {"properties": {"CH_ALPN": {"type": "keyword", "index": true}, "CH_Ciphersuit": {"type": "keyword", "index": true}, "CH_CiphersuitNum": {"type": "long"}, "CH_ServerName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "dCertHash": {"type": "keyword", "index": true}, "sCertHash": {"type": "keyword", "index": true}}}, "SbytesDiviDbytes": {"type": "long"}, "SessionId": {"type": "keyword", "index": true}, "StartTime": {"type": "long"}, "ThreadId": {"type": "long"}, "TotalBytes": {"type": "long"}, "TotalPacketNum": {"type": "long"}, "dBaseTTL": {"type": "long"}, "dHTTPFinger": {"type": "keyword", "index": true}, "dInitialTTL": {"type": "long"}, "dIp": {"type": "ip"}, "dMac": {"type": "keyword", "index": true}, "dMaxTTL": {"type": "long"}, "dPort": {"type": "long"}, "dSSLFinger": {"type": "keyword", "index": true}, "es_key": {"type": "keyword", "index": true}, "ip2ip": {"type": "keyword", "index": true}, "ip_port_ippro_appid": {"type": "keyword", "index": true}, "mac2mac": {"type": "keyword", "index": true}, "pkt": {"properties": {"dPayloadBytes": {"type": "long"}, "dPayloadNum": {"type": "long"}, "sPayloadBytes": {"type": "long"}, "sPayloadNum": {"type": "long"}}}, "sHTTPFinger": {"type": "keyword", "index": true}, "sInitialTTL": {"type": "long"}, "sIp": {"type": "ip"}, "sMac": {"type": "keyword", "index": true}, "sPort": {"type": "long"}, "sSSLFinger": {"type": "keyword", "index": true}, "sTTLMax": {"type": "long"}, "sTTLMin": {"type": "long"}, "sip_appid_dport_dip": {"type": "keyword", "index": true}}}, "aliases": {"add": {"index": "connectinfo_*", "alias": "connectinfo"}}}