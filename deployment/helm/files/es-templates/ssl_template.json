{"order": 0, "index_patterns": ["ssl*"], "settings": {"index": {"max_result_window": 1000000, "max_terms_count": 1000000, "refresh_interval": "30s", "number_of_shards": "1", "translog.durability": "async", "translog.flush_threshold_size": "1024mb", "translog.sync_interval": "120s", "number_of_replicas": "0"}}, "mappings": {"properties": {"AppName": {"type": "keyword", "index": true}, "BatchNum": {"type": "long"}, "CH_ALPN": {"type": "keyword", "index": true}, "CH_Ciphersuit": {"type": "keyword", "index": true}, "CH_CiphersuitNum": {"type": "long"}, "CH_CompressionMethod": {"type": "keyword", "index": true}, "CH_CompressionMethodLen": {"type": "long"}, "CH_Extention": {"properties": {"l": {"type": "long"}, "t": {"type": "long"}, "v": {"type": "keyword", "index": true}}}, "CH_ExtentionNum": {"type": "long"}, "CH_ServerName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "CH_ServerNameType": {"type": "long"}, "CH_SessionIDLen": {"type": "long"}, "CH_SessionTicket": {"type": "keyword", "index": true}, "CH_Time": {"type": "long"}, "CH_Version": {"type": "long"}, "ProxyIP": {"type": "keyword", "index": true}, "Hkey": {"type": "keyword", "index": true}, "SH_ALPN": {"type": "keyword", "index": true}, "SH_Cipersuite": {"type": "keyword", "index": true}, "SH_CompressionMethod": {"type": "keyword", "index": true}, "SH_Extention": {"properties": {"l": {"type": "long"}, "t": {"type": "long"}, "v": {"type": "keyword", "index": true}}}, "SH_Random": {"type": "keyword", "index": true}, "SH_SessionId": {"type": "keyword", "index": true}, "SH_SessionIdLen": {"type": "long"}, "SH_SessionTicket": {"type": "keyword", "index": true}, "SH_Time": {"type": "long"}, "SH_Version": {"type": "long"}, "SessionId": {"type": "keyword", "index": true}, "StartNSec": {"type": "long"}, "StartTime": {"type": "long"}, "TaskId": {"type": "long"}, "cSSLVersion": {"type": "long"}, "dCertHash": {"type": "keyword", "index": true}, "dCertHashStr": {"type": "keyword", "index": true}, "dCertNum": {"type": "long"}, "dIp": {"type": "ip"}, "dKeyExchange": {"type": "keyword", "index": true}, "dKeyExchangelen": {"type": "long"}, "dNewSessionTicket_LifeTime": {"type": "long"}, "dNewSessionTicket_Ticket": {"type": "keyword", "index": true}, "dNewSessionTicket_TicketLen": {"type": "long"}, "dPort": {"type": "long"}, "dSSLFinger": {"type": "keyword", "index": true}, "es_key": {"type": "keyword", "index": true}, "sCertHash": {"type": "keyword", "index": true}, "sCertNum": {"type": "long"}, "sIp": {"type": "ip"}, "sKeyExchange": {"type": "keyword", "index": true}, "sKeyExchangeLen": {"type": "long"}, "sPort": {"type": "long"}, "sSSLFinger": {"type": "keyword", "index": true}, "sSSLVersion": {"type": "long"}}}, "aliases": {"add": {"index": "ssl_*", "alias": "ssl"}}}