"Port","Tcp_Pro_Name","Tcp_Pro_Id","Udp_Pro_Name","Udp_Pro_Id","Remark"
"564","9P","596",,"0","9P (Plan 9)"
"384",,"0",,"0","A Remote Network Server System"
"11",,"0",,"0","Active Users (systat service)[14][15]"
"843",,"0",,"0","Adobe Flash[106]"
"666",,"0",,"0","airserv-ng, aircrack-ng's server for remote-controlling wireless devices"
"210",,"0",,"0","ANSI Z39.50"
"75",,"0",,"0","Any private dial out service[further explanation needed]"
"77",,"0",,"0","Any private Remote job entry[further explanation needed]"
"57",,"0",,"0","Any private terminal access[further explanation needed]"
"87",,"0",,"0","Any private terminal link[further explanation needed]"
"548","AFP","1140",,"0","Apple Filing Protocol (AFP) over TCP[10]"
"201",,"43",,"0","AppleTalk Routing Maintenance"
"674","ACAP","296",,"0","Application Configuration Access Protocol (ACAP)"
"262",,"0",,"0","Arcisdms"
"623",,"0","RMCP","510","ASF Remote Management and Control Protocol (ASF-RMCP) & IPMI Remote Management Protocol"
"623",,"0","IPMI","291","ASF Remote Management and Control Protocol (ASF-RMCP) & IPMI Remote Management Protocol"
"387",,"0",,"0","AURP (AppleTalk Update-based Routing Protocol)[84]"
"465","SSL","678",,"0","Authenticated SMTP[10] over TLS/SSL (SMTPS)[86]"
"113",,"0",,"0","Authentication Service (auth), the predecessor to identification protocol. Used to determine a user's identity of a particular TCP connection.[64]"
"152",,"0",,"0","Background File Transfer Program (BFTP)[72][importance?]"
"953",,"0",,"0","BIND remote name daemon control (RNDC)[109][110]"
"68",,"0","DHCP","66","Bootstrap Protocol (BOOTP) client;[10] also used by Dynamic Host Configuration Protocol (DHCP)"
"67",,"0","DHCP","66","Bootstrap Protocol (BOOTP) server;[10] also used by Dynamic Host Configuration Protocol (DHCP)"
"67",,"0","Bootstrap_Protocol","1301","Bootstrap Protocol (BOOTP) server;[10] also used by Dynamic Host Configuration Protocol (DHCP)"
"264",,"0",,"0","Border Gateway Multicast Protocol (BGMP)"
"179","BGP","61",,"0","Border Gateway Protocol (BGP),[78] used to exchange routing and reachability information among autonomous systems (AS) on the Internet"
"897",,"0",,"0","Brocade SMI-S RPC"
"898","SSL","678",,"0","Brocade SMI-S RPC SSL"
"105",,"0",,"0","CCSO Nameserver[58]"
"888",,"0",,"0","cddbp, CD DataBase (CDDB) protocol (CDDBP)"
"829","CMP","121",,"0","Certificate Management Protocol[105]"
"19",,"0",,"0","Character Generator Protocol (CHARGEN)[20]"
"711",,"0",,"0","Cisco Tag Distribution Protocol[100][101][102]—being replaced by the MPLS Label Distribution Protocol[103]"
"504",,"0",,"0","Citadel, multiservice protocol for dedicated clients for the Citadel groupware system"
"371",,"0",,"0","ClearCase albd"
"356",,"0",,"0","cloanto-net-1 (used by Cloanto Amiga Explorer and VMs)"
"370",,"0",,"0","codaauth2, Coda authentication server"
"542",,"0",,"0","commerce (Commerce Applications)"
"631",,"0","CUPS","134","Common Unix Printing System (CUPS) administration console (extension to IPP)"
"512","EXEC","194",,"0","comsat, together with biff"
"782",,"0",,"0","Conserver serial-console management server"
"13","DAYTIME","137","DAYTIME","137","Daytime Protocol[16]"
"135",,"0",,"0","DCE endpoint resolution"
"647",,"0","DHCP","66","DHCP Failover protocol[91]"
"847",,"0","DHCP","66","DHCP Failover protocol"
"546",,"0","DHCPv6","147","DHCPv6 client"
"547","DHCPv6","147","DHCPv6","147","DHCPv6 server"
"399",,"0",,"0","Digital Equipment Corporation DECnet (Phase V+) over TCP/IP"
"104",,"0",,"0","Digital Imaging and Communications in Medicine (DICOM; also port 11112)"
"9",,"0",,"0","Discard Protocol[12]"
"158",,"0",,"0","Distributed Mail System Protocol (DMSP, sometimes referred to as Pcmail)[74][importance?]"
"853","SSL","678",,"0","DNS over TLS (RFC 7858)"
"90",,"0",,,"dnsix (DoD Network Security for Information Exchange) Securit [sic?] Attribute Token Map[importance?]"
"53","DNS","156","DNS","156","Domain Name System (DNS)[37][10]"
"666",,"0",,"0","Doom, first online first-person shooter"
"7","ECHO","171","ECHO","171","Echo Protocol[9][10]"
"259",,"0",,"0","Efficient Short Remote Operations (ESRO)"
"520",,"0","RIP","507","RIP(Routing Information Protocol,路由信息协议）是一种内部网关协议（IGP），是一种动态路由选择协议，用于自治系统（AS）内的路由信息的传递。"
"587","MAIL_SMTP","568",,"0","email message submission[10][89] (SMTP)"
"700","EPP","1090",,"0","Extensible Provisioning Protocol (EPP), a protocol for communication between domain name registries and registrars (RFC 5734)"
"21","FTP","220",,"0","File Transfer Protocol (FTP) control (command)[10][11][21][22]"
"20","FTP","220",,"0","File Transfer Protocol (FTP) data transfer[10]"
"591","HTTP","263",,"0","FileMaker 6.0 (and later) Web Sharing (HTTP Alternate, also see port 80)"
"79","FINGER","212",,"0","Finger protocol[10][47][48]"
"510","FCP","205",,"0","FirstClass Protocol (FCP), used by FirstClass client/server groupware system"
"126",,"0",,"0","Formerly Unisys Unitary Login, renamed by Unisys to NXEdit. Used by Unisys Programmer's Workbench for Clearpath MCP, an IDE for Unisys MCP software development"
"990","SSL","678",,"0","FTPS Protocol (control), FTP over TLS/SSL"
"989","SSL","678",,"0","FTPS Protocol (data), FTP over TLS/SSL"
"491",,"0",,"0","GO-Global remote access and application publishing software"
"70",,"0",,"0","Gopher protocol[43]"
"848",,"0",,"0","Group Domain Of Interpretation (GDOI) protocol"
"61",,"0",,"0","Historically assigned to the NIFTP-Based Mail protocol[38], but was never documented in the related IEN.[39] The port number entry was removed from IANA's registry on 2017-05-18.[1]"
"51",,"0",,"0","Historically used for Interface Message Processor logical address management,[35] entry has been removed by IANA on 2013-05-25"
"42",,"0",,"0","Host Name Server Protocol[28]"
"383",,"0",,"0","HP data alarm manager"
"593","HTTP","263",,"0","HTTP RPC Ep Map, Remote procedure call over Hypertext Transfer Protocol, often used by Distributed Component Object Model services and Microsoft Exchange Server"
"280","HTTP","263",,"0","http-mgmt"
"80","HTTP","263",,"0","Hypertext Transfer Protocol (HTTP)[10][49][50][51]"
"443","SSL","678",,"0","Hypertext Transfer Protocol over TLS/SSL (HTTPS)[10]"
"888",,"0",,"0","IBM Endpoint Manager Remote Control"
"657","RMCP","510",,"0","IBM RMC (Remote monitoring and Control) protocol, used by System p5 AIX Integrated Virtualization Manager (IVM)[94] and Hardware Management Console to connect managed logical partitions (LPAR) to enable dynamic partition reconfiguration"
"108","SNA","570",,"0","IBM Systems Network Architecture (SNA) gateway access server"
"113",,"0",,"0","Ident, authentication service/identification protocol,[10][63] used by IRC servers to identify users"
"695","SSL","678",,"0","IEEE Media Management System over SSL (IEEE-MMS-SSL)[95]"
"651","MMS","1207",,"0","IEEE-MMS"
"143","MAIL_IMAP","284",,"0","Internet Message Access Protocol (IMAP),[10] management of electronic mail messages on a server[71]"
"220","MAIL_IMAP","284",,"0","Internet Message Access Protocol (IMAP), version 3"
"993","SSL","678",,"0","Internet Message Access Protocol over TLS/SSL (IMAPS)[10]"
"631","IPP","1060","CUPS","134","Internet Printing Protocol (IPP)[10]"
"194","IRC","301",,"0","Internet Relay Chat (IRC)[79]"
"994","SSL","678",,"0","Internet Relay Chat over TLS/SSL (IRCS). Previously assigned, but not used in common practice.[79]"
"500","IPSec_ISAKMP","302","IPSec_ISAKMP","302","Internet Security Association and Key Management Protocol (ISAKMP) / Internet Key Exchange (IKE)[10]"
"213",,"0","IPX","299","Internetwork Packet Exchange (IPX)"
"702","BEEP","58",,"0","IRIS[97][98] (Internet Registry Information Service) over BEEP (Blocks Extensible Exchange Protocol)[99] (RFC 3983)"
"860","SCSI","264",,"0","iSCSI (RFC 3720)"
"102","TSAP","90",,"0","ISO Transport Service Access Point (TSAP) Class 0 protocol;[56][57]"
"749","KERBEROS","321","KERBEROS","321","Kerberos (protocol) administration[10]"
"88","KERBEROS","321","KERBEROS","321","Kerberos[10][53][54] authentication system"
"464","KPASSWD","326","KPASSWD","326","Kerberos Change/Set password"
"750",,"0","KRB4","327","kerberos-iv, Kerberos version IV"
"751","KERBEROS","321","KERBEROS","321","kerberos_master, Kerberos authentication"
"543","KERBEROS","321","KERBEROS","321","klogin, Kerberos login"
"754","KERBEROS","321","KERBEROS","321","krb5_prop, Kerberos v5 slave propagation"
"760","KERBEROS","321","KERBEROS","321","krbupdate [kreg], Kerberos registration"
"544","KERBEROS","321","KERBEROS","321","kshell, Kerberos Remote shell"
"646","LDP","338","LDP","338","Label Distribution Protocol (LDP), a routing protocol used in MPLS networks"
"585","SSL","678",,"0","Legacy use of Internet Message Access Protocol over TLS/SSL (IMAPS), now in use at port 993.[88]"
"389",,"0","LDAP","337","Lightweight Directory Access Protocol (LDAP)[10]"
"636",,"0","LDAP","337","Lightweight Directory Access Protocol over TLS/SSL (LDAPS)[10]"
"515","LPD","352",,"0","Line Printer Daemon (LPD),[10] print service"
"701",,"0","LMP","349","Link Management Protocol (LMP),[96] a protocol that runs between a pair of nodes and is used to manage traffic engineering (TE) links"
"694",,"0",,"0","Linux-HA high-availability heartbeat"
"311",,"0",,"0","Mac OS X Server Admin[10] (officially AppleShare IP Web administration[1])"
"660",,"0",,"0","Mac OS X Server administration,[1] version 10.4 and earlier[10]"
"350",,"0",,"0","Mapping of Airline Traffic over Internet Protocol (MATIP) type A"
"351",,"0",,"0","MATIP type B"
"800",,"0",,"0","mdbs-daemon"
"654","MMS","1207",,"0","Media Management System (MMS) Media Management Protocol (MMP)[93]"
"218",,"0",,"0","Message posting protocol (MPP)"
"18",,"0",,"0","Message Send Protocol[18][19]"
"135",,"0",,"0","Microsoft EPMAP (End Point Mapper), also known as DCE/RPC Locator service,[68] used to remotely manage services including DHCP server, DNS server and WINS. Also used by DCOM"
"808",,"0",,"0","Microsoft Net.TCP Port Sharing Service"
"987",,"0",,"0","Microsoft Remote Web Workplace, a feature of Windows Small Business Server[112]"
"445","NBT","410",,"0","Microsoft-DS (Directory Services) Active Directory,[85] Windows shares"
"445","NBT","410",,"0","Microsoft-DS (Directory Services) SMB[10] file sharing"
"434",,"0","MIP","379","Mobile IP Agent (RFC 5944)"
"502","MBTCP","369",,"0","Modbus Protocol"
"561",,"0",,"0","monitor"
"691",,"0",,"0","MS Exchange Routing"
"639","MSDP","396",,"0","MSDP, Multicast Source Discovery Protocol"
"138",,"0","NBDGM","783","NetBIOS Datagram Service[10][69][70]"
"137",,"0","NBNS","782","NetBIOS Name Service, used for name registration and resolution[69][70]"
"139","NBT","410",,"0","NetBIOS Session Service[69][70]"
"833","BEEP","58",,"0","NETCONF for SOAP over BEEP"
"832","SSL","678",,"0","NETCONF for SOAP over HTTPS"
"831","BEEP","58",,"0","NETCONF over BEEP"
"830","SSH","580",,"0","NETCONF over SSH"
"532",,"0",,"0","netnews[10]"
"991",,"0",,"0","Netnews Administration System (NAS)[113]"
"71",,"0",,"0","NETRJS protocol[44][45][46]"
"533",,"0",,"0","netwall, For Emergency Broadcasts"
"524","NCP","411","NCP","411","NetWare Core Protocol (NCP) is used for a variety things such as access to primary NetWare server resources, Time Synchronization, etc."
"119","NNTP","431",,"0","Network News Transfer Protocol (NNTP),[10] retrieval of newsgroup messages[66][67]"
"123","NTP","438","NTP","438","Network Time Protocol (NTP), used for time synchronization[10]"
"550",,"0",,"0","new-rwho, new-who[87]"
"101",,"0",,"0","NIC host name[55]"
"433",,"0",,"0","NNSP, part of Network News Transfer Protocol"
"563","SSL","678",,"0","NNTP over TLS/SSL (NNTPS)"
"308",,"0",,"0","Novastor Online Backup"
"518",,"0",,"0","NTalk"
"366",,"0",,"0","On-Demand Mail Relay (ODMR)"
"625",,"0",,"0","Open Directory Proxy (ODProxy)[10]"
"111","RPC","515","RPC","515","Open Network Computing Remote Procedure Call (ONC RPC, sometimes referred to as Sun RPC)"
"698",,"0","OLSR","442","Optimized Link State Routing (OLSR)"
"861",,"0",,"0","OWAMP control (RFC 4656)"
"752",,"0",,"0","passwd_server, Kerberos password (kpasswd) server"
"318",,"0",,"0","PKIX Time Stamp Protocol (TSP)"
"90",,"0",,"0","PointCast (dotcom)[1][third-party source needed]"
"995","SSL","678",,"0","Post Office Protocol 3 over TLS/SSL (POP3S)[10]"
"109","MAIL_POP","475",,"0","Post Office Protocol, version 2 (POP2)[60]"
"110","MAIL_POP","475",,"0","Post Office Protocol, version 3 (POP3)[10][61][62]"
"319",,"0","PTP","483","Precision Time Protocol (PTP) event messages"
"320",,"0","PTP","483","Precision Time Protocol (PTP) general messages"
"15",,"0",,"0","Previously netstat service[1][14]"
"170",,"0",,"0","Print server[verification needed]"
"209",,"0",,"0","Quick Mail Transfer Protocol[82]"
"80",,"0","QUIC","496","Quick UDP Internet Connections (QUIC), a transport protocol over UDP (still in draft as of July 2018), using stream multiplexing, encryption by default with TLS, and currently supporting HTTP/2.[52]"
"443",,"0","QUIC","496","Quick UDP Internet Connections (QUIC), a transport protocol over UDP (still in draft as of July 2018), using stream multiplexing, encryption by default with TLS, and currently supporting HTTP/2.[52]"
"17",,"0",,"0","Quote of the Day (QOTD)[17]"
"554","RTSP","531",,"0","Real Time Streaming Protocol (RTSP)[10]"
"688",,"0",,"0","REALM-RUSD (ApplianceWare Server Appliance Management Protocol)"
"648",,"0",,"0","Registry Registrar Protocol (RRP)[92]"
"601",,"0",,"0","Reliable Syslog Service — used for system logging"
"981",,"0",,"0","Remote HTTPS management for firewall devices running embedded Check Point VPN-1 software[111]"
"5",,"0",,"0","Remote Job Entry[7] was historically using socket 5 in its old socket form, while MIB PIM has identified it as TCP/5[8] and IANA has assigned both TCP and UDP 5 to it."
"50",,"0",,"0","Remote Mail Checking Protocol[34][importance?]"
"530","RPC","515",,"0","Remote procedure call (RPC)"
"514","RSH","518","SYSLOG","589","Remote Shell, used to execute non-interactive commands on a remote system (Remote Shell, rsh, remsh)"
"107",,"0",,"0","Remote User Telnet Service (RTelnet)[59]"
"556",,"0",,"0","Remotefs, RFS, rfs_server"
"39",,"0",,"0","Resource Location Protocol (RLP)[27][importance?]—used for determining the location of higher level services from hosts on a network"
"497",,"0",,"0","Retrospect"
"753",,"0",,"0","Reverse Routing Header (RRH)[104]"
"512","EXEC","194",,"0","Rexec, Remote Process Execution"
"513","RLOGIN","509","WHO","651","rlogin"
"635",,"0",,"0","RLZ DBase"
"560",,"0",,"0","rmonitor, Remote Monitor"
"38",,"0",,"0","Route Access Protocol (RAP)[26][importance?]"
"520",,"0","RIP","507","Routing Information Protocol (RIP)"
"521",,"0","RIPNG","508","Routing Information Protocol Next Generation (RIPng)"
"369",,"0",,"0","Rpc2portmap"
"873","RSYNC","523",,"0","rsync file synchronization protocol"
"643",,"0",,"0","SANity"
"706",,"0",,"0","Secure Internet Live Conferencing (SILC)"
"22","SSH","580",,"0","Secure Shell (SSH),[10] secure logins, file transfers (scp, sftp) and port forwarding"
"370",,"0",,"0","securecast1, outgoing packets to NAI's SecureCast servers[83]As of 2000"
"427","SRVLOC","578","SRVLOC","578","Service Location Protocol (SLP)[10]"
"115",,"0",,"0","Simple File Transfer Protocol[10][65]"
"153",,"0",,"0","Simple Gateway Monitoring Protocol (SGMP), a protocol for remote inspection and alteration of gateway management information[73]"
"25","MAIL_SMTP","568",,"0","Simple Mail Transfer Protocol (SMTP),[10][24] used for email routing between mail servers"
"161","SNMP","573","SNMP","573","Simple Network Management Protocol (SNMP)[75][citation needed][10]"
"162","SNMP","573","SNMP","573","Simple Network Management Protocol Trap (SNMPTRAP)[75][76][citation needed]"
"444",,"0",,"0","Simple Network Paging Protocol (SNPP), RFC 1568"
"199","SMUX","569",,"0","SNMP multiplexing protocol (SMUX)[80][81][importance?]"
"783",,"0",,"0","SpamAssassin spamd daemon"
"156",,"0",,"0","Structured Query Language (SQL) Service[jargon]"
"118",,"0",,"0","Structured Query Language (SQL) Services[jargon]"
"641",,"0",,"0","SupportSoft Nexus Remote Command (control/listening), a proxy gateway connecting remote control traffic"
"653",,"0",,"0","SupportSoft Nexus Remote Command (data), a proxy gateway connecting remote control traffic"
"514","RSH","518","SYSLOG","589","Syslog,[10] used for system logging"
"49","TACPLUS","593","Tacacs+","592","TACACS Login Host protocol.[32] TACACS+, still in draft which is an improved but dinstinct version of TACACS, only uses TCP 49.[33]"
"517",,"0",,"0","Talk"
"1",,"0",,"0","TCP Port Service Multiplexer (TCPMUX). Historic. Both TCP and UDP have been assigned to TCPMUX by IANA,[1] but by design only TCP is specified.[6]"
"475",,"0",,"0","tcpnethaspsrv, Aladdin Knowledge Systems Hasp services"
"754",,"0",,"0","tell send"
"992","SSL","678",,"0","Telnet protocol over TLS/SSL"
"23","Telnet","602",,"0","Telnet protocol—unencrypted text communications[10][23]"
"300",,"0",,"0","ThinLinc Web Access"
"1010",,"0",,"0","ThinLinc web-based administration interface[114]"
"37","TIME","607","TIME","607","Time Protocol[25]"
"525",,"0","TSP","618","Timed, Timeserver"
"655",,"0",,"0","Tinc VPN daemon"
"712",,"0",,"0","Topology Broadcast based on Reverse-Path Forwarding routing protocol (TBRPF; RFC 3684)"
"82",,"0",,"0","TorPark control[verification needed]"
"81",,"0",,"0","TorPark onion routing[verification needed]"
"69",,"0",,"0","Trivial File Transfer Protocol (TFTP)[10][40][41][42]"
"604",,"0",,"0","TUNNEL profile,[90] a protocol for BEEP peers to form an application layer tunnel"
"862",,"0",,"0","TWAMP control (RFC 5357)"
"388",,"0",,"0","Unidata LDM near real-time data distribution protocol"
"401",,"0",,"0","Uninterruptible power supply (UPS)"
"540",,"0",,"0","Unix-to-Unix Copy Protocol (UUCP)"
"465",,"0",,"0","URL Rendezvous Directory for SSM (Cisco protocol)[importance?]"
"753",,"0",,"0","userreg_server, Kerberos userreg server"
"117",,"0",,"0","UUCP Mapping Project (path service)[citation needed]"
"690",,"0",,"0","Velneo Application Transfer Protocol (VATP)"
"902",,"0",,"0","VMware ESXi[107][108]"
"903",,"0",,"0","VMware ESXi[107][108]"
"9",,"0",,"0","Wake-on-LAN[13]"
"513","RLOGIN","509","WHO","651","Who[87]"
"43","WHOIS","652",,"0","WHOIS protocol[29][30][31]"
"99",,"0",,"0","WIP message protocol[verification needed]"
"177",,"0","XDMCP","666","X Display Manager Control Protocol (XDMCP), used for remote logins to an X Display Manager server[77]"
"56",,"0",,"0","Xerox Network Systems (XNS) Authentication Protocol. Despite this port being assigned by IANA, the service is meant to work on SPP (ancestor of IPX/SPX), instead of TCP/IP.[36]"
"54",,"0",,"0","Xerox Network Systems (XNS) Clearinghouse (Name Server). Despite this port being assigned by IANA, the service is meant to work on SPP (ancestor of IPX/SPX), instead of TCP/IP.[36]"
"58",,"0",,"0","Xerox Network Systems (XNS) Mail. Despite this port being assigned by IANA, the service is meant to work on SPP (ancestor of IPX/SPX), instead of TCP/IP.[36]"
"52",,"0",,"0","Xerox Network Systems (XNS) Time Protocol. Despite this port being assigned by IANA, the service is meant to work on SPP (ancestor of IPX/SPX), instead of TCP/IP.[36]"
"1023",,"0",,"0","z/OS Network File System (NFS) (potentially ports 991–1023)[115]"
