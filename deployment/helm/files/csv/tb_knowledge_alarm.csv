"knowledge_alarm_id","alarm_name","attack_type","attack_time","relation_tag_id","exclude_tag_id","black_list","remark","id"
"100002","扫描行为","1","最初入口","[]","[]","61","扫描爆破行为","2"
"100003","Web漏洞利用","2","最初入口","[]","[]","91","常见web漏洞攻击","3"
"100004","后门程序","3","持续性控制","[]","[]","92","服务器中有webshell连接、反弹shell行为","4"
"100005","远控木马","3","持续性控制","[]","[]","81","常见远控木马会话","5"
"100006","挖矿病毒","3","持续性控制","[]","[]","91","挖矿协议、挖矿地址","6"
"100007","APT攻击","3","持续性控制","[]","[]","95","常见APT攻击","7"
"120008","隧道代理","4","命令控制","[]","[]","91","搭建内网隧道","8"
"100009","违规外联","6","其他","[]","[]","62","服务器存在违规外联会话行为","9"
"100010","隐蔽隧道","4","命令控制","[]","[]","81","建立隐蔽隧道会话","10"
"100011","横向渗透","5","横向权限扩展/执行攻击","[]","[]","92","用于告警服务器失陷后，黑客进行的内网横向攻击行为","11"
"100012","域内扫描","5","查看信息","[]","[]","81","爆破域内账号密码、服务行为","12"
"100013","域内攻击","5","合法凭证访问/执行攻击","[]","[]","91","利用kerberos协议漏洞进行的一系列攻击","13"
"100014","域后渗透","5","持续性控制","[]","[]","91","用于告警域控制器/域管理员失陷后，黑客进行的一系列攻击","14"
"100015","证书异常","6","其他","[]","[]","81","使用证书存在异常情况","15"
"110101","下班时间未关机","0","其他","[]","[]","61","内网客户端机器在非工作时间对外发起网络请求","16"
"110102","内外网混用","0","其他","[]","[]","61","内网客户端机器同时访问内网服务和外网服务","17"
"100016","弱口令","2","最初入口","[]","[]","81","使用弱口令登录且登录成功","18"
"10001","可疑域名请求","1",,"503,402,502",,"50",,"19"
"30011","非法Mac 攻击者","1",,"[30011]","[]","100","MAC不在预设的列表中","20"
"30012","非法Mac 被攻击者","1",,"[30012]","[]","80","MAC不在预设的列表中","21"
"33014","非法Mac 被控主机","1",,"[33014]","[]","80","MAC不在预设的列表中","22"
"30021","非法IP范围 攻击者","1",,"[30021]","[]","100","IP/MAC关系不在预设的列表中","23"
"30022","非法IP范围 被攻击者","1",,"[30022]","[]","80","IP/MAC关系不在预设的列表中","24"
"30024","非法IP范围 被控主机","1",,"[30024]","[]","80","IP/MAC关系不在预设的列表中","25"
"30031","ARP非法Mac 攻击者","1",,"[30031]","[]","100","未启用","26"
"30032","ARP非法Mac 被攻击者","1",,"[30032]","[]","100","未启用","27"
"30034","ARP非法Mac 被控主机","1",,"[30034]","[]","100","未启用","28"
"30041","ARP劫持 攻击者","2",,"[30041]","[]","100","ARP协议中回应的IP地址与实际不匹配","29"
"30042","ARP劫持 被攻击者","2",,"[30042]","[]","100","ARP协议中回应的IP地址与实际不匹配","30"
"30044","ARP劫持 被控主机","2",,"[30044]","[]","100","ARP协议中回应的IP地址与实际不匹配","31"
"30051","ARP Cache 缓存攻击 攻击者","0",,"[30051]","[]","100","大量发送ARP响应报文，且关联IP变化-- 未启用","32"
"30052","ARP Cache 缓存攻击 被攻击者","0",,"[30052]","[]","100","大量发送ARP响应报文，且关联IP变化-- 未启用","33"
"30054","ARP Cache 缓存攻击 被控主机","0",,"[30054]","[]","100","大量发送ARP响应报文，且关联IP变化-- 未启用","34"
"30061","端口扫描--横向移动 攻击者","0",,"[30061]","[]","100","外部IP访问当前网络中未开放的端口","35"
"30063","端口扫描--拒绝服务 攻击者","0",,"[30063]","[]","50","外部IP访问当前网络中未开放的端口","36"
"30062","端口扫描--横向移动 被攻击者","0",,"[30062]","[]","60","IP被扫描","37"
"30064","端口扫描--横向移动 被控主机","0",,"[30064]","[]","60","IP被扫描","38"
"30071","异常服务 攻击者","2",,"[30071]","[]","100","当前网络中发现非预设端口服务的流量","39"
"30072","异常服务 被攻击者","2",,"[30072]","[]","70","当前网络中发现非预设端口服务的流量","40"
"30074","异常服务 被控主机","2",,"[30074]","[]","70","当前网络中发现非预设端口服务的流量","41"
"30081","ARP协议格式错误 攻击者","4",,"[30081]","[]","80","ARP协议格式与标准协议格式不匹配","42"
"30082","异常协议 被攻击者","0",,"[30082]","[]","90",,"43"
"30084","异常协议 被控主机","0",,"[30084]","[]","90",,"44"
"30091","隐藏信道 攻击者","0",,"[30091]","[]","100","不启用，由其他告警代替","45"
"30092","隐藏信道 被攻击者","0",,"[30092]","[]","100",,"46"
"30094","隐藏信道 被控主机","0",,"[30094]","[]","100",,"47"
"30101","DDOS-TCP 攻击者","0",,"[30101]","[]","100","发现TCP-SYN的DDOS攻击，TCP-SYN数据包占比过大","48"
"30102","DDOS-TCP 被攻击者","0",,"[30102]","[]","50","发现TCP-SYN的DDOS攻击，TCP-SYN数据包占比过大","49"
"30104","DDOS-TCP 被控主机","0",,"[30104]","[]","50","发现TCP-SYN的DDOS攻击，TCP-SYN数据包占比过大","50"
"30111","DDOS-UDP 攻击者","0",,"[30111]","[]","100","利用UDP协议进行DDOS攻击，UDP发送/接收比例悬殊","51"
"30112","DDOS-UDP 被攻击者","0",,"[30112]","[]","50","利用UDP协议进行DDOS攻击，UDP发送/接收比例悬殊","52"
"30114","DDOS-UDP 被控主机","0",,"[30114]","[]","50","利用UDP协议进行DDOS攻击，UDP发送/接收比例悬殊","53"
"30121","DDOS-ICMP 攻击者","0",,"[30121]","[]","100","利用ICMP协议进行DDOS攻击，ICMP发送/接收比例悬殊","54"
"30122","DDOS-ICMP 被攻击者","0",,"[30122]","[]","50","利用ICMP协议进行DDOS攻击，ICMP发送/接收比例悬殊","55"
"30124","DDOS-ICMP 被控主机","0",,"[30124]","[]","50","利用ICMP协议进行DDOS攻击，ICMP发送/接收比例悬殊","56"
"30131","DDOS-DNS 攻击者","0",,"[30131]","[]","100","利用DNS协议进行DDOS攻击，DNS发送/接收比例悬殊","57"
"30132","DDOS-DNS 被攻击者","0",,"[30132]","[]","50","利用DNS协议进行DDOS攻击，DNS发送/接收比例悬殊","58"
"30134","DDOS-DNS 被控主机","0",,"[30134]","[]","50","利用DNS协议进行DDOS攻击，DNS发送/接收比例悬殊","59"
"30141","Ping of Death 攻击者","0",,"[30141]","[]","100","未启用-- ping to death","60"
"30142","Ping of Death 被攻击者","0",,"[30142]","[]","80",,"61"
"30144","Ping of Death 被控主机","0",,"[30144]","[]","80",,"62"
"30151","单播 攻击者","0",,"[30151]","[]","100","未启用","63"
"30152","单播 被攻击者","0",,"[30152]","[]","50",,"64"
"30154","单播 被控主机","0",,"[30154]","[]","50",,"65"
"30161","多播 攻击者","0",,"[30161]","[]","100","未启用","66"
"30162","多播 被攻击者","0",,"[30162]","[]","50",,"67"
"30164","多播 被控主机","0",,"[30164]","[]","50",,"68"
"30171","广播 攻击者","4",,"[30171]","[]","80","Mac广播数据包占比过大","69"
"30172","广播 被攻击者","0",,"[30172]","[]","50",,"70"
"30174","广播 被控主机","0",,"[30174]","[]","50",,"71"
"30181","ARP 风暴 攻击者","2",,"[30181]","[]","100","ARP发送/接收比例悬殊","72"
"30182","ARP 风暴 被攻击者","0",,"[30182]","[]","70",,"73"
"30184","ARP 风暴 被控主机","0",,"[30184]","[]","70",,"74"
"30191","LLDP 风暴 攻击者","4",,"[30191]","[]","80","LLDP发送/接收比例悬殊","75"
"30192","LLDP 风暴 被攻击者","0",,"[30192]","[]","50",,"76"
"30194","LLDP 风暴 被控主机","0",,"[30194]","[]","50",,"77"
"30201","IP分片 风暴 攻击者","4",,"[30201]","[]","80","IP分片包占比过大","78"
"30202","IP分片 风暴 被攻击者","0",,"[30202]","[]","50",,"79"
"30204","IP分片 风暴 被控主机","0",,"[30204]","[]","50",,"80"
"30211","IP Checksum 风暴 攻击者","4",,"[30211]","[]","80","IP校验错误数据包占比过大","81"
"30212","IP Checksum 风暴 被攻击者","0",,"[30212]","[]","70",,"82"
"30214","IP Checksum 风暴 被控主机","0",,"[30214]","[]","70",,"83"
"30221","IGMP 风暴 攻击者","4",,"[30221]","[]","80","IGMP发送/接收比例悬殊","84"
"30222","IGMP 风暴 被攻击者","0",,"[30222]","[]","60",,"85"
"30224","IGMP 风暴 被控主机","0",,"[30224]","[]","60",,"86"
"30231","TCP FIN/RST 标识 风暴 攻击者","0",,"[30231]","[]","100","FIN/RST包占比过大","87"
"30232","TCP FIN/RST 标识 风暴 被攻击者","0",,"[30232]","[]","50","FIN/RST包占比过大","89"
"30234","TCP FIN/RST 标识 风暴 被控主机","0",,"[30234]","[]","50",,"90"
"30241","未知MAC 攻击者","0",,"[30241]","[]","100","未启用-- 网络中出现新Mac，单包数超过基线后，出现新的Mac地址，出现此报警，一个分析周期产生一个报警","91"
"30242","未知MAC 被攻击者","0",,"[30242]","[]","90",,"92"
"30244","未知MAC 被控主机","0",,"[30244]","[]","90",,"93"
"30251","虚假Mac 攻击者","0",,"[30251]","[]","100","未启用-- 虚假Mac，在一个分析周期内，频繁出现新的Mac","94"
"30252","虚假Mac 被攻击者","0",,"[30252]","[]","90",,"95"
"30254","虚假Mac 被控主机","0",,"[30254]","[]","90",,"96"
"30261","未知IP网段 攻击者","0",,"[30261]","[]","20","未启用-- 内网出现新的IP网段，单包数超过基线后，出现新的IP网段，出现此报警，一个分析周期产生一个报警","97"
"30262","未知IP网段 被攻击者","0",,"[30262]","[]","90",,"98"
"30264","未知IP网段 被控主机","0",,"[30264]","[]","90",,"99"
"30271","虚假IP 攻击者","0",,"[30271]","[]","100","未启用-- 虚假IP，在一个分析周期内，频繁出现新的IP或网段","100"
"30272","虚假IP 被攻击者","0",,"[30272]","[]","70",,"101"
"30274","虚假IP 被控主机","0",,"[30274]","[]","70",,"102"
"30281","无IP 攻击者","0",,"[30281]","[]","0",,"103"
"30282","无IP 被攻击者","0",,"[30282]","[]","0",,"104"
"30284","无IP 被控主机","0",,"[30284]","[]","0",,"105"
"30291","异常IP分片 攻击者","2",,"[30291]","[]","100","IP分片格式异常，偏移量超过阈值","106"
"30292","异常IP分片 被攻击者","0",,"[30292]","[]","80",,"107"
"30294","异常IP分片 被控主机","0",,"[30294]","[]","80",,"108"
"30301","Ping To Death 攻击者","2",,"[30301]","[]","100","超过合法长度的ICMP数据包","109"
"30302","Ping To Death 被攻击者","0",,"[30302]","[]","80",,"110"
"30304","Ping To Death 被控主机","0",,"[30304]","[]","80",,"111"
"30311","UDP Checksum 风暴 攻击者","4",,"[30311]","[]","80","UDP校验错误数据包占比过大","112"
"30312","UDP Checksum 风暴 被攻击者","0",,"[30312]","[]","80",,"113"
"30314","UDP Checksum 风暴 被控主机","0",,"[30314]","[]","80",,"114"
"30321","Mac地址为零 攻击者","4",,"[30321]","[]","80","ETH协议中MAC地址为零","115"
"30331","伪造Mac 攻击者","2",,"[30331]","[]","100","ARP协议中声明的MAC地址与实际发生方不一致","118"
"30332","伪造Mac 被攻击者","0",,"[30332]","[]","80",,"119"
"30334","伪造Mac 被控主机","0",,"[30334]","[]","80",,"120"
"35001","规则-[实际的规则名称]","0",,"[35001]","[]","0","【显示规则说明】","121"
"33000","模型-[模型名称]","0",,"[33000]","[]","0","【模型说明】","122"
"30322","隐蔽信道-DNS","5",,"[30322]","[]","100",,"123"
"30323","隐蔽信道-ICMP","5",,"[30323]","[]","100",,"124"
"30324","隐蔽信道-SSL","5",,"[30324]","[]","100",,"125"
"30325","隐蔽信道-其它","5",,"[30325]","[]","100",,"126"
"30326","翻墙软件","6",,"[30326]","[]","50",,"127"
"33001","APT29","5",,"[33001]","[]","100",,"128"
"33002","海莲花","5",,"[33002]","[]","100",,"129"
"33003","白象","5",,"[33003]","[]","100",,"130"
"33004","恶意加密流量","5",,"[33004]","[]","100",,"131"
"33005","可疑SSL会话","5",,"[33005]","[]","80",,"132"
"33006","HTTP格式异常","7",,"[33006]","[]","80",,"133"
"33007","HTTP负载异常","7",,"[33007]","[]","50",,"134"
"33008","VPN","6",,"[33008]","[]","50",,"135"
"100017","尝试挖矿连接","3","持续性控制","[]","[]","80",,"136"
"120044","指纹随机化访问服务端","2",,"[]","[]","81","指纹随机化访问客户端","137"
"100018","加密类恶意软件通信","4","持续性控制","[]","[]","80",,"931"
"100019","加密隐蔽隧道通信","4","持续性控制","[]","[]","81",,"932"
"100020","黑客工具","4","持续性控制","[]","[]","81",,"933"
"100021","加密通道攻击行为","4","持续性控制","[]","[]","81",,"934"
"100022","加密类APT恶意软件通信","4","持续性控制","[]","[]","81",,"935"
"100023","恶意加密应用","4","持续性控制","[]","[]","81",,"936"
"100024","蚁剑","4","持续性控制","[]","[]","81",,"937"
