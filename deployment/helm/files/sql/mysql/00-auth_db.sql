SET NAMES utf8mb4 COLLATE utf8mb4_0900_ai_ci;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 用户表
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码，加密存储',
  `display_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '显示名',
  `group_id` int DEFAULT NULL COMMENT '用户群组ID',
  `status` int DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`user_id`) USING BTREE,
  UNIQUE KEY `uk_username` (`username`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- 角色表
-- ----------------------------
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色名称',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '角色描述',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `created_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '最后修改人',
  `last_modified_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_role_name` (`name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- 权限表
-- ----------------------------
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '权限编码',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '权限名称',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '权限描述',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `created_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '最后修改人',
  `last_modified_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_permission_code` (`code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- 用户角色关联表
-- ----------------------------
DROP TABLE IF EXISTS `user_role`;
CREATE TABLE `user_role` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_user_role` (`user_id`, `role_id`) USING BTREE,
  KEY `fk_role_id` (`role_id`) USING BTREE,
  CONSTRAINT `fk_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_role_id` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户角色关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- 角色权限关联表
-- ----------------------------
DROP TABLE IF EXISTS `role_permission`;
CREATE TABLE `role_permission` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `permission_id` bigint NOT NULL COMMENT '权限ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`) USING BTREE,
  KEY `fk_permission_id` (`permission_id`) USING BTREE,
  CONSTRAINT `fk_role_id_rp` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_permission_id` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色权限关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- 远程密钥表
-- ----------------------------
DROP TABLE IF EXISTS `user_remote_key`;
CREATE TABLE `user_remote_key` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `api_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'API密钥',
  `created_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_api_key` (`api_key`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '远程密钥表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- 初始数据
-- ----------------------------

-- 初始化管理员用户 (密码: admin123)
INSERT INTO `users` (`username`, `password`, `display_name`, `status`, `created_at`, `updated_at`)
VALUES ('admin', '$2a$12$8uQqxP9NK7QKqiHGnLsiNOGsIq.ZJ3r7Q.pQCJVOHdz1ZpvFh6NHi', '系统管理员', 1, NOW(), NOW());

-- 初始化root用户 (密码: root123)
INSERT INTO `users` (`username`, `password`, `display_name`, `status`, `created_at`, `updated_at`)
VALUES ('root', '$2a$12$d4ac9c9e9e2ba7b067e70ad9e0741fa7', '服务器管理员', 1, NOW(), NOW());

-- 初始化角色
INSERT INTO `roles` (`name`, `description`, `created_by`, `created_date`)
VALUES ('ADMIN', '系统管理员', 'system', NOW());

INSERT INTO `roles` (`name`, `description`, `created_by`, `created_date`)
VALUES ('USER', '普通用户', 'system', NOW());

-- 初始化权限
INSERT INTO `permissions` (`code`, `name`, `description`, `created_by`, `created_date`)
VALUES ('user:view', '查看用户', '查看用户信息权限', 'system', NOW());

INSERT INTO `permissions` (`code`, `name`, `description`, `created_by`, `created_date`)
VALUES ('user:create', '创建用户', '创建新用户权限', 'system', NOW());

INSERT INTO `permissions` (`code`, `name`, `description`, `created_by`, `created_date`)
VALUES ('user:update', '更新用户', '更新用户信息权限', 'system', NOW());

INSERT INTO `permissions` (`code`, `name`, `description`, `created_by`, `created_date`)
VALUES ('user:delete', '删除用户', '删除用户权限', 'system', NOW());

INSERT INTO `permissions` (`code`, `name`, `description`, `created_by`, `created_date`)
VALUES ('role:view', '查看角色', '查看角色信息权限', 'system', NOW());

INSERT INTO `permissions` (`code`, `name`, `description`, `created_by`, `created_date`)
VALUES ('role:create', '创建角色', '创建新角色权限', 'system', NOW());

INSERT INTO `permissions` (`code`, `name`, `description`, `created_by`, `created_date`)
VALUES ('role:update', '更新角色', '更新角色信息权限', 'system', NOW());

INSERT INTO `permissions` (`code`, `name`, `description`, `created_by`, `created_date`)
VALUES ('role:delete', '删除角色', '删除角色权限', 'system', NOW());

-- 为管理员角色分配所有权限
INSERT INTO `role_permission` (`role_id`, `permission_id`)
SELECT 1, id FROM `permissions`;

-- 为普通用户角色分配查看权限
INSERT INTO `role_permission` (`role_id`, `permission_id`)
SELECT 2, id FROM `permissions` WHERE `code` LIKE '%:view';

-- 为管理员用户分配管理员角色
INSERT INTO `user_role` (`user_id`, `role_id`) VALUES (1, 1);

-- 为root用户分配管理员角色
INSERT INTO `user_role` (`user_id`, `role_id`) VALUES (2, 1);

-- 初始化远程密钥
INSERT INTO `user_remote_key` (`api_key`, `created_time`)
VALUES ('default-remote-key', NOW());

SET FOREIGN_KEY_CHECKS = 1;
