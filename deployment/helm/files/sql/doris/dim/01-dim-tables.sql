-- 使用数据库
USE nta;

-- DIM层 - IPv4维度表
CREATE TABLE IF NOT EXISTS dim_ipv4 (
  ip IPV4 NOT NULL COMMENT "IPv4地址",
  city VARCHAR(50) REPLACE COMMENT "城市",
  country VARCHAR(50) REPLACE COMMENT "国家",
  country_code VARCHAR(10) REPLACE COMMENT "国家代码",
  province VARCHAR(50) REPLACE COMMENT "省份/州",
  latitude DOUBLE REPLACE COMMENT "纬度",
  longitude DOUBLE REPLACE COMMENT "经度",
  postal_code VARCHAR(20) REPLACE COMMENT "邮政编码",
  continent VARCHAR(50) REPLACE COMMENT "大洲",
  asn VARCHAR(20) REPLACE COMMENT "自治系统编号",
  asn_org VARCHAR(255) REPLACE COMMENT "自治系统组织",
  is_internal BOOLEAN REPLACE DEFAULT FALSE COMMENT "是否为内网IP",
  threat_score TINYINT REPLACE COMMENT "威胁评分",
  trust_score TINYINT REPLACE COMMENT "可信评分",
  remark STRING REPLACE COMMENT "备注",
  total_bytes BIGINT SUM COMMENT "总流量",
  total_packets BIGINT SUM COMMENT "总包数",
  recv_total_bytes BIGINT SUM COMMENT "接收总流量",
  send_total_bytes BIGINT SUM COMMENT "发送总流量",
  session_count INT SUM COMMENT "会话次数",
  first_seen DATETIME MIN COMMENT "首次出现时间",
  last_seen DATETIME MAX COMMENT "最后出现时间",
  total_duration BIGINT SUM COMMENT "总会话持续时长(毫秒)",
  create_time DATETIME MIN COMMENT "记录创建时间",
  update_time DATETIME REPLACE COMMENT "记录更新时间"
)
AGGREGATE KEY(ip)
DISTRIBUTED BY HASH(ip) BUCKETS 10
PROPERTIES(
  "replication_num" = "3"
);

-- DIM层 - IPv6维度表
CREATE TABLE IF NOT EXISTS dim_ipv6 (
  ip IPV6 NOT NULL COMMENT "IPv6地址",
  city VARCHAR(50) REPLACE COMMENT "城市",
  country VARCHAR(50) REPLACE COMMENT "国家",
  country_code VARCHAR(10) REPLACE COMMENT "国家代码",
  province VARCHAR(50) REPLACE COMMENT "省份/州",
  latitude DOUBLE REPLACE COMMENT "纬度",
  longitude DOUBLE REPLACE COMMENT "经度",
  postal_code VARCHAR(20) REPLACE COMMENT "邮政编码",
  continent VARCHAR(50) REPLACE COMMENT "大洲",
  asn VARCHAR(20) REPLACE COMMENT "自治系统编号",
  asn_org VARCHAR(255) REPLACE COMMENT "自治系统组织",
  is_internal BOOLEAN REPLACE DEFAULT FALSE COMMENT "是否为内网IP",
  threat_score TINYINT REPLACE COMMENT "威胁评分",
  trust_score TINYINT REPLACE COMMENT "可信评分",
  remark STRING REPLACE COMMENT "备注",
  total_bytes BIGINT SUM COMMENT "总流量",
  total_packets BIGINT SUM COMMENT "总包数",
  recv_total_bytes BIGINT SUM COMMENT "接收总流量",
  send_total_bytes BIGINT SUM COMMENT "发送总流量",
  session_count INT SUM COMMENT "会话次数",
  first_seen DATETIME MIN COMMENT "首次出现时间",
  last_seen DATETIME MAX COMMENT "最后出现时间",
  total_duration BIGINT SUM COMMENT "总会话持续时长(毫秒)",
  create_time DATETIME MIN COMMENT "记录创建时间",
  update_time DATETIME REPLACE COMMENT "记录更新时间"
)
AGGREGATE KEY(ip)
DISTRIBUTED BY HASH(ip) BUCKETS 10
PROPERTIES(
  "replication_num" = "3"
);

-- Materialized View for dim_ipv4 with calculated avg_bps
CREATE MATERIALIZED VIEW IF NOT EXISTS dim_ipv4_mv
AS
SELECT
  *,
  IF(
    total_duration IS NOT NULL AND total_duration > 0 AND total_bytes IS NOT NULL,
    CAST(((total_bytes * 8 * 1000) / total_duration) AS BIGINT),
    0
  ) AS avg_bps
FROM dim_ipv4;

-- Materialized View for dim_ipv6 with calculated avg_bps
CREATE MATERIALIZED VIEW IF NOT EXISTS dim_ipv6_mv
AS
SELECT
  *,
  IF(
    total_duration IS NOT NULL AND total_duration > 0 AND total_bytes IS NOT NULL,
    CAST(((total_bytes * 8 * 1000) / total_duration) AS BIGINT),
    0
  ) AS avg_bps
FROM dim_ipv6;

-- DIM层 - 域名维度表
CREATE TABLE IF NOT EXISTS dim_domain (
  domain VARCHAR(255) NOT NULL COMMENT "域名地址",
  alexa_rank INT REPLACE COMMENT "Alexa排名",
  threat_score TINYINT REPLACE COMMENT "威胁评分",
  trust_score TINYINT REPLACE COMMENT "可信评分",
  remark STRING REPLACE COMMENT "备注",
  whois STRING REPLACE COMMENT "WHOIS信息",
  total_bytes BIGINT SUM COMMENT "总流量",
  total_queries BIGINT SUM COMMENT "总查询次数",
  unique_client_ips HLL HLL_UNION COMMENT "独立客户端IP的HLL集合",
  first_seen DATETIME MIN COMMENT "首次出现时间",
  last_seen DATETIME MAX COMMENT "最后出现时间",
  create_time DATETIME MIN COMMENT "记录创建时间",
  update_time DATETIME REPLACE COMMENT "记录更新时间"
)
AGGREGATE KEY(domain)
DISTRIBUTED BY HASH(domain) BUCKETS 10
PROPERTIES(
  "replication_num" = "3"
);

-- DIM层 - MAC地址维度表
CREATE TABLE IF NOT EXISTS dim_mac (
  mac VARCHAR(17) NOT NULL COMMENT "MAC地址",
  vendor VARCHAR(255) REPLACE COMMENT "厂商名称",
  oui VARCHAR(8) REPLACE COMMENT "OUI（前3个字节）",
  is_locally_administered BOOLEAN REPLACE DEFAULT FALSE COMMENT "是否为本地管理地址",
  is_multicast BOOLEAN REPLACE DEFAULT FALSE COMMENT "是否为组播地址",
  is_potentially_randomized BOOLEAN REPLACE DEFAULT FALSE COMMENT "是否可能为随机MAC地址",
  is_virtualized BOOLEAN REPLACE DEFAULT FALSE COMMENT "是否为虚拟化厂商",
  vlan_info VARCHAR(100) REPLACE COMMENT "VLAN信息",
  threat_score TINYINT REPLACE COMMENT "威胁评分",
  trust_score TINYINT REPLACE COMMENT "可信评分",
  remark STRING REPLACE COMMENT "备注",
  total_bytes BIGINT SUM COMMENT "总流量",
  session_count BIGINT SUM COMMENT "会话次数",
  total_duration BIGINT SUM COMMENT "总会话持续时长(毫秒)",
  first_seen DATETIME MIN COMMENT "首次出现时间",
  last_seen DATETIME MAX COMMENT "最后出现时间",
  create_time DATETIME MIN COMMENT "记录创建时间",
  update_time DATETIME REPLACE COMMENT "记录更新时间"
)
AGGREGATE KEY(mac)
DISTRIBUTED BY HASH(mac) BUCKETS 10
PROPERTIES(
  "replication_num" = "3"
);

-- Materialized View for dim_mac with calculated avg_bps
CREATE MATERIALIZED VIEW IF NOT EXISTS dim_mac_mv
AS
SELECT
  *,
  IF(
    total_duration IS NOT NULL AND total_duration > 0 AND total_bytes IS NOT NULL,
    CAST(((total_bytes * 8 * 1000) / total_duration) AS BIGINT),
    0
  ) AS avg_bps
FROM dim_mac;

-- DIM层 - 证书维度表
CREATE TABLE IF NOT EXISTS dim_cert (
  cert_sha1 VARCHAR(64) NOT NULL COMMENT "证书SHA1哈希",
  cert_md5 VARCHAR(64) REPLACE COMMENT "证书MD5哈希",
  issuer_id VARCHAR(32) REPLACE COMMENT "颁发者ID",
  subject_id VARCHAR(32) REPLACE COMMENT "主题ID",
  not_before DATETIME REPLACE COMMENT "证书生效时间",
  not_after DATETIME REPLACE COMMENT "证书过期时间",
  subject_alt_names ARRAY<STRING> REPLACE COMMENT "主题备用名称",
  issuer_alt_names ARRAY<STRING> REPLACE COMMENT "颁发者备用名称",
  key_usage VARCHAR(255) REPLACE COMMENT "密钥用途",
  extended_key_usage VARCHAR(255) REPLACE COMMENT "扩展密钥用途",
  is_ca BOOLEAN REPLACE COMMENT "是否为CA证书",
  path_length INT REPLACE COMMENT "路径长度约束",
  crl_distribution_points ARRAY<STRING> REPLACE COMMENT "CRL分发点",
  ocsp_url STRING REPLACE COMMENT "OCSP响应者URL",
  issuer_cert_url STRING REPLACE COMMENT "颁发者证书URL",
  cert_policies ARRAY<STRING> REPLACE COMMENT "证书策略",
  signature_algorithm VARCHAR(100) REPLACE COMMENT "签名算法",
  hash_algorithm VARCHAR(50) REPLACE COMMENT "哈希算法",
  authority_key_identifier STRING REPLACE COMMENT "授权密钥标识符",
  subject_key_identifier STRING REPLACE COMMENT "主题密钥标识符",
  threat_score TINYINT REPLACE COMMENT "威胁评分",
  trust_score TINYINT REPLACE COMMENT "可信评分",
  source_type TINYINT REPLACE COMMENT "来源类型",
  remark STRING REPLACE COMMENT "备注",
  first_seen DATETIME MIN COMMENT "首次出现时间",
  last_seen DATETIME MAX COMMENT "最后出现时间",
  create_time DATETIME MIN COMMENT "记录创建时间",
  update_time DATETIME REPLACE COMMENT "记录更新时间"
)
AGGREGATE KEY(cert_sha1)
DISTRIBUTED BY HASH(cert_sha1) BUCKETS 10
PROPERTIES(
  "replication_num" = "3"
);

-- DIM层 - SSL指纹维度表
CREATE TABLE IF NOT EXISTS dim_sslfingerprint (
  ja3_hash VARCHAR(64) NOT NULL COMMENT "指纹哈希",
  type TINYINT NOT NULL COMMENT "指纹类型(JA3/JA3S/HASSH等)",
  finger_desc VARCHAR(255) REPLACE COMMENT "指纹描述",
  threat_score TINYINT REPLACE COMMENT "威胁评分",
  trust_score TINYINT REPLACE COMMENT "可信评分",
  first_seen DATETIME MIN COMMENT "首次出现时间",
  last_seen DATETIME MAX COMMENT "最后出现时间",
  create_time DATETIME MIN COMMENT "记录创建时间",
  update_time DATETIME REPLACE COMMENT "记录更新时间"
)
AGGREGATE KEY(ja3_hash, type)
DISTRIBUTED BY HASH(ja3_hash) BUCKETS 10
PROPERTIES(
  "replication_num" = "3"
);

-- DIM层 - 用户代理维度表
CREATE TABLE IF NOT EXISTS dim_ua (
  id VARCHAR(64) NOT NULL COMMENT "UA哈希",
  ua STRING REPLACE COMMENT "UA字符串",
  ua_desc STRING REPLACE COMMENT "UA描述",
  browser_engine VARCHAR(50) REPLACE COMMENT "浏览器引擎",
  browser_version VARCHAR(50) REPLACE COMMENT "浏览器版本",
  is_bot TINYINT REPLACE COMMENT "是否为机器人",
  bot_name VARCHAR(100) REPLACE COMMENT "机器人名称",
  bot_category VARCHAR(50) REPLACE COMMENT "机器人类别",
  layout_engine VARCHAR(50) REPLACE COMMENT "布局引擎",
  layout_engine_version VARCHAR(50) REPLACE COMMENT "布局引擎版本",
  agent_class VARCHAR(50) REPLACE COMMENT "代理类别",
  threat_score TINYINT REPLACE COMMENT "威胁评分",
  trust_score TINYINT REPLACE COMMENT "可信评分",
  first_seen DATETIME MIN COMMENT "首次出现时间",
  last_seen DATETIME MAX COMMENT "最后出现时间",
  create_time DATETIME MIN COMMENT "记录创建时间",
  update_time DATETIME REPLACE COMMENT "记录更新时间"
)
AGGREGATE KEY(id)
DISTRIBUTED BY HASH(ua) BUCKETS 10
PROPERTIES(
  "replication_num" = "3"
);

-- DIM层 - 应用维度表
CREATE TABLE IF NOT EXISTS dim_app (
  app_name VARCHAR(100) NOT NULL COMMENT "应用名称",
  app_version VARCHAR(50) NOT NULL COMMENT "应用版本",
  threat_score TINYINT REPLACE COMMENT "威胁评分",
  trust_score TINYINT REPLACE COMMENT "可信评分",
  first_seen DATETIME MIN COMMENT "首次出现时间",
  last_seen DATETIME MAX COMMENT "最后出现时间",
  create_time DATETIME MIN COMMENT "记录创建时间",
  update_time DATETIME REPLACE COMMENT "记录更新时间"
)
AGGREGATE KEY(app_name, app_version)
DISTRIBUTED BY HASH(app_name) BUCKETS 10
PROPERTIES(
  "replication_num" = "3"
);

-- DIM层 - 组织维度表
CREATE TABLE IF NOT EXISTS dim_org (
  org_name VARCHAR(255) NOT NULL COMMENT "组织名称",
  org_desc STRING REPLACE COMMENT "组织描述",
  threat_score TINYINT REPLACE COMMENT "威胁评分",
  trust_score TINYINT REPLACE COMMENT "可信评分",
  remark STRING REPLACE COMMENT "备注",
  first_seen DATETIME MIN COMMENT "首次出现时间",
  last_seen DATETIME MAX COMMENT "最后出现时间",
  create_time DATETIME MIN COMMENT "记录创建时间",
  update_time DATETIME REPLACE COMMENT "记录更新时间"
)
AGGREGATE KEY(org_name)
DISTRIBUTED BY HASH(org_name) BUCKETS 10
PROPERTIES(
  "replication_num" = "3"
);

-- DIM层 - URL维度表
CREATE TABLE IF NOT EXISTS dim_url (
  id VARCHAR(255) NOT NULL COMMENT "URL唯一标识",
  url STRING REPLACE COMMENT "URL字符串",
  threat_score TINYINT REPLACE COMMENT "威胁评分",
  trust_score TINYINT REPLACE COMMENT "可信评分",
  first_seen DATETIME MIN COMMENT "首次出现时间",
  last_seen DATETIME MAX COMMENT "最后出现时间",
  create_time DATETIME MIN COMMENT "记录创建时间",
  update_time DATETIME REPLACE COMMENT "记录更新时间"
)
AGGREGATE KEY(id)
DISTRIBUTED BY HASH(url) BUCKETS 10
PROPERTIES(
  "replication_num" = "3"
);

-- DIM层 - 基础域名维度表
CREATE TABLE IF NOT EXISTS dim_base_domain (
  base_domain VARCHAR(255) NOT NULL COMMENT "基础域名",
  threat_score TINYINT REPLACE COMMENT "威胁评分",
  trust_score TINYINT REPLACE COMMENT "可信评分",
  first_seen DATETIME MIN COMMENT "首次出现时间",
  last_seen DATETIME MAX COMMENT "最后出现时间",
  create_time DATETIME MIN COMMENT "记录创建时间",
  update_time DATETIME REPLACE COMMENT "记录更新时间"
)
AGGREGATE KEY(base_domain)
DISTRIBUTED BY HASH(base_domain) BUCKETS 10
PROPERTIES(
  "replication_num" = "3"
);

-- DIM层 - 证书颁发者维度表
CREATE TABLE IF NOT EXISTS dim_issuer (
  issuer_id VARCHAR(32) NOT NULL COMMENT "颁发者唯一ID",
  common_name VARCHAR(255) REPLACE COMMENT "通用名称CN",
  organization VARCHAR(255) REPLACE COMMENT "组织名称O",
  country VARCHAR(10) REPLACE COMMENT "国家C",
  org_unit VARCHAR(255) REPLACE COMMENT "组织单位OU",
  state VARCHAR(128) REPLACE COMMENT "州/省ST",
  locality VARCHAR(128) REPLACE COMMENT "地区L",
  email VARCHAR(255) REPLACE COMMENT "邮箱地址E",
  serial_number VARCHAR(255) REPLACE COMMENT "序列号",
  street_address VARCHAR(255) REPLACE COMMENT "街道地址",
  postal_code VARCHAR(20) REPLACE COMMENT "邮政编码",
  business_category VARCHAR(255) REPLACE COMMENT "业务类别",
  description TEXT REPLACE COMMENT "描述",
  threat_score TINYINT REPLACE COMMENT "威胁评分",
  trust_score TINYINT REPLACE COMMENT "可信评分",
  first_seen DATETIME MIN COMMENT "首次出现时间",
  last_seen DATETIME MAX COMMENT "最后出现时间",
  create_time DATETIME MIN COMMENT "记录创建时间",
  update_time DATETIME REPLACE COMMENT "记录更新时间"
)
AGGREGATE KEY(issuer_id)
DISTRIBUTED BY HASH(issuer_id) BUCKETS 10
PROPERTIES(
  "replication_num" = "3"
);

-- DIM层 - 证书主题维度表
CREATE TABLE IF NOT EXISTS dim_subject (
  subject_id VARCHAR(32) NOT NULL COMMENT "主题唯一ID",
  common_name VARCHAR(255) REPLACE COMMENT "通用名称CN",
  organization VARCHAR(255) REPLACE COMMENT "组织名称O",
  country VARCHAR(10) REPLACE COMMENT "国家C",
  org_unit VARCHAR(255) REPLACE COMMENT "组织单位OU",
  state VARCHAR(128) REPLACE COMMENT "州/省ST",
  locality VARCHAR(128) REPLACE COMMENT "地区L",
  email VARCHAR(255) REPLACE COMMENT "邮箱地址E",
  serial_number VARCHAR(255) REPLACE COMMENT "序列号",
  street_address VARCHAR(255) REPLACE COMMENT "街道地址",
  postal_code VARCHAR(20) REPLACE COMMENT "邮政编码",
  business_category VARCHAR(255) REPLACE COMMENT "业务类别",
  description TEXT REPLACE COMMENT "描述",
  threat_score TINYINT REPLACE COMMENT "威胁评分",
  trust_score TINYINT REPLACE COMMENT "可信评分",
  first_seen DATETIME MIN COMMENT "首次出现时间",
  last_seen DATETIME MAX COMMENT "最后出现时间",
  create_time DATETIME MIN COMMENT "记录创建时间",
  update_time DATETIME REPLACE COMMENT "记录更新时间"
)
AGGREGATE KEY(subject_id)
DISTRIBUTED BY HASH(subject_id) BUCKETS 10
PROPERTIES(
  "replication_num" = "3"
);

-- DIM层 - 设备维度表
CREATE TABLE IF NOT EXISTS dim_device (
  device_name VARCHAR(100) NOT NULL COMMENT "设备名称",
  device_brand VARCHAR(100) REPLACE COMMENT "设备品牌",
  device_model VARCHAR(100) REPLACE COMMENT "设备型号",
  device_category VARCHAR(50) REPLACE COMMENT "设备类别",
  threat_score TINYINT REPLACE COMMENT "威胁评分",
  trust_score TINYINT REPLACE COMMENT "可信评分",
  first_seen DATETIME MIN COMMENT "首次出现时间",
  last_seen DATETIME MAX COMMENT "最后出现时间",
  create_time DATETIME MIN COMMENT "记录创建时间",
  update_time DATETIME REPLACE COMMENT "记录更新时间"
)
AGGREGATE KEY(device_name)
DISTRIBUTED BY HASH(device_name) BUCKETS 10
PROPERTIES(
  "replication_num" = "3"
);

-- DIM层 - 操作系统维度表
CREATE TABLE IF NOT EXISTS dim_os (
  os_name VARCHAR(100) NOT NULL COMMENT "操作系统名称",
  os_version VARCHAR(50) REPLACE COMMENT "操作系统版本",
  os_major_version VARCHAR(20) REPLACE COMMENT "操作系统主版本",
  os_minor_version VARCHAR(20) REPLACE COMMENT "操作系统次版本",
  os_class VARCHAR(50) REPLACE COMMENT "操作系统类别",
  threat_score TINYINT REPLACE COMMENT "威胁评分",
  trust_score TINYINT REPLACE COMMENT "可信评分",
  first_seen DATETIME MIN COMMENT "首次出现时间",
  last_seen DATETIME MAX COMMENT "最后出现时间",
  create_time DATETIME MIN COMMENT "记录创建时间",
  update_time DATETIME REPLACE COMMENT "记录更新时间"
)
AGGREGATE KEY(os_name)
DISTRIBUTED BY HASH(os_name) BUCKETS 10
PROPERTIES(
  "replication_num" = "3"
);

-- DIM层 - 应用服务维度表
CREATE TABLE IF NOT EXISTS dim_appservice (
  service_key VARCHAR(255) NOT NULL COMMENT "服务唯一标识",
  ip VARCHAR(50) REPLACE COMMENT "IP地址",
  app_name VARCHAR(100) REPLACE COMMENT "应用名称",
  dport INT REPLACE COMMENT "目标端口",
  ip_protocol VARCHAR(20) REPLACE COMMENT "IP协议",
  threat_score TINYINT REPLACE COMMENT "威胁评分",
  trust_score TINYINT REPLACE COMMENT "可信评分",
  first_seen DATETIME MIN COMMENT "首次出现时间",
  last_seen DATETIME MAX COMMENT "最后出现时间",
  create_time DATETIME MIN COMMENT "记录创建时间",
  update_time DATETIME REPLACE COMMENT "记录更新时间"
)
AGGREGATE KEY(service_key)
DISTRIBUTED BY HASH(service_key) BUCKETS 10
PROPERTIES(
  "replication_num" = "3"
);
