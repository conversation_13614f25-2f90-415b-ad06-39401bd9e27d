-- 使用数据库
USE nta;

-- ODS层 - VNC协议元数据表 (原始数据)
CREATE TABLE IF NOT EXISTS ods_vnc_protocol_metadata (
  -- 公共字段 (Comm_msg)
  session_id VARCHAR(64) NOT NULL COMMENT "会话ID",
  begin_time DATETIME NOT NULL COMMENT "会话起始时间",
  src_ip VARCHAR(50) NOT NULL COMMENT "源IP地址",
  dst_ip VARCHAR(50) NOT NULL COMMENT "目标IP地址",
  src_port INT COMMENT "源端口",
  dst_port INT COMMENT "目标端口",
  ippro INT COMMENT "IP协议号",
  begin_nsec INT COMMENT "会话起始时间纳秒数",
  server_ip VARCHAR(64) COMMENT "服务器IP",
  app_id INT COMMENT "应用ID",
  app_name VARCHAR(128) COMMENT "应用名称",
  thread_id INT COMMENT "线程ID",
  task_id INT COMMENT "任务ID",
  batch_id INT COMMENT "批次ID",

  -- 服务端帧缓冲区信息 (vnc_sever_fb_msg)
  fb_width INT COMMENT "帧缓冲区宽度",
  fb_height INT COMMENT "帧缓冲区高度",
  bits_per_pixel INT COMMENT "每像素位数",
  depth INT COMMENT "颜色深度",
  big_endian_flag INT COMMENT "大端标志",
  true_color_flag INT COMMENT "真彩色标志",
  red_maximum INT COMMENT "红色最大值",
  green_maximum INT COMMENT "绿色最大值",
  blue_maximum INT COMMENT "蓝色最大值",
  red_shift INT COMMENT "红色位移",
  green_shift INT COMMENT "绿色位移",
  blue_shift INT COMMENT "蓝色位移",
  desktop_name VARCHAR(255) COMMENT "桌面名称",

  -- 客户端编码设置 (vnc_client_set_encoding_msg)
  encoding_types ARRAY<STRING> COMMENT "编码类型列表",

  -- 客户端像素格式设置 (vnc_client_set_pixel_format_msg)
  client_bits_per_pixel INT COMMENT "客户端每像素位数",
  client_depth INT COMMENT "客户端颜色深度",
  client_big_endian_flag INT COMMENT "客户端大端标志",
  client_true_color_flag INT COMMENT "客户端真彩色标志",
  client_red_maximum INT COMMENT "客户端红色最大值",
  client_green_maximum INT COMMENT "客户端绿色最大值",
  client_blue_maximum INT COMMENT "客户端蓝色最大值",
  client_red_shift INT COMMENT "客户端红色位移",
  client_green_shift INT COMMENT "客户端绿色位移",
  client_blue_shift INT COMMENT "客户端蓝色位移",

  -- 协议版本信息
  server_protocol_ver VARCHAR(64) COMMENT "服务端协议版本",
  client_protocol_ver VARCHAR(64) COMMENT "客户端协议版本",

  -- 安全类型信息
  server_secure_type_supported ARRAY<STRING> COMMENT "服务端支持的安全类型",
  client_secure_type_selected VARCHAR(64) COMMENT "客户端选择的安全类型",

  -- 认证信息
  server_authentication_challenge STRING COMMENT "服务端认证挑战",
  client_authentication_response STRING COMMENT "客户端认证响应",
  authentication_result VARCHAR(64) COMMENT "认证结果",

  -- 其他信息
  share_dsktp_flag INT COMMENT "共享桌面标志",

  -- 系统字段
  create_time DATETIME DEFAULT NOW() COMMENT "记录创建时间"
)
DUPLICATE KEY(session_id, begin_time, src_ip, dst_ip, src_port, dst_port, desktop_name)
PARTITION BY RANGE(begin_time) (
  PARTITION p_default VALUES LESS THAN (MAXVALUE)
)
DISTRIBUTED BY HASH(session_id) BUCKETS 32
PROPERTIES(
  "replication_num" = "3",
  "storage_medium" = "SSD",
  "storage_cooldown_time" = "30 DAY",
  "dynamic_partition.enable" = "true",
  "dynamic_partition.time_unit" = "DAY",
  "dynamic_partition.start" = "-90",
  "dynamic_partition.end" = "3",
  "dynamic_partition.prefix" = "p",
  "dynamic_partition.buckets" = "32",
  "dynamic_partition.create_history_partition" = "true",
  "compression" = "zstd"
);
