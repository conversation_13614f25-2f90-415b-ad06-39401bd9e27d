-- 使用数据库
USE nta;

-- ODS层 - S7协议元数据表 (原始数据)
CREATE TABLE IF NOT EXISTS ods_s7_protocol_metadata (
  -- 公共字段 (Comm_msg)
  session_id VARCHAR(64) NOT NULL COMMENT "会话ID",
  begin_time DATETIME NOT NULL COMMENT "会话起始时间",
  src_ip VARCHAR(50) NOT NULL COMMENT "源IP地址",
  dst_ip VARCHAR(50) NOT NULL COMMENT "目标IP地址",
  src_port INT COMMENT "源端口",
  dst_port INT COMMENT "目标端口",
  ippro INT COMMENT "IP协议号",
  server_ip VARCHAR(64) COMMENT "服务器IP",
  app_id INT COMMENT "应用ID",
  app_name VARCHAR(128) COMMENT "应用名称",
  thread_id INT COMMENT "线程ID",
  task_id INT COMMENT "任务ID",
  begin_nsec INT COMMENT "会话起始时间纳秒数",
  batch_id INT COMMENT "批次ID",

  -- S7 specific fields (s7_msg)
  tpkt_version INT COMMENT "TPKT版本",
  cotp_type INT COMMENT "COTP类型",
  s7_type INT COMMENT "S7类型",
  s7_function INT COMMENT "S7功能",
  system_type INT COMMENT "系统类型",
  system_group_function INT COMMENT "系统组功能",
  system_sub_function INT COMMENT "系统子功能",
  dst_ref INT COMMENT "目标引用",
  src_ref INT COMMENT "源引用",
  pdu_size INT COMMENT "PDU大小",
  src_connect_type INT COMMENT "源连接类型",
  src_rack INT COMMENT "源机架",
  src_slot INT COMMENT "源槽位",
  dst_connect_type INT COMMENT "目标连接类型",
  dst_rack INT COMMENT "目标机架",
  dst_slot INT COMMENT "目标槽位",
  packet_c2s INT COMMENT "客户端到服务器的包数",

  -- 系统字段
  create_time DATETIME DEFAULT NOW() COMMENT "记录创建时间"
)
DUPLICATE KEY(session_id, begin_time, src_ip, dst_ip, src_port, dst_port, s7_function)
PARTITION BY RANGE(begin_time) (
  PARTITION p_default VALUES LESS THAN (MAXVALUE)
)
DISTRIBUTED BY HASH(session_id) BUCKETS 32
PROPERTIES(
  "replication_num" = "3",
  "storage_medium" = "SSD",
  "storage_cooldown_time" = "30 DAY",
  "dynamic_partition.enable" = "true",
  "dynamic_partition.time_unit" = "DAY",
  "dynamic_partition.start" = "-90",
  "dynamic_partition.end" = "3",
  "dynamic_partition.prefix" = "p",
  "dynamic_partition.buckets" = "32",
  "dynamic_partition.create_history_partition" = "true",
  "compression" = "zstd"
);
