-- 使用数据库
USE nta;

-- ODS层 - NTP协议元数据表 (原始数据)
CREATE TABLE IF NOT EXISTS ods_ntp_protocol_metadata (
  -- 公共字段 (Comm_msg)
  session_id VARCHAR(64) NOT NULL COMMENT "会话ID",
  begin_time DATETIME NOT NULL COMMENT "会话起始时间",
  src_ip VARCHAR(50) NOT NULL COMMENT "源IP地址",
  dst_ip VARCHAR(50) NOT NULL COMMENT "目标IP地址",
  src_port INT COMMENT "源端口",
  dst_port INT COMMENT "目标端口",
  ippro INT COMMENT "IP协议号",
  server_ip VARCHAR(64) COMMENT "服务器IP",
  app_id INT COMMENT "应用ID",
  app_name VARCHAR(128) COMMENT "应用名称",
  thread_id INT COMMENT "线程ID",
  task_id INT COMMENT "任务ID",
  begin_nsec INT COMMENT "会话起始时间纳秒数",
  batch_id INT COMMENT "批次ID",

  -- NTP特定字段 (ntp_msg)
  version INT COMMENT "NTP协议版本号",

  -- 客户端信息 (ntp_client_msg)
  client_stratum INT COMMENT "客户端跳数",
  client_poll_interval_sec INT COMMENT "客户端轮询间隔(秒)",
  client_clock_precision DOUBLE COMMENT "客户端时钟精度",
  client_root_delay DOUBLE COMMENT "客户端根延迟",
  client_root_dispersion DOUBLE COMMENT "客户端根分散",
  client_reference_identifier VARCHAR(64) COMMENT "客户端参考标识符",
  client_refer_ts_sec INT COMMENT "客户端参考时间戳(秒)",
  client_refer_ts_nsec INT COMMENT "客户端参考时间戳(纳秒)",
  client_origin_ts_sec INT COMMENT "客户端原始时间戳(秒)",
  client_origin_ts_nsec INT COMMENT "客户端原始时间戳(纳秒)",
  client_recv_ts_sec INT COMMENT "客户端接收时间戳(秒)",
  client_recv_ts_nsec INT COMMENT "客户端接收时间戳(纳秒)",
  client_xmit_ts_sec INT COMMENT "客户端传输时间戳(秒)",
  client_xmit_ts_nsec INT COMMENT "客户端传输时间戳(纳秒)",

  -- 服务端信息 (ntp_server_msg)
  server_stratum INT COMMENT "服务端跳数",
  server_poll_interval_sec INT COMMENT "服务端轮询间隔(秒)",
  server_clock_precision DOUBLE COMMENT "服务端时钟精度",
  server_root_delay DOUBLE COMMENT "服务端根延迟",
  server_root_dispersion DOUBLE COMMENT "服务端根分散",
  server_reference_identifier VARCHAR(64) COMMENT "服务端参考标识符",
  server_refer_ts_sec INT COMMENT "服务端参考时间戳(秒)",
  server_refer_ts_nsec INT COMMENT "服务端参考时间戳(纳秒)",
  server_origin_ts_sec INT COMMENT "服务端原始时间戳(秒)",
  server_origin_ts_nsec INT COMMENT "服务端原始时间戳(纳秒)",
  server_recv_ts_sec INT COMMENT "服务端接收时间戳(秒)",
  server_recv_ts_nsec INT COMMENT "服务端接收时间戳(纳秒)",
  server_xmit_ts_sec INT COMMENT "服务端传输时间戳(秒)",
  server_xmit_ts_nsec INT COMMENT "服务端传输时间戳(纳秒)",

  -- 系统字段
  create_time DATETIME DEFAULT NOW() COMMENT "记录创建时间"
)
DUPLICATE KEY(session_id, begin_time, src_ip, dst_ip, src_port, dst_port)
PARTITION BY RANGE(begin_time) (
  PARTITION p_default VALUES LESS THAN (MAXVALUE)
)
DISTRIBUTED BY HASH(session_id) BUCKETS 32
PROPERTIES(
  "replication_num" = "3",
  "storage_medium" = "SSD",
  "storage_cooldown_time" = "30 DAY",
  "dynamic_partition.enable" = "true",
  "dynamic_partition.time_unit" = "DAY",
  "dynamic_partition.start" = "-90",
  "dynamic_partition.end" = "3",
  "dynamic_partition.prefix" = "p",
  "dynamic_partition.buckets" = "32",
  "dynamic_partition.create_history_partition" = "true",
  "compression" = "zstd"
);
