-- 使用数据库
USE nta;

-- ODS层 - 网络流量会话日志表 (原始数据)
CREATE TABLE IF NOT EXISTS ods_single_session_logs (
  -- Common message fields (comm_msg)
  session_id VARCHAR(64) NOT NULL COMMENT "会话ID",
  src_ip VARCHAR(50) NOT NULL COMMENT "源IP地址",
  dst_ip VARCHAR(50) NOT NULL COMMENT "目标IP地址",
  src_port INT COMMENT "源端口",
  dst_port INT COMMENT "目标端口",
  ippro INT COMMENT "IP协议号",
  begin_time DATETIME NOT NULL COMMENT "会话开始时间",
  begin_nsec INT COMMENT "会话开始时间纳秒部分",
  end_time DATETIME COMMENT "会话结束时间",
  end_nsec INT COMMENT "会话结束时间纳秒部分",
  server_ip VARCHAR(50) COMMENT "服务器IP",
  app_id INT COMMENT "应用ID",
  app_name VARCHAR(128) COMMENT "应用名称",
  thread_id INT COMMENT "线程ID",
  task_id INT COMMENT "任务ID",
  batch_id INT COMMENT "批次ID",

  -- Session basic info (ss_basic)
  smac VARCHAR(32) COMMENT "源MAC地址",
  dmac VARCHAR(32) COMMENT "目标MAC地址",
  rule_num INT COMMENT "规则数量",
  rule_level INT COMMENT "规则级别",
  syn STRING COMMENT "SYN二进制流(Base64编码)",
  syn_ack STRING COMMENT "SYN-ACK二进制流(Base64编码)",
  rule_msg JSON COMMENT "规则消息",

  -- Session statistics (ss_stats)
  stats_stotalsign INT COMMENT "源标识",
  stats_dtotalsign INT COMMENT "目标标识",
  stats_distbytes JSON COMMENT "负载字节分布",
  stats_distbytesnum BIGINT COMMENT "负载字节数",
  stats_distcsq DOUBLE COMMENT "统计值",
  stats_distcsqt DOUBLE COMMENT "统计t值",
  stats_sdistlen ARRAY<INT> COMMENT "包负载长度分布数组",
  stats_ddistlen ARRAY<INT> COMMENT "包负载长度分布数组",
  stats_sdistdur INT COMMENT "源包时间间隔分布",
  stats_ddistdur INT COMMENT "目标包时间间隔分布",
  stats_distdur ARRAY<INT> COMMENT "包时间间隔分布数组",
  stats_prolist_num INT COMMENT "包协议栈数量",
  stats_prolist JSON COMMENT "包协议栈",
  sio_sign INT COMMENT "源IP是否为内部IP",
  dio_sign INT COMMENT "目标IP是否为内部IP",
  ext_json JSON COMMENT "JSON扩展字段",
  stats_src_mss INT COMMENT "源MSS",
  stats_dst_mss INT COMMENT "目标MSS",
  stats_src_window_scale INT COMMENT "源窗口扩展因子",
  stats_dst_window_scale INT COMMENT "目标窗口扩展因子",
  stats_spayload_maxlen INT COMMENT "源负载最大长度",
  stats_dpayload_maxlen INT COMMENT "目标负载最大长度",
  stats_sack_payload_maxlen INT COMMENT "源ACK负载最大长度",
  stats_dack_payload_maxlen INT COMMENT "目标ACK负载最大长度",
  stats_sack_payload_minlen INT COMMENT "源ACK负载最小长度",
  stats_dack_payload_minlen INT COMMENT "目标ACK负载最小长度",
  stats_tcp_info ARRAY<STRUCT<
    bytes: BIGINT,
    packet_num: INT,
    psh_num: INT,
    acknowledgement: INT,
    min_sequence: INT,
    max_sequence: INT
  >> COMMENT "TCP信息数组",
  syn_seq ARRAY<INT> COMMENT "SYN序列号数组",
  syn_seq_num INT COMMENT "SYN序列号数量",
  stats_sipid_offset ARRAY<INT> COMMENT "源IP的ID偏移数组",
  stats_dipid_offset ARRAY<INT> COMMENT "目标IP的ID偏移数组",
  block_cipher ARRAY<INT> COMMENT "SSL会话块密码数组",

  -- Session packet info (ss_pkt)
  pkt_smaxlen INT COMMENT "源最大IP包长",
  pkt_dmaxlen INT COMMENT "目标最大IP包长",
  pkt_snum INT COMMENT "源包数",
  pkt_dnum INT COMMENT "目标包数",
  pkt_spayloadnum INT COMMENT "源有负载的包数",
  pkt_dpayloadnum INT COMMENT "目标有负载的包数",
  pkt_sbytes BIGINT COMMENT "源字节数",
  pkt_dbytes BIGINT COMMENT "目标字节数",
  pkt_spayloadbytes BIGINT COMMENT "源有负载的字节数",
  pkt_dpayloadbytes BIGINT COMMENT "目标有负载的字节数",
  pkt_sfinnum INT COMMENT "源Fin包数量",
  pkt_dfinnum INT COMMENT "目标Fin包数量",
  pkt_srstnum INT COMMENT "源Rst包数量",
  pkt_drstnum INT COMMENT "目标Rst包数量",
  pkt_ssynnum INT COMMENT "源Syn包数量",
  pkt_dsynnum INT COMMENT "目标Syn包数量",
  pkt_ssynbytes INT COMMENT "源Syn包字节数",
  pkt_dsynbytes INT COMMENT "目标Syn包字节数",
  pkt_sttlmax INT COMMENT "源IP:TTL最大值",
  pkt_dttlmax INT COMMENT "目标IP:TTL最大值",
  pkt_sttlmin INT COMMENT "源IP:TTL最小值",
  pkt_dttlmin INT COMMENT "目标IP:TTL最小值",
  pkt_sdurmax INT COMMENT "源包的最大时间间隔",
  pkt_ddurmax INT COMMENT "目标包的最大时间间隔",
  pkt_sdurmin INT COMMENT "源包的最小时间间隔",
  pkt_ddurmin INT COMMENT "目标包的最小时间间隔",
  pkt_sdisorder INT COMMENT "源乱序包数",
  pkt_ddisorder INT COMMENT "目标乱序包数",
  pkt_sresend INT COMMENT "源重发包数",
  pkt_dresend INT COMMENT "目标重发包数",
  pkt_slost INT COMMENT "源丢包长度",
  pkt_dlost INT COMMENT "目标丢包长度",
  pkt_spshnum INT COMMENT "源PSH包数",
  pkt_dpshnum INT COMMENT "目标PSH包数",
  pkt_pronum INT COMMENT "包协议数量",
  pkt_unkonw_pronum INT COMMENT "未知协议包数量",
  pkt_syn_data INT COMMENT "syn包包含负载包数",
  pkt_sbadnum INT COMMENT "源错包数量",
  pkt_dbadnum INT COMMENT "目标错包数量",
  app_pkt_id INT COMMENT "第几个负载包识别到应用",
  pkt_spayload ARRAY<STRING> COMMENT "源包负载前4个",
  pkt_dpayload ARRAY<STRING> COMMENT "目标包负载前4个",
  pkt_infor ARRAY<STRUCT<
    count: INT,
    sec: INT,
    nsec: INT,
    len: INT
  >> COMMENT "包信息数组",

  -- Fingerprints
  tcp_c_finger BIGINT COMMENT "TCP客户端指纹",
  tcp_s_finger BIGINT COMMENT "TCP服务端指纹",
  http_c_finger BIGINT COMMENT "HTTP请求指纹",
  http_s_finger BIGINT COMMENT "HTTP应答指纹",
  ssl_c_finger BIGINT COMMENT "SSL请求指纹",
  ssl_s_finger BIGINT COMMENT "SSL应答指纹",

  -- TCP指纹特征字段 (tcp_finger_feature_msg)
  tcp_c_feature_ecn_ip_ect BOOLEAN COMMENT "客户端TCP指纹特征-ECN IP ECT",
  tcp_c_feature_qk_dfnz_ipid BOOLEAN COMMENT "客户端TCP指纹特征-DF设置为1时IPID值是否不为0",
  tcp_c_feature_flag_cwr BOOLEAN COMMENT "客户端TCP指纹特征-CWR标志",
  tcp_c_feature_flag_ece BOOLEAN COMMENT "客户端TCP指纹特征-ECE标志",
  tcp_c_feature_qk_opt_zero_ts1 BOOLEAN COMMENT "客户端TCP指纹特征-时间戳前四位是否为0",
  tcp_c_feature_ttl INT COMMENT "客户端TCP指纹特征-TTL值",
  tcp_c_feature_tcpopt_eol_padnum INT COMMENT "客户端TCP指纹特征-EOL填充字节数",
  tcp_c_feature_tcpopt_wscale INT COMMENT "客户端TCP指纹特征-窗口扩大因子",
  tcp_c_feature_qk_win_mss INT COMMENT "客户端TCP指纹特征-窗口值与MSS比值",
  tcp_c_feature_tcpopt_layout VARCHAR(255) COMMENT "客户端TCP指纹特征-TCP选项布局",

  tcp_s_feature_ecn_ip_ect BOOLEAN COMMENT "服务端TCP指纹特征-ECN IP ECT",
  tcp_s_feature_qk_dfnz_ipid BOOLEAN COMMENT "服务端TCP指纹特征-DF设置为1时IPID值是否不为0",
  tcp_s_feature_flag_cwr BOOLEAN COMMENT "服务端TCP指纹特征-CWR标志",
  tcp_s_feature_flag_ece BOOLEAN COMMENT "服务端TCP指纹特征-ECE标志",
  tcp_s_feature_qk_opt_zero_ts1 BOOLEAN COMMENT "服务端TCP指纹特征-时间戳前四位是否为0",
  tcp_s_feature_ttl INT COMMENT "服务端TCP指纹特征-TTL值",
  tcp_s_feature_tcpopt_eol_padnum INT COMMENT "服务端TCP指纹特征-EOL填充字节数",
  tcp_s_feature_tcpopt_wscale INT COMMENT "服务端TCP指纹特征-窗口扩大因子",
  tcp_s_feature_qk_win_mss INT COMMENT "服务端TCP指纹特征-窗口值与MSS比值",
  tcp_s_feature_tcpopt_layout VARCHAR(255) COMMENT "服务端TCP指纹特征-TCP选项布局",

  -- Other session fields
  duration INT COMMENT "会话持续时间(秒)",
  first_sender VARCHAR(50) COMMENT "首包发送方IP",
  device_id INT COMMENT "设备码",
  first_proto INT COMMENT "首层协议",
  proxy_ip VARCHAR(50) COMMENT "代理IP",
  proxy_port INT COMMENT "代理端口",
  proxy_real_host VARCHAR(255) COMMENT "代理访问的真实域名",
  proxy_type INT COMMENT "代理类型",
  handle_begin_time DATETIME COMMENT "开始处理时间",
  handle_end_time DATETIME COMMENT "结束处理时间",
  rule_labels ARRAY<INT> COMMENT "规则标签",
  port_list ARRAY<INT> COMMENT "端口列表",

  -- System fields
  create_time DATETIME DEFAULT NOW() COMMENT "记录创建时间"
)
DUPLICATE KEY(session_id, begin_time, src_ip, dst_ip, src_port, dst_port, ippro)
PARTITION BY RANGE(begin_time) (
  PARTITION p_default VALUES LESS THAN (MAXVALUE)
)
DISTRIBUTED BY HASH(session_id) BUCKETS 32
PROPERTIES(
  "replication_num" = "3",
  "storage_medium" = "SSD",
  "storage_cooldown_time" = "30 DAY",
  "dynamic_partition.enable" = "true",
  "dynamic_partition.time_unit" = "DAY",
  "dynamic_partition.start" = "-90",
  "dynamic_partition.end" = "3",
  "dynamic_partition.prefix" = "p",
  "dynamic_partition.buckets" = "32",
  "dynamic_partition.create_history_partition" = "true",
  "compression" = "zstd",
  "bloom_filter_columns" = "task_id"
);
