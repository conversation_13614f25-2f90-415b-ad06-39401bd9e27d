-- 使用数据库
USE nta;

-- ODS层 - XDMCP协议元数据表 (原始数据)
CREATE TABLE IF NOT EXISTS ods_xdmcp_protocol_metadata (
  -- 公共字段 (Comm_msg)
  session_id VARCHAR(64) NOT NULL COMMENT "会话ID",
  begin_time DATETIME NOT NULL COMMENT "会话起始时间",
  src_ip VARCHAR(50) NOT NULL COMMENT "源IP地址",
  dst_ip VARCHAR(50) NOT NULL COMMENT "目标IP地址",
  src_port INT COMMENT "源端口",
  dst_port INT COMMENT "目标端口",
  ippro INT COMMENT "IP协议号",
  server_ip VARCHAR(64) COMMENT "服务器IP",
  app_id INT COMMENT "应用ID",
  app_name VARCHAR(128) COMMENT "应用名称",
  thread_id INT COMMENT "线程ID",
  task_id INT COMMENT "任务ID",
  begin_nsec INT COMMENT "会话起始时间纳秒数",
  batch_id INT COMMENT "批次ID",

  -- XDMCP特定字段 (xdmcp_msg)
  version INT COMMENT "XDMCP版本号",
  display_number INT COMMENT "显示编号",
  xdmcp_session_id INT COMMENT "XDMCP会话ID",
  hostname VARCHAR(255) COMMENT "主机名",
  manufacture_disp_id VARCHAR(255) COMMENT "制造商显示ID",
  display_class VARCHAR(255) COMMENT "显示类别",
  status VARCHAR(255) COMMENT "状态",

  -- 连接信息 (xdmcp_connection_msg)
  connection_types ARRAY<STRING> COMMENT "连接类型",
  connection_addresses ARRAY<STRING> COMMENT "连接地址",

  -- 认证信息 (xdmcp_auth_msg)
  client_authentication_names ARRAY<STRING> COMMENT "客户端认证名称",
  client_authentication_data STRING COMMENT "客户端认证数据",
  client_authorization_names ARRAY<STRING> COMMENT "客户端授权名称",
  client_authorization_data STRING COMMENT "客户端授权数据",
  server_authentication_names ARRAY<STRING> COMMENT "服务端认证名称",
  server_authentication_data STRING COMMENT "服务端认证数据",
  server_authorization_names ARRAY<STRING> COMMENT "服务端授权名称",
  server_authorization_data STRING COMMENT "服务端授权数据",

  -- 系统字段
  create_time DATETIME DEFAULT NOW() COMMENT "记录创建时间"
)
DUPLICATE KEY(session_id, begin_time, src_ip, dst_ip, src_port, dst_port, hostname)
PARTITION BY RANGE(begin_time) (
  PARTITION p_default VALUES LESS THAN (MAXVALUE)
)
DISTRIBUTED BY HASH(session_id) BUCKETS 32
PROPERTIES(
  "replication_num" = "3",
  "storage_medium" = "SSD",
  "storage_cooldown_time" = "30 DAY",
  "dynamic_partition.enable" = "true",
  "dynamic_partition.time_unit" = "DAY",
  "dynamic_partition.start" = "-90",
  "dynamic_partition.end" = "3",
  "dynamic_partition.prefix" = "p",
  "dynamic_partition.buckets" = "32",
  "dynamic_partition.create_history_partition" = "true",
  "compression" = "zstd"
);
