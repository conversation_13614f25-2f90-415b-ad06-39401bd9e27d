-- 使用数据库
USE nta;

-- ODS层 - RDP协议元数据表 (原始数据)
CREATE TABLE IF NOT EXISTS ods_rdp_protocol_metadata (
  -- 公共字段 (Comm_msg)
  session_id VARCHAR(64) NOT NULL COMMENT "会话ID",
  begin_time DATETIME NOT NULL COMMENT "会话起始时间",
  src_ip VARCHAR(50) NOT NULL COMMENT "源IP地址",
  dst_ip VARCHAR(50) NOT NULL COMMENT "目标IP地址",
  src_port INT COMMENT "源端口",
  dst_port INT COMMENT "目标端口",
  ippro INT COMMENT "IP协议号",
  server_ip VARCHAR(64) COMMENT "服务器IP",
  app_id INT COMMENT "应用ID",
  app_name VARCHAR(128) COMMENT "应用名称",
  thread_id INT COMMENT "线程ID",
  task_id INT COMMENT "任务ID",
  begin_nsec INT COMMENT "会话起始时间纳秒数",
  batch_id INT COMMENT "批次ID",

  -- RDP客户端信息 (rdp_client_msg)
  client_version_major INT COMMENT "客户端主版本号",
  client_version_minor INT COMMENT "客户端次版本号",
  desktop_width INT COMMENT "桌面宽度",
  desktop_height INT COMMENT "桌面高度",
  color_depth INT COMMENT "颜色深度",
  sas_sequence INT COMMENT "SAS序列",
  keyboard_layout INT COMMENT "键盘布局",
  client_build INT COMMENT "客户端构建号",
  client_name VARCHAR(255) COMMENT "客户端名称",
  keyboard_type INT COMMENT "键盘类型",
  keyboard_subtype INT COMMENT "键盘子类型",
  keyboard_funckey INT COMMENT "键盘功能键",
  ime_filename VARCHAR(255) COMMENT "IME文件名",
  client_productid INT COMMENT "客户端产品ID",
  connection_type INT COMMENT "连接类型",
  encryption_methods INT COMMENT "加密方法",
  ext_encrytionmethod INT COMMENT "扩展加密方法",
  channel_count INT COMMENT "通道数量",
  request_protocols INT COMMENT "请求协议",
  rdp_c_flag INT COMMENT "RDP客户端标志",
  cookie STRING COMMENT "Cookie",

  -- RDP服务端信息 (rdp_server_msg)
  server_version_major INT COMMENT "服务端主版本号",
  server_version_minor INT COMMENT "服务端次版本号",
  mcs_channelid INT COMMENT "MCS通道ID",
  server_channel_count INT COMMENT "服务端通道数量",
  encryption_method INT COMMENT "加密方法",
  encryption_level INT COMMENT "加密级别",
  server_randomlen INT COMMENT "服务端随机数长度",
  server_certlen INT COMMENT "服务端证书长度",
  server_random STRING COMMENT "服务端随机数",
  server_cert STRING COMMENT "服务端证书",
  selected_protocols INT COMMENT "选择的协议",
  rdp_s_flag INT COMMENT "RDP服务端标志",

  -- RDP协商信息 (rdp_negotiate_msg)
  c_flag_protocols ARRAY<STRING> COMMENT "客户端标志协议",
  c_requested_protocols ARRAY<STRING> COMMENT "客户端请求协议",
  s_flag_protocols ARRAY<STRING> COMMENT "服务端标志协议",
  s_selected_protocols ARRAY<STRING> COMMENT "服务端选择协议",

  -- 系统字段
  create_time DATETIME DEFAULT NOW() COMMENT "记录创建时间"
)
DUPLICATE KEY(session_id, begin_time, src_ip, dst_ip, src_port, dst_port, client_name)
PARTITION BY RANGE(begin_time) (
  PARTITION p_default VALUES LESS THAN (MAXVALUE)
)
DISTRIBUTED BY HASH(session_id) BUCKETS 32
PROPERTIES(
  "replication_num" = "3",
  "storage_medium" = "SSD",
  "storage_cooldown_time" = "30 DAY",
  "dynamic_partition.enable" = "true",
  "dynamic_partition.time_unit" = "DAY",
  "dynamic_partition.start" = "-90",
  "dynamic_partition.end" = "3",
  "dynamic_partition.prefix" = "p",
  "dynamic_partition.buckets" = "32",
  "dynamic_partition.create_history_partition" = "true",
  "compression" = "zstd"
);
