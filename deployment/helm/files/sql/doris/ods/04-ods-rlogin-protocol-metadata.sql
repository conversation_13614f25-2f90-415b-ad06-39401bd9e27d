-- 使用数据库
USE nta;

-- ODS层 - Rlogin协议元数据表 (原始数据)
CREATE TABLE IF NOT EXISTS ods_rlogin_protocol_metadata (
  -- 公共字段 (Comm_msg)
  session_id VARCHAR(64) NOT NULL COMMENT "会话ID",
  begin_time DATETIME NOT NULL COMMENT "会话起始时间",
  src_ip VARCHAR(50) NOT NULL COMMENT "源IP地址",
  dst_ip VARCHAR(50) NOT NULL COMMENT "目标IP地址",
  src_port INT COMMENT "源端口",
  dst_port INT COMMENT "目标端口",
  ippro INT COMMENT "IP协议号",
  begin_nsec INT COMMENT "会话起始时间纳秒数",
  server_ip VARCHAR(64) COMMENT "服务器IP",
  app_id INT COMMENT "应用ID",
  app_name VARCHAR(128) COMMENT "应用名称",
  thread_id INT COMMENT "线程ID",
  task_id INT COMMENT "任务ID",
  batch_id INT COMMENT "批次ID",

  -- Rlogin窗口信息 (rlogin_wininfo_msg)
  magic_cookie VARCHAR(64) COMMENT "魔术Cookie",
  winsize_marker INT COMMENT "窗口大小标记",
  rows INT COMMENT "行数",
  columns INT COMMENT "列数",
  x_pixels INT COMMENT "X像素",
  y_pixels INT COMMENT "Y像素",
  term_type VARCHAR(64) COMMENT "终端类型",
  term_speed VARCHAR(64) COMMENT "终端速度",

  -- Rlogin用户信息 (rlogin_userinfo_msg)
  client_username VARCHAR(128) COMMENT "客户端用户名",
  server_username VARCHAR(128) COMMENT "服务端用户名",
  passwd VARCHAR(128) COMMENT "密码",

  -- 系统字段
  create_time DATETIME DEFAULT NOW() COMMENT "记录创建时间"
)
DUPLICATE KEY(session_id, begin_time, src_ip, dst_ip, src_port, dst_port, client_username)
PARTITION BY RANGE(begin_time) (
  PARTITION p_default VALUES LESS THAN (MAXVALUE)
)
DISTRIBUTED BY HASH(session_id) BUCKETS 32
PROPERTIES(
  "replication_num" = "3",
  "storage_medium" = "SSD",
  "storage_cooldown_time" = "30 DAY",
  "dynamic_partition.enable" = "true",
  "dynamic_partition.time_unit" = "DAY",
  "dynamic_partition.start" = "-90",
  "dynamic_partition.end" = "3",
  "dynamic_partition.prefix" = "p",
  "dynamic_partition.buckets" = "32",
  "dynamic_partition.create_history_partition" = "true",
  "compression" = "zstd"
);
