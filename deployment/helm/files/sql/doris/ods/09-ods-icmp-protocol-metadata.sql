-- 使用数据库
USE nta;

-- ODS层 - ICMP协议元数据表 (原始数据)
CREATE TABLE IF NOT EXISTS ods_icmp_protocol_metadata (
  -- 公共字段 (Comm_msg)
  session_id VARCHAR(64) NOT NULL COMMENT "会话ID",
  begin_time DATETIME NOT NULL COMMENT "会话起始时间",
  src_ip VARCHAR(50) NOT NULL COMMENT "源IP地址",
  dst_ip VARCHAR(50) NOT NULL COMMENT "目标IP地址",
  src_port INT COMMENT "源端口",
  dst_port INT COMMENT "目标端口",
  ippro INT COMMENT "IP协议号",
  server_ip VARCHAR(64) COMMENT "服务器IP",
  app_id INT COMMENT "应用ID",
  app_name VARCHAR(128) COMMENT "应用名称",
  thread_id INT COMMENT "线程ID",
  task_id INT COMMENT "任务ID",
  begin_nsec INT COMMENT "会话起始时间纳秒数",
  batch_id INT COMMENT "批次ID",

  -- ICMP specific fields (icmp_msg)
  msg_type INT COMMENT "消息类型",
  info_code INT COMMENT "信息代码",
  echo_seq_num INT COMMENT "反射数据包序列号",
  data_con STRING COMMENT "数据内容",
  unr_src_addr STRING COMMENT "不可达源IP地址",
  unr_dst_addr STRING COMMENT "不可达目的IP地址",
  unr_prot INT COMMENT "不可达协议号",
  unc_ttl INT COMMENT "不可达跳数",
  ver INT COMMENT "ICMP协议版本号",
  orig_time_stamp BIGINT COMMENT "发送时间戳（单位：毫秒）",
  recv_time_stamp BIGINT COMMENT "接收时间戳（单位：毫秒）",
  trans_time_stamp BIGINT COMMENT "传送时间戳（单位：毫秒）",
  mask INT COMMENT "网络掩码",
  sub_net_id INT COMMENT "子网络号",
  rtr_time_out INT COMMENT "通告地址的有效时间（单位：秒）",
  exc_src_addr VARCHAR(50) COMMENT "导致发生异常消息的报文的源ip地址",
  exc_dst_addr VARCHAR(50) COMMENT "导致发生异常消息的报文的目的ip地址",
  exc_prot INT COMMENT "导致发生异常消息的报文的协议号",
  exc_src_port INT COMMENT "导致发生异常消息的报文的源ip端口",
  exc_dst_port INT COMMENT "导致发生异常消息的报文的目的ip端口",
  gw_addr VARCHAR(50) COMMENT "重路由网关gateway",
  ttl INT COMMENT "生存时间",
  rep_ttl INT COMMENT "响应生存时间",
  qur_type INT COMMENT "查询类型",
  qur_ipv6_addr VARCHAR(50) COMMENT "查询的IPV6地址",
  qur_ipv4_addr VARCHAR(50) COMMENT "查询的IPV4地址",
  qur_dns VARCHAR(255) COMMENT "查询的DNS",
  ndp_life_time INT COMMENT "作为默认路由器时的生存期",
  ndp_link_addr STRING COMMENT "NDP重路由网关的链路地址",
  ndp_pre_len INT COMMENT "NDP前缀长度",
  ndp_pre_fix STRING COMMENT "NDP前缀",
  ndp_val_life_time INT COMMENT "NDP前缀有效生存期",
  ndp_cur_mtu INT COMMENT "NDP当前链路的MTU大小",
  ndp_tar_addr STRING COMMENT "邻居目的地址",
  next_hop_mtu INT COMMENT "下一跳链路的MTU大小",
  exc_pointer INT COMMENT "参数错误的位置（单位：字节）",
  mul_cast_addr STRING COMMENT "组播组地址",
  check_sum INT COMMENT "icmp层数据校验和",
  check_sum_reply INT COMMENT "icmp层响应数据校验和",
  rtraddr INT COMMENT "路由器地址",
  res_time BIGINT COMMENT "响应时间",
  exc_ttl INT COMMENT "不可达跳数",
  response_time BIGINT COMMENT "ICMP协议的响应时间间隔，微秒级",
  unreachable_source_port INT COMMENT "ICMP的不可达源端口",
  unreachable_destination_port INT COMMENT "ICMP的不可达目的端口",

  -- 系统字段
  create_time DATETIME DEFAULT NOW() COMMENT "记录创建时间"
)
DUPLICATE KEY(session_id, begin_time, src_ip, dst_ip, msg_type, info_code)
PARTITION BY RANGE(begin_time) (
  PARTITION p_default VALUES LESS THAN (MAXVALUE)
)
DISTRIBUTED BY HASH(session_id) BUCKETS 32
PROPERTIES(
  "replication_num" = "3",
  "storage_medium" = "SSD",
  "storage_cooldown_time" = "30 DAY",
  "dynamic_partition.enable" = "true",
  "dynamic_partition.time_unit" = "DAY",
  "dynamic_partition.start" = "-90",
  "dynamic_partition.end" = "3",
  "dynamic_partition.prefix" = "p",
  "dynamic_partition.buckets" = "32",
  "dynamic_partition.create_history_partition" = "true",
  "compression" = "zstd"
);
