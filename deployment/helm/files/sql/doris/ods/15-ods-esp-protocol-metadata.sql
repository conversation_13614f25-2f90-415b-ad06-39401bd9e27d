-- 使用数据库
USE nta;

-- ODS层 - ESP协议元数据表 (原始数据)
CREATE TABLE IF NOT EXISTS ods_esp_protocol_metadata (
  -- 公共字段 (Comm_msg)
  session_id VARCHAR(64) NOT NULL COMMENT "会话ID",
  begin_time DATETIME NOT NULL COMMENT "会话起始时间",
  src_ip VARCHAR(50) NOT NULL COMMENT "源IP地址",
  dst_ip VARCHAR(50) NOT NULL COMMENT "目标IP地址",
  src_port INT COMMENT "源端口",
  dst_port INT COMMENT "目标端口",
  ippro INT COMMENT "IP协议号",
  server_ip VARCHAR(64) COMMENT "服务器IP",
  app_id INT COMMENT "应用ID",
  app_name VARCHAR(128) COMMENT "应用名称",
  thread_id INT COMMENT "线程ID",
  task_id INT COMMENT "任务ID",
  begin_nsec INT COMMENT "会话起始时间纳秒数",
  batch_id INT COMMENT "批次ID",

  -- ESP specific fields (esp_msg)
  protocol_family INT COMMENT "协议名称(SSL/TLS/SSH/ISAKMP/IKE/L2TP/PPTP/IPSec)",
  communication_rate DOUBLE COMMENT "通信速率",
  direction INT COMMENT "数据流方向(0:不确定 1:c2s 2:s2c 3:双向)",
  encapsulation_mode INT COMMENT "封装模式(1:传输模式 2:隧道模式)",
  esp_spi INT COMMENT "封闭安全载荷(ESP)安全参数索引",
  esp_seq INT COMMENT "封闭安全载荷(ESP)序列号",
  esp_data_len INT COMMENT "封闭安全载荷(ESP)数据长度",
  esp_data STRING COMMENT "封闭安全载荷(ESP)数据",

  -- 系统字段
  create_time DATETIME DEFAULT NOW() COMMENT "记录创建时间"
)
DUPLICATE KEY(session_id, begin_time, src_ip, dst_ip, src_port, dst_port, protocol_family)
PARTITION BY RANGE(begin_time) (
  PARTITION p_default VALUES LESS THAN (MAXVALUE)
)
DISTRIBUTED BY HASH(session_id) BUCKETS 32
PROPERTIES(
  "replication_num" = "3",
  "storage_medium" = "SSD",
  "storage_cooldown_time" = "30 DAY",
  "dynamic_partition.enable" = "true",
  "dynamic_partition.time_unit" = "DAY",
  "dynamic_partition.start" = "-90",
  "dynamic_partition.end" = "3",
  "dynamic_partition.prefix" = "p",
  "dynamic_partition.buckets" = "32",
  "dynamic_partition.create_history_partition" = "true",
  "compression" = "zstd"
);
