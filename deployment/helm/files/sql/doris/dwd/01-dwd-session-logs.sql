-- 使用数据库
USE nta;

-- DWD层 - 网络流量会话日志表 (清洗后的明细数据)
-- 设计理念：单一会话表 + 协议元数据JSON数组，解决一对多关系
--
-- 协议元数据存储方式：
-- - 每种协议类型使用独立的 ARRAY<JSON> 字段
-- - 一个会话可以包含多个同类型协议的元数据记录
-- - JSON结构保持协议字段的完整性和灵活性
--
-- 查询示例：
-- 1. 获取会话的所有HTTP协议数据：SELECT session_id, http_protocols FROM dwd_session_logs WHERE session_id = 'xxx'
-- 2. 查询包含特定域名的会话：SELECT * FROM dwd_session_logs WHERE JSON_EXTRACT(http_protocols, '$[*].host') LIKE '%example.com%'
-- 3. 统计SSL证书信息：SELECT session_id, JSON_EXTRACT(ssl_protocols, '$[*].cert_subject') FROM dwd_session_logs WHERE ssl_protocols IS NOT NULL

CREATE TABLE IF NOT EXISTS dwd_session_logs (
  -- 基础会话标识字段
  session_id VARCHAR(64) NOT NULL COMMENT "会话ID",
  src_ip VARCHAR(50) NOT NULL COMMENT "源IP地址",
  dst_ip VARCHAR(50) NOT NULL COMMENT "目标IP地址",
  src_port INT COMMENT "源端口",
  dst_port INT COMMENT "目标端口",
  protocol INT COMMENT "IP协议号",
  session_start_time DATETIME NOT NULL COMMENT "会话开始时间",
  session_start_nsec INT COMMENT "会话开始时间纳秒部分",
  session_end_time DATETIME COMMENT "会话结束时间",
  session_end_nsec INT COMMENT "会话结束时间纳秒部分",
  server_ip VARCHAR(50) COMMENT "服务器IP",
  app_id INT COMMENT "应用ID",
  app_name VARCHAR(128) COMMENT "应用名称",
  thread_id INT COMMENT "线程ID",
  task_id INT COMMENT "任务ID",
  batch_id INT COMMENT "批次ID",

  -- 会话基础信息
  src_mac VARCHAR(32) COMMENT "源MAC地址",
  dst_mac VARCHAR(32) COMMENT "目标MAC地址",
  rule_count INT COMMENT "规则数量",
  rule_level INT COMMENT "规则级别",
  syn_data STRING COMMENT "SYN二进制流(Base64编码)",
  syn_ack_data STRING COMMENT "SYN-ACK二进制流(Base64编码)",
  rule_messages JSON COMMENT "规则消息",

  -- 会话统计信息
  src_total_sign INT COMMENT "源标识",
  dst_total_sign INT COMMENT "目标标识",
  payload_bytes_distribution JSON COMMENT "负载字节分布",
  payload_bytes_count BIGINT COMMENT "负载字节数",
  distribution_csq DOUBLE COMMENT "统计值",
  distribution_csqt DOUBLE COMMENT "统计t值",
  src_payload_length_distribution ARRAY<INT> COMMENT "源包负载长度分布数组",
  dst_payload_length_distribution ARRAY<INT> COMMENT "目标包负载长度分布数组",
  src_duration_distribution INT COMMENT "源包时间间隔分布",
  dst_duration_distribution INT COMMENT "目标包时间间隔分布",
  duration_distribution ARRAY<INT> COMMENT "包时间间隔分布数组",
  protocol_stack_count INT COMMENT "包协议栈数量",
  protocol_stack JSON COMMENT "包协议栈",
  src_is_internal BOOLEAN COMMENT "源IP是否为内部IP",
  dst_is_internal BOOLEAN COMMENT "目标IP是否为内部IP",
  extension_data JSON COMMENT "JSON扩展字段",
  src_mss INT COMMENT "源MSS",
  dst_mss INT COMMENT "目标MSS",
  src_window_scale INT COMMENT "源窗口扩展因子",
  dst_window_scale INT COMMENT "目标窗口扩展因子",
  src_payload_max_length INT COMMENT "源负载最大长度",
  dst_payload_max_length INT COMMENT "目标负载最大长度",
  src_ack_payload_max_length INT COMMENT "源ACK负载最大长度",
  dst_ack_payload_max_length INT COMMENT "目标ACK负载最大长度",
  src_ack_payload_min_length INT COMMENT "源ACK负载最小长度",
  dst_ack_payload_min_length INT COMMENT "目标ACK负载最小长度",
  tcp_connection_info ARRAY<STRUCT<
    bytes: BIGINT,
    packet_num: INT,
    psh_num: INT,
    acknowledgement: INT,
    min_sequence: INT,
    max_sequence: INT
  >> COMMENT "TCP连接信息数组",
  syn_sequence_numbers ARRAY<INT> COMMENT "SYN序列号数组",
  syn_sequence_count INT COMMENT "SYN序列号数量",
  src_ip_id_offsets ARRAY<INT> COMMENT "源IP的ID偏移数组",
  dst_ip_id_offsets ARRAY<INT> COMMENT "目标IP的ID偏移数组",
  ssl_block_ciphers ARRAY<INT> COMMENT "SSL会话块密码数组",

  -- 会话包统计信息
  src_max_packet_length INT COMMENT "源最大IP包长",
  dst_max_packet_length INT COMMENT "目标最大IP包长",
  src_packet_count INT COMMENT "源包数",
  dst_packet_count INT COMMENT "目标包数",
  src_payload_packet_count INT COMMENT "源有负载的包数",
  dst_payload_packet_count INT COMMENT "目标有负载的包数",
  src_total_bytes BIGINT COMMENT "源字节数",
  dst_total_bytes BIGINT COMMENT "目标字节数",
  src_payload_bytes BIGINT COMMENT "源有负载的字节数",
  dst_payload_bytes BIGINT COMMENT "目标有负载的字节数",
  src_fin_packet_count INT COMMENT "源Fin包数量",
  dst_fin_packet_count INT COMMENT "目标Fin包数量",
  src_rst_packet_count INT COMMENT "源Rst包数量",
  dst_rst_packet_count INT COMMENT "目标Rst包数量",
  src_syn_packet_count INT COMMENT "源Syn包数量",
  dst_syn_packet_count INT COMMENT "目标Syn包数量",
  src_syn_bytes INT COMMENT "源Syn包字节数",
  dst_syn_bytes INT COMMENT "目标Syn包字节数",
  src_ttl_max INT COMMENT "源IP:TTL最大值",
  dst_ttl_max INT COMMENT "目标IP:TTL最大值",
  src_ttl_min INT COMMENT "源IP:TTL最小值",
  dst_ttl_min INT COMMENT "目标IP:TTL最小值",
  src_duration_max INT COMMENT "源包的最大时间间隔",
  dst_duration_max INT COMMENT "目标包的最大时间间隔",
  src_duration_min INT COMMENT "源包的最小时间间隔",
  dst_duration_min INT COMMENT "目标包的最小时间间隔",
  src_disorder_packet_count INT COMMENT "源乱序包数",
  dst_disorder_packet_count INT COMMENT "目标乱序包数",
  src_resend_packet_count INT COMMENT "源重发包数",
  dst_resend_packet_count INT COMMENT "目标重发包数",
  src_lost_packet_length INT COMMENT "源丢包长度",
  dst_lost_packet_length INT COMMENT "目标丢包长度",
  src_psh_packet_count INT COMMENT "源PSH包数",
  dst_psh_packet_count INT COMMENT "目标PSH包数",
  protocol_packet_count INT COMMENT "包协议数量",
  unknown_protocol_packet_count INT COMMENT "未知协议包数量",
  syn_with_data_count INT COMMENT "syn包包含负载包数",
  src_bad_packet_count INT COMMENT "源错包数量",
  dst_bad_packet_count INT COMMENT "目标错包数量",
  app_detection_packet_id INT COMMENT "第几个负载包识别到应用",
  src_payload_samples ARRAY<STRING> COMMENT "源包负载前4个",
  dst_payload_samples ARRAY<STRING> COMMENT "目标包负载前4个",
  packet_info ARRAY<STRUCT<
    count: INT,
    sec: INT,
    nsec: INT,
    len: INT
  >> COMMENT "包信息数组",

  -- 协议指纹信息
  tcp_client_fingerprint BIGINT COMMENT "TCP客户端指纹",
  tcp_server_fingerprint BIGINT COMMENT "TCP服务端指纹",
  http_client_fingerprint BIGINT COMMENT "HTTP请求指纹",
  http_server_fingerprint BIGINT COMMENT "HTTP应答指纹",
  ssl_client_fingerprint BIGINT COMMENT "SSL请求指纹",
  ssl_server_fingerprint BIGINT COMMENT "SSL应答指纹",

  -- TCP指纹特征详情
  tcp_client_ecn_ip_ect BOOLEAN COMMENT "客户端TCP指纹特征-ECN IP ECT",
  tcp_client_df_nonzero_ipid BOOLEAN COMMENT "客户端TCP指纹特征-DF设置为1时IPID值是否不为0",
  tcp_client_flag_cwr BOOLEAN COMMENT "客户端TCP指纹特征-CWR标志",
  tcp_client_flag_ece BOOLEAN COMMENT "客户端TCP指纹特征-ECE标志",
  tcp_client_zero_timestamp BOOLEAN COMMENT "客户端TCP指纹特征-时间戳前四位是否为0",
  tcp_client_ttl INT COMMENT "客户端TCP指纹特征-TTL值",
  tcp_client_eol_padding_bytes INT COMMENT "客户端TCP指纹特征-EOL填充字节数",
  tcp_client_window_scale INT COMMENT "客户端TCP指纹特征-窗口扩大因子",
  tcp_client_window_mss_ratio INT COMMENT "客户端TCP指纹特征-窗口值与MSS比值",
  tcp_client_options_layout VARCHAR(255) COMMENT "客户端TCP指纹特征-TCP选项布局",

  tcp_server_ecn_ip_ect BOOLEAN COMMENT "服务端TCP指纹特征-ECN IP ECT",
  tcp_server_df_nonzero_ipid BOOLEAN COMMENT "服务端TCP指纹特征-DF设置为1时IPID值是否不为0",
  tcp_server_flag_cwr BOOLEAN COMMENT "服务端TCP指纹特征-CWR标志",
  tcp_server_flag_ece BOOLEAN COMMENT "服务端TCP指纹特征-ECE标志",
  tcp_server_zero_timestamp BOOLEAN COMMENT "服务端TCP指纹特征-时间戳前四位是否为0",
  tcp_server_ttl INT COMMENT "服务端TCP指纹特征-TTL值",
  tcp_server_eol_padding_bytes INT COMMENT "服务端TCP指纹特征-EOL填充字节数",
  tcp_server_window_scale INT COMMENT "服务端TCP指纹特征-窗口扩大因子",
  tcp_server_window_mss_ratio INT COMMENT "服务端TCP指纹特征-窗口值与MSS比值",
  tcp_server_options_layout VARCHAR(255) COMMENT "服务端TCP指纹特征-TCP选项布局",

  -- 会话扩展信息
  session_duration INT COMMENT "会话持续时间(秒)",
  first_packet_sender VARCHAR(50) COMMENT "首包发送方IP",
  device_id INT COMMENT "设备码",
  first_layer_protocol INT COMMENT "首层协议",
  proxy_ip VARCHAR(50) COMMENT "代理IP",
  proxy_port INT COMMENT "代理端口",
  proxy_real_hostname VARCHAR(255) COMMENT "代理访问的真实域名",
  proxy_type INT COMMENT "代理类型",
  processing_start_time DATETIME COMMENT "开始处理时间",
  processing_end_time DATETIME COMMENT "结束处理时间",
  rule_labels ARRAY<INT> COMMENT "规则标签",
  port_list ARRAY<INT> COMMENT "端口列表",

  -- HTTP协议元数据数组
  http_protocols ARRAY<JSON> COMMENT "HTTP协议元数据数组，JSON结构：{\"sequence\":1,\"timestamp\":\"2024-01-01 12:00:00\",\"method\":\"GET\",\"url\":\"/api/data\",\"host\":\"example.com\",\"status_code\":200,\"user_agent\":\"Mozilla/5.0...\",\"request_headers\":{\"Accept\":\"application/json\"},\"response_headers\":{\"Content-Type\":\"application/json\"},\"request_size\":1024,\"response_size\":2048}",

  -- SSL/TLS协议元数据数组
  ssl_protocols ARRAY<JSON> COMMENT "SSL/TLS协议元数据数组，JSON结构：{\"sequence\":1,\"timestamp\":\"2024-01-01 12:00:00\",\"version\":\"TLS1.3\",\"cipher_suite\":\"TLS_AES_256_GCM_SHA384\",\"server_name\":\"example.com\",\"cert_subject\":\"CN=example.com,O=Example Inc\",\"cert_issuer\":\"CN=Let's Encrypt Authority\",\"cert_serial\":\"03A1B2C3D4E5F6\",\"cert_not_before\":\"2024-01-01\",\"cert_not_after\":\"2024-12-31\",\"cert_sha1\":\"A1B2C3...\",\"cert_sha256\":\"D4E5F6...\",\"ja3\":\"769,47-53-5-10-49161-49162...\",\"ja3s\":\"769,47,65281\",\"is_self_signed\":false}",

  -- DNS协议元数据数组
  dns_protocols ARRAY<JSON> COMMENT "DNS协议元数据数组，JSON结构：{\"sequence\":1,\"timestamp\":\"2024-01-01 12:00:00\",\"transaction_id\":12345,\"query_name\":\"example.com\",\"query_type\":\"A\",\"query_class\":\"IN\",\"response_code\":\"NOERROR\",\"answer_count\":1,\"authority_count\":0,\"additional_count\":0,\"resolved_addresses\":[\"*************\"],\"ttl\":300,\"is_recursive\":true}",

  -- SSH协议元数据数组
  ssh_protocols ARRAY<JSON> COMMENT "SSH协议元数据数组，JSON结构：{\"sequence\":1,\"timestamp\":\"2024-01-01 12:00:00\",\"protocol_version\":\"2.0\",\"client_software\":\"OpenSSH_8.0\",\"server_software\":\"OpenSSH_7.4\",\"kex_algorithm\":\"diffie-hellman-group14-sha256\",\"host_key_algorithm\":\"rsa-sha2-512\",\"encryption_algorithm\":\"aes128-ctr\",\"mac_algorithm\":\"hmac-sha2-256\",\"compression_algorithm\":\"none\",\"hassh_client\":\"92674389fa9e47a5...\",\"hassh_server\":\"b12d2871a1189eff...\",\"auth_methods\":[\"password\",\"publickey\"],\"auth_success\":true}",

  -- VNC协议元数据数组
  vnc_protocols ARRAY<JSON> COMMENT "VNC协议元数据数组，JSON结构：{\"sequence\":1,\"timestamp\":\"2024-01-01 12:00:00\",\"fb_width\":1920,\"fb_height\":1080,\"desktop_name\":\"user-desktop\",\"server_protocol_ver\":\"RFB 003.008\",\"client_protocol_ver\":\"RFB 003.008\",\"auth_result\":\"OK\",\"encoding_types\":[\"Raw\",\"CopyRect\",\"Hextile\"],\"bits_per_pixel\":32,\"depth\":24,\"big_endian\":false,\"true_color\":true}",

  -- TELNET协议元数据数组
  telnet_protocols ARRAY<JSON> COMMENT "TELNET协议元数据数组，JSON结构：{\"sequence\":1,\"timestamp\":\"2024-01-01 12:00:00\",\"term_width\":80,\"term_height\":24,\"term_type\":\"xterm\",\"username\":\"admin\",\"options_negotiated\":[\"ECHO\",\"SUPPRESS_GO_AHEAD\",\"TERMINAL_TYPE\"]}",

  -- RLOGIN协议元数据数组
  rlogin_protocols ARRAY<JSON> COMMENT "RLOGIN协议元数据数组，JSON结构：{\"sequence\":1,\"timestamp\":\"2024-01-01 12:00:00\",\"rows\":24,\"columns\":80,\"term_type\":\"vt100\",\"client_username\":\"user1\",\"server_username\":\"admin\",\"terminal_speed\":\"38400\"}",

  -- RDP协议元数据数组
  rdp_protocols ARRAY<JSON> COMMENT "RDP协议元数据数组，JSON结构：{\"sequence\":1,\"timestamp\":\"2024-01-01 12:00:00\",\"desktop_width\":1920,\"desktop_height\":1080,\"client_name\":\"DESKTOP-ABC123\",\"encryption_method\":1,\"color_depth\":32,\"keyboard_layout\":\"en-US\",\"client_build\":7601}",

  -- ICMP协议元数据数组
  icmp_protocols ARRAY<JSON> COMMENT "ICMP协议元数据数组，JSON结构：{\"sequence\":1,\"timestamp\":\"2024-01-01 12:00:00\",\"msg_type\":8,\"info_code\":0,\"echo_seq_num\":1,\"response_time\":15000,\"payload_size\":64,\"identifier\":12345}",

  -- NTP协议元数据数组
  ntp_protocols ARRAY<JSON> COMMENT "NTP协议元数据数组，JSON结构：{\"sequence\":1,\"timestamp\":\"2024-01-01 12:00:00\",\"version\":4,\"client_stratum\":3,\"server_stratum\":2,\"client_reference_id\":\"***********\",\"server_reference_id\":\"GPS\",\"precision\":-20,\"root_delay\":0.015,\"root_dispersion\":0.002}",

  -- XDMCP协议元数据数组
  xdmcp_protocols ARRAY<JSON> COMMENT "XDMCP协议元数据数组，JSON结构：{\"sequence\":1,\"timestamp\":\"2024-01-01 12:00:00\",\"version\":1,\"display_number\":0,\"session_id\":12345,\"hostname\":\"workstation1\",\"status\":\"Willing\",\"authentication_name\":\"MIT-MAGIC-COOKIE-1\"}",

  -- S7协议元数据数组
  s7_protocols ARRAY<JSON> COMMENT "S7协议元数据数组，JSON结构：{\"sequence\":1,\"timestamp\":\"2024-01-01 12:00:00\",\"tpkt_version\":3,\"cotp_type\":240,\"s7_type\":1,\"s7_function\":240,\"system_type\":1,\"system_group_function\":4,\"system_sub_function\":1,\"dst_ref\":0,\"src_ref\":0,\"pdu_size\":240,\"src_connect_type\":1,\"src_rack\":0,\"src_slot\":2,\"dst_connect_type\":1,\"dst_rack\":0,\"dst_slot\":1,\"packet_c2s\":5}",

  -- Modbus协议元数据数组
  modbus_protocols ARRAY<JSON> COMMENT "Modbus协议元数据数组，JSON结构：{\"sequence\":1,\"timestamp\":\"2024-01-01 12:00:00\",\"trans_id\":1,\"protocol_id\":0,\"slave_id\":1,\"func_code\":3,\"packet_c2s\":2}",

  -- IEC104协议元数据数组
  iec104_protocols ARRAY<JSON> COMMENT "IEC104协议元数据数组，JSON结构：{\"sequence\":1,\"timestamp\":\"2024-01-01 12:00:00\",\"trans_id\":1,\"protocol_id\":0,\"slave_id\":1,\"func_code\":100}",

  -- EIP协议元数据数组
  eip_protocols ARRAY<JSON> COMMENT "EIP协议元数据数组，JSON结构：{\"sequence\":1,\"timestamp\":\"2024-01-01 12:00:00\",\"trans_id\":1,\"protocol_id\":0,\"slave_id\":1,\"func_code\":14}",

  -- OPC协议元数据数组
  opc_protocols ARRAY<JSON> COMMENT "OPC协议元数据数组，JSON结构：{\"sequence\":1,\"timestamp\":\"2024-01-01 12:00:00\",\"trans_id\":1,\"protocol_id\":0,\"slave_id\":1,\"func_code\":1}",

  -- ESP协议元数据数组
  esp_protocols ARRAY<JSON> COMMENT "ESP协议元数据数组，JSON结构：{\"sequence\":1,\"timestamp\":\"2024-01-01 12:00:00\",\"protocol_family\":50,\"communication_rate\":1024.5,\"direction\":1,\"encapsulation_mode\":1,\"esp_spi\":12345,\"esp_seq\":1,\"esp_data_len\":64,\"esp_data\":\"encrypted_payload\"}",

  -- L2TP协议元数据数组
  l2tp_protocols ARRAY<JSON> COMMENT "L2TP协议元数据数组，JSON结构：{\"sequence\":1,\"timestamp\":\"2024-01-01 12:00:00\",\"protocol_family\":115,\"communication_rate\":512.0,\"direction\":3,\"protocol_version\":2,\"framing_capabilities\":1,\"bearer_capabilities\":1,\"server_hostname\":\"vpn-server\",\"client_hostname\":\"client-pc\",\"server_vendorname\":\"Cisco\",\"client_vendorname\":\"Windows\",\"calling_number\":\"+1234567890\",\"proxy_authen_type\":1,\"proxy_authen_name\":\"<EMAIL>\",\"is_negotiate_success\":1}",

  -- 系统字段
  create_time DATETIME COMMENT "记录创建时间",
  update_time DATETIME COMMENT "记录更新时间"
)
DUPLICATE KEY(session_id, session_start_time, src_ip, dst_ip, src_port, dst_port, protocol)
PARTITION BY RANGE(session_start_time) (
  PARTITION p_default VALUES LESS THAN (MAXVALUE)
)
DISTRIBUTED BY HASH(session_id) BUCKETS 64
PROPERTIES(
  "replication_num" = "3",
  "storage_medium" = "SSD",
  "storage_cooldown_time" = "30 DAY",
  "dynamic_partition.enable" = "true",
  "dynamic_partition.time_unit" = "DAY",
  "dynamic_partition.start" = "-90",
  "dynamic_partition.end" = "3",
  "dynamic_partition.prefix" = "p",
  "dynamic_partition.buckets" = "64",
  "dynamic_partition.create_history_partition" = "true",
  "compression" = "zstd",
  "bloom_filter_columns" = "session_id,src_ip,dst_ip,task_id"
);


