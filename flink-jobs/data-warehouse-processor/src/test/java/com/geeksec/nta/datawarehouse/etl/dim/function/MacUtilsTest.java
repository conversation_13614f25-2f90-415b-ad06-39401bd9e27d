package com.geeksec.nta.datawarehouse.etl.dim.function;

import java.util.Map;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeAll;
import static org.junit.jupiter.api.Assertions.*;

import com.geeksec.common.utils.net.MacUtils;

/**
 * MAC地址工具类测试
 *
 * <AUTHOR>
 */
public class MacUtilsTest {

    @BeforeAll
    static void setUp() {
        // 确保OUI数据库已加载
        MacUtils.getOuiCacheSize();
    }

    @Test
    void testValidMacAddresses() {
        // 测试各种有效的MAC地址格式
        assertTrue(MacUtils.isValidMac("AA:BB:CC:DD:EE:FF"));
        assertTrue(MacUtils.isValidMac("aa:bb:cc:dd:ee:ff"));
        assertTrue(MacUtils.isValidMac("AA-BB-CC-DD-EE-FF"));
        assertTrue(MacUtils.isValidMac("AABBCCDDEEFF"));
        assertTrue(MacUtils.isValidMac("AA.BB.CC.DD.EE.FF"));
    }

    @Test
    void testInvalidMacAddresses() {
        // 测试无效的MAC地址格式
        assertFalse(MacUtils.isValidMac(""));
        assertFalse(MacUtils.isValidMac(null));
        assertFalse(MacUtils.isValidMac("AA:BB:CC:DD:EE"));
        assertFalse(MacUtils.isValidMac("AA:BB:CC:DD:EE:FF:GG"));
        assertFalse(MacUtils.isValidMac("GG:BB:CC:DD:EE:FF"));
        assertFalse(MacUtils.isValidMac("AA:BB:CC:DD:EE:ZZ"));
    }

    @Test
    void testNormalizeMac() {
        // 测试MAC地址标准化
        assertEquals("AA:BB:CC:DD:EE:FF", MacUtils.normalizeMac("aa:bb:cc:dd:ee:ff"));
        assertEquals("AA:BB:CC:DD:EE:FF", MacUtils.normalizeMac("AA-BB-CC-DD-EE-FF"));
        assertEquals("AA:BB:CC:DD:EE:FF", MacUtils.normalizeMac("AABBCCDDEEFF"));
        assertEquals("AA:BB:CC:DD:EE:FF", MacUtils.normalizeMac("AA.BB.CC.DD.EE.FF"));

        // 测试无效MAC地址
        assertNull(MacUtils.normalizeMac("invalid"));
        assertNull(MacUtils.normalizeMac(""));
        assertNull(MacUtils.normalizeMac(null));
    }

    @Test
    void testGetOui() {
        // 测试OUI提取
        assertEquals("AA:BB:CC", MacUtils.getOui("AA:BB:CC:DD:EE:FF"));
        assertEquals("AA:BB:CC", MacUtils.getOui("aa:bb:cc:dd:ee:ff"));
        assertEquals("AA:BB:CC", MacUtils.getOui("AA-BB-CC-DD-EE-FF"));
        assertEquals("AA:BB:CC", MacUtils.getOui("AABBCCDDEEFF"));

        // 测试无效MAC地址
        assertNull(MacUtils.getOui("invalid"));
        assertNull(MacUtils.getOui(null));
    }

    @Test
    void testGetVendor() {
        // 测试已知厂商的MAC地址
        // Apple的OUI: F0:EE:7A
        String appleVendor = MacUtils.getVendor("F0:EE:7A:12:34:56");
        assertNotNull(appleVendor);
        assertNotEquals("Unknown", appleVendor);

        // 测试未知厂商的MAC地址
        String unknownVendor = MacUtils.getVendor("FF:FF:FF:12:34:56");
        assertEquals("Unknown", unknownVendor);

        // 测试无效MAC地址
        assertEquals("Unknown", MacUtils.getVendor("invalid"));
        assertEquals("Unknown", MacUtils.getVendor(null));
    }

    @Test
    void testIsLocallyAdministered() {
        // 测试本地管理地址（第二位为1）
        assertTrue(MacUtils.isLocallyAdministered("02:00:00:00:00:00"));
        assertTrue(MacUtils.isLocallyAdministered("06:00:00:00:00:00"));
        assertTrue(MacUtils.isLocallyAdministered("0A:00:00:00:00:00"));
        assertTrue(MacUtils.isLocallyAdministered("0E:00:00:00:00:00"));

        // 测试全球唯一地址（第二位为0）
        assertFalse(MacUtils.isLocallyAdministered("00:00:00:00:00:00"));
        assertFalse(MacUtils.isLocallyAdministered("04:00:00:00:00:00"));
        assertFalse(MacUtils.isLocallyAdministered("08:00:00:00:00:00"));
        assertFalse(MacUtils.isLocallyAdministered("0C:00:00:00:00:00"));

        // 测试无效MAC地址
        assertFalse(MacUtils.isLocallyAdministered("invalid"));
        assertFalse(MacUtils.isLocallyAdministered(null));
    }

    @Test
    void testIsMulticast() {
        // 测试组播地址（最低位为1）
        assertTrue(MacUtils.isMulticast("01:00:00:00:00:00"));
        assertTrue(MacUtils.isMulticast("03:00:00:00:00:00"));
        assertTrue(MacUtils.isMulticast("05:00:00:00:00:00"));
        assertTrue(MacUtils.isMulticast("07:00:00:00:00:00"));

        // 测试单播地址（最低位为0）
        assertFalse(MacUtils.isMulticast("00:00:00:00:00:00"));
        assertFalse(MacUtils.isMulticast("02:00:00:00:00:00"));
        assertFalse(MacUtils.isMulticast("04:00:00:00:00:00"));
        assertFalse(MacUtils.isMulticast("06:00:00:00:00:00"));

        // 测试无效MAC地址
        assertFalse(MacUtils.isMulticast("invalid"));
        assertFalse(MacUtils.isMulticast(null));
    }

    @Test
    void testGetMacInfo() {
        // 测试有效MAC地址的详细信息
        Map<String, Object> macInfo = MacUtils.getMacInfo("F0:EE:7A:12:34:56");

        assertNotNull(macInfo);
        assertEquals("F0:EE:7A:12:34:56", macInfo.get("mac"));
        assertEquals(true, macInfo.get("is_valid"));
        assertEquals("F0:EE:7A", macInfo.get("oui"));
        assertNotNull(macInfo.get("vendor"));
        assertNotNull(macInfo.get("is_locally_administered"));
        assertNotNull(macInfo.get("is_multicast"));
        assertNotNull(macInfo.get("is_potentially_randomized"));
        assertNotNull(macInfo.get("is_virtualized"));

        // 测试无效MAC地址的详细信息
        Map<String, Object> invalidMacInfo = MacUtils.getMacInfo("invalid");

        assertNotNull(invalidMacInfo);
        assertEquals("invalid", invalidMacInfo.get("mac"));
        assertEquals(false, invalidMacInfo.get("is_valid"));
        assertNull(invalidMacInfo.get("oui"));
        assertEquals("Unknown", invalidMacInfo.get("vendor"));
        assertEquals(false, invalidMacInfo.get("is_locally_administered"));
        assertEquals(false, invalidMacInfo.get("is_multicast"));
        assertEquals(false, invalidMacInfo.get("is_potentially_randomized"));
        assertEquals(false, invalidMacInfo.get("is_virtualized"));
    }

    @Test
    void testOuiCacheSize() {
        // 测试OUI缓存大小
        int cacheSize = MacUtils.getOuiCacheSize();
        assertTrue(cacheSize > 0, "OUI缓存应该包含数据");
    }

    @Test
    void testRefreshOuiDatabase() {
        // 测试刷新OUI数据库
        int originalSize = MacUtils.getOuiCacheSize();
        MacUtils.refreshOuiDatabase();
        int newSize = MacUtils.getOuiCacheSize();

        assertEquals(originalSize, newSize, "刷新后缓存大小应该相同");
    }

    @Test
    void testKnownVendors() {
        // 测试一些已知的厂商OUI
        // 这些测试可能会因为OUI数据库的更新而失败，但可以验证基本功能

        // Apple的一个OUI
        String appleVendor = MacUtils.getVendor("F0:EE:7A:00:00:00");
        assertTrue(appleVendor.toLowerCase().contains("apple") || !appleVendor.equals("Unknown"));

        // Cisco的一个OUI
        String ciscoVendor = MacUtils.getVendor("E8:0A:B9:00:00:00");
        assertTrue(ciscoVendor.toLowerCase().contains("cisco") || !ciscoVendor.equals("Unknown"));

        // Intel的一个OUI
        String intelVendor = MacUtils.getVendor("E4:C7:67:00:00:00");
        assertTrue(intelVendor.toLowerCase().contains("intel") || !intelVendor.equals("Unknown"));
    }

    @Test
    void testIsVirtualizedVendor() {
        // 测试虚拟化厂商识别
        assertTrue(MacUtils.isVirtualizedVendor("VMware, Inc."));
        assertTrue(MacUtils.isVirtualizedVendor("Oracle VirtualBox"));
        assertTrue(MacUtils.isVirtualizedVendor("Parallels Desktop"));
        assertTrue(MacUtils.isVirtualizedVendor("Microsoft Virtual"));
        assertTrue(MacUtils.isVirtualizedVendor("Xen Source"));
        assertTrue(MacUtils.isVirtualizedVendor("QEMU"));
        assertTrue(MacUtils.isVirtualizedVendor("KVM"));
        assertTrue(MacUtils.isVirtualizedVendor("Hyper-V"));
        assertTrue(MacUtils.isVirtualizedVendor("Citrix"));

        // 测试非虚拟化厂商
        assertFalse(MacUtils.isVirtualizedVendor("Apple, Inc."));
        assertFalse(MacUtils.isVirtualizedVendor("Intel Corporation"));
        assertFalse(MacUtils.isVirtualizedVendor("Cisco Systems"));
        assertFalse(MacUtils.isVirtualizedVendor(null));
        assertFalse(MacUtils.isVirtualizedVendor(""));
    }

    @Test
    void testPotentiallyRandomizedMac() {
        // 测试可能的随机MAC地址（本地管理地址且非组播）
        Map<String, Object> randomMacInfo = MacUtils.getMacInfo("02:00:00:12:34:56");
        assertEquals(true, randomMacInfo.get("is_locally_administered"));
        assertEquals(false, randomMacInfo.get("is_multicast"));
        assertEquals(true, randomMacInfo.get("is_potentially_randomized"));

        // 测试组播地址（不是随机MAC）
        Map<String, Object> multicastMacInfo = MacUtils.getMacInfo("03:00:00:12:34:56");
        assertEquals(true, multicastMacInfo.get("is_locally_administered"));
        assertEquals(true, multicastMacInfo.get("is_multicast"));
        assertEquals(false, multicastMacInfo.get("is_potentially_randomized"));

        // 测试全球唯一地址（不是随机MAC）
        Map<String, Object> globalMacInfo = MacUtils.getMacInfo("00:00:00:12:34:56");
        assertEquals(false, globalMacInfo.get("is_locally_administered"));
        assertEquals(false, globalMacInfo.get("is_multicast"));
        assertEquals(false, globalMacInfo.get("is_potentially_randomized"));
    }

    @Test
    void testEnrichmentIntegration() {
        // 测试完整的丰富化流程
        Map<String, Object> macInfo = MacUtils.getMacInfo("F0:EE:7A:12:34:56");

        // 验证所有丰富化字段都存在
        assertTrue(macInfo.containsKey("mac"));
        assertTrue(macInfo.containsKey("is_valid"));
        assertTrue(macInfo.containsKey("oui"));
        assertTrue(macInfo.containsKey("vendor"));
        assertTrue(macInfo.containsKey("is_locally_administered"));
        assertTrue(macInfo.containsKey("is_multicast"));
        assertTrue(macInfo.containsKey("is_potentially_randomized"));
        assertTrue(macInfo.containsKey("is_virtualized"));

        // 验证字段类型
        assertTrue(macInfo.get("mac") instanceof String);
        assertTrue(macInfo.get("is_valid") instanceof Boolean);
        assertTrue(macInfo.get("oui") instanceof String || macInfo.get("oui") == null);
        assertTrue(macInfo.get("vendor") instanceof String);
        assertTrue(macInfo.get("is_locally_administered") instanceof Boolean);
        assertTrue(macInfo.get("is_multicast") instanceof Boolean);
        assertTrue(macInfo.get("is_potentially_randomized") instanceof Boolean);
        assertTrue(macInfo.get("is_virtualized") instanceof Boolean);
    }
}
