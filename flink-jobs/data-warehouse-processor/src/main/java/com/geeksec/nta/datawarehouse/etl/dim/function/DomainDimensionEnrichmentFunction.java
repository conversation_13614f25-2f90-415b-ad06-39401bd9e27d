package com.geeksec.nta.datawarehouse.etl.dim.function;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 域名维度数据丰富化处理函数
 * 使用Flink Backend State管理域名信息缓存，提供容错性和可扩展性
 *
 * <AUTHOR>
 */
public class DomainDimensionEnrichmentFunction extends ProcessFunction<Row, Row> {

    private static final Logger log = LoggerFactory.getLogger(DomainDimensionEnrichmentFunction.class);

    /** 域名部分最小长度 */
    private static final int MIN_DOMAIN_PARTS = 2;

    /** 域名信息缓存状态，使用MapState管理 */
    private transient MapState<String, Map<String, Object>> domainInfoCache;

    /** 缓存TTL配置，默认24小时 */
    private final Duration cacheTtl;

    /** 域名字段名 */
    private final String domainFieldName;

    /**
     * 构造函数
     *
     * @param domainFieldName 域名字段名
     */
    public DomainDimensionEnrichmentFunction(String domainFieldName) {
        this(domainFieldName, Duration.ofHours(24));
    }

    /**
     * 构造函数
     *
     * @param domainFieldName 域名字段名
     * @param cacheTtl 缓存TTL时间
     */
    public DomainDimensionEnrichmentFunction(String domainFieldName, Duration cacheTtl) {
        this.domainFieldName = domainFieldName;
        this.cacheTtl = cacheTtl;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 配置状态TTL
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(cacheTtl)
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();

        // 创建MapState描述符
        MapStateDescriptor<String, Map<String, Object>> descriptor = new MapStateDescriptor<>(
                "domain-enrichment-cache",
                TypeInformation.of(new TypeHint<String>() {}),
                TypeInformation.of(new TypeHint<Map<String, Object>>() {})
        );

        // 启用TTL
        descriptor.enableTimeToLive(ttlConfig);

        // 获取状态
        domainInfoCache = getRuntimeContext().getMapState(descriptor);
    }

    @Override
    public void processElement(Row value, Context ctx, Collector<Row> out) throws Exception {
        // 获取域名
        String domain = getDomainFromRow(value);

        if (StringUtils.isEmpty(domain)) {
            // 域名无效，直接输出原始数据
            out.collect(value);
            return;
        }

        // 获取丰富化的域名信息
        Map<String, Object> enrichedInfo = getEnrichedDomainInfo(domain);

        // 将丰富化信息添加到Row中
        Row enrichedRow = addEnrichmentToRow(value, enrichedInfo);

        // 输出丰富化后的数据
        out.collect(enrichedRow);
    }

    /**
     * 从Row中获取域名
     *
     * @param row 输入Row
     * @return 域名字符串
     */
    private String getDomainFromRow(Row row) {
        try {
            Object domainValue = row.getField(domainFieldName);
            return domainValue != null ? domainValue.toString() : null;
        } catch (Exception e) {
            log.warn("无法从Row中获取域名字段 {}: {}", domainFieldName, e.getMessage());
            return null;
        }
    }

    /**
     * 获取丰富化的域名信息，优先从缓存获取
     *
     * @param domain 域名
     * @return 丰富化的域名信息
     * @throws Exception 状态访问异常
     */
    private Map<String, Object> getEnrichedDomainInfo(String domain) throws Exception {
        // 检查缓存
        Map<String, Object> cachedInfo = domainInfoCache.get(domain);
        if (cachedInfo != null) {
            return cachedInfo;
        }

        // 缓存未命中，重新计算并缓存
        Map<String, Object> enrichedInfo = enrichDomainInfo(domain);
        domainInfoCache.put(domain, enrichedInfo);

        return enrichedInfo;
    }

    /**
     * 丰富域名信息
     * 为域名添加基础域名等信息
     *
     * @param domain 域名
     * @return 丰富后的域名信息Map
     */
    private Map<String, Object> enrichDomainInfo(String domain) {
        Map<String, Object> domainInfo = new HashMap<>(8);
        domainInfo.put("domain", domain);
        domainInfo.put("is_valid", true);

        try {
            // 提取基础域名
            String baseDomain = extractBaseDomain(domain);
            domainInfo.put("base_domain", baseDomain);

            // TODO: 实现域名信息查询逻辑
            // 例如查询Alexa排名、Whois信息等
            // 简化处理，返回基本信息
            domainInfo.put("alexa_rank", 0);
            domainInfo.put("whois", "");

        } catch (Exception e) {
            log.warn("获取域名信息失败: {}", domain, e);
        }

        return domainInfo;
    }

    /**
     * 提取基础域名
     * 例如从www.example.com提取example.com
     *
     * @param domain 完整域名
     * @return 基础域名
     */
    private String extractBaseDomain(String domain) {
        if (domain == null || domain.isEmpty()) {
            return "";
        }

        // 简单实现，仅作示例
        // 实际应用中应使用更复杂的算法，考虑各国顶级域名规则
        String[] parts = domain.split("\\.");
        if (parts.length <= MIN_DOMAIN_PARTS) {
            return domain;
        }

        return parts[parts.length - 2] + "." + parts[parts.length - 1];
    }

    /**
     * 将丰富化信息添加到Row中
     *
     * @param originalRow 原始Row
     * @param enrichmentInfo 丰富化信息
     * @return 丰富化后的Row
     */
    private Row addEnrichmentToRow(Row originalRow, Map<String, Object> enrichmentInfo) {
        // 创建新的Row，包含原始数据和丰富化信息
        Row enrichedRow = Row.withNames();

        // 复制原始字段
        Set<String> fieldNames = originalRow.getFieldNames(true);
        if (fieldNames != null) {
            for (String fieldName : fieldNames) {
                enrichedRow.setField(fieldName, originalRow.getField(fieldName));
            }
        }

        // 添加丰富化字段（使用前缀避免冲突）
        String prefix = "enriched_";
        for (Map.Entry<String, Object> entry : enrichmentInfo.entrySet()) {
            String fieldName = prefix + entry.getKey();
            enrichedRow.setField(fieldName, entry.getValue());
        }

        return enrichedRow;
    }
}
