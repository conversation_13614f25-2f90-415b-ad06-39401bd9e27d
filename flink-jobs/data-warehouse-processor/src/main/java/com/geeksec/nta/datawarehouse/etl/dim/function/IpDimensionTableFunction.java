package com.geeksec.nta.datawarehouse.etl.dim.function;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import com.geeksec.common.utils.net.GeoIpUtils;
import com.geeksec.common.utils.net.IpUtils;
import com.geeksec.common.utils.time.DateTimeUtils;
import com.maxmind.geoip2.model.AsnResponse;
import com.maxmind.geoip2.model.CityResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * IP维度表处理函数
 * 专门用于生成符合dim_ipv4/dim_ipv6表结构的维度数据
 * 使用Flink Backend State管理IP地理信息缓存
 *
 * <AUTHOR>
 */
@Slf4j
public class IpDimensionTableFunction extends ProcessFunction<Row, Row> {

    /** IPv4维度侧输出标签 */
    public static final OutputTag<Row> IPV4_DIM_TAG = new OutputTag<Row>("IPv4Dimension") {
    };

    /** IPv6维度侧输出标签 */
    public static final OutputTag<Row> IPV6_DIM_TAG = new OutputTag<Row>("IPv6Dimension") {
    };

    /** IP信息缓存状态，使用MapState管理 */
    private transient MapState<String, Map<String, Object>> ipInfoCache;

    /** 缓存TTL配置，默认12小时 */
    private final Duration cacheTtl;

    /** IP字段名 */
    private final String ipFieldName;

    /**
     * 构造函数
     *
     * @param ipFieldName IP字段名
     */
    public IpDimensionTableFunction(String ipFieldName) {
        this(ipFieldName, Duration.ofHours(12));
    }

    /**
     * 构造函数
     *
     * @param ipFieldName IP字段名
     * @param cacheTtl    缓存TTL时间
     */
    public IpDimensionTableFunction(String ipFieldName, Duration cacheTtl) {
        this.ipFieldName = ipFieldName;
        this.cacheTtl = cacheTtl;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 配置状态TTL
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(cacheTtl)
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();

        // 创建MapState描述符
        MapStateDescriptor<String, Map<String, Object>> descriptor = new MapStateDescriptor<>(
                "ip-dimension-cache",
                TypeInformation.of(new TypeHint<String>() {
                }),
                TypeInformation.of(new TypeHint<Map<String, Object>>() {
                }));

        // 启用TTL
        descriptor.enableTimeToLive(ttlConfig);

        // 获取状态
        ipInfoCache = getRuntimeContext().getMapState(descriptor);
    }

    @Override
    public void processElement(Row value, Context ctx, Collector<Row> out) throws Exception {
        // 获取IP地址
        String ipAddress = getIpFromRow(value);

        if (StringUtils.isEmpty(ipAddress) || !IpUtils.isValidIp(ipAddress)) {
            // IP无效，直接输出原始数据
            out.collect(value);
            return;
        }

        // 获取丰富化的IP信息
        Map<String, Object> enrichedInfo = getEnrichedIpInfo(ipAddress);

        // 创建维度表记录
        Row dimensionRow = createDimensionRow(ipAddress, enrichedInfo);

        // 根据IP类型输出到不同的侧输出流
        if (IpUtils.isValidIpv6(ipAddress)) {
            ctx.output(IPV6_DIM_TAG, dimensionRow);
        } else {
            ctx.output(IPV4_DIM_TAG, dimensionRow);
        }

        // 输出原始数据
        out.collect(value);
    }

    /**
     * 从Row中获取IP地址
     *
     * @param row 输入Row
     * @return IP地址字符串
     */
    private String getIpFromRow(Row row) {
        try {
            Object ipValue = row.getField(ipFieldName);
            return ipValue != null ? ipValue.toString() : null;
        } catch (Exception e) {
            log.warn("无法从Row中获取IP字段 {}: {}", ipFieldName, e.getMessage());
            return null;
        }
    }

    /**
     * 获取丰富化的IP信息，优先从缓存获取
     *
     * @param ipAddress IP地址
     * @return 丰富化的IP信息
     * @throws Exception 状态访问异常
     */
    private Map<String, Object> getEnrichedIpInfo(String ipAddress) throws Exception {
        // 检查缓存
        Map<String, Object> cachedInfo = ipInfoCache.get(ipAddress);
        if (cachedInfo != null) {
            return cachedInfo;
        }

        // 缓存未命中，重新查询并缓存
        Map<String, Object> enrichedInfo = enrichIpInfo(ipAddress);
        ipInfoCache.put(ipAddress, enrichedInfo);

        return enrichedInfo;
    }

    /**
     * 丰富IP信息
     *
     * @param ipAddress IP地址
     * @return 丰富后的IP信息Map
     */
    private Map<String, Object> enrichIpInfo(String ipAddress) {
        Map<String, Object> ipInfo = new HashMap<>(16);
        ipInfo.put("ip", ipAddress);
        ipInfo.put("is_valid", true);

        // 检查是否为内网IP
        boolean isInternal = IpUtils.isInternalIp(ipAddress);
        ipInfo.put("is_internal", isInternal);

        if (isInternal) {
            ipInfo.put("country", "内网");
            ipInfo.put("province", "内网");
            ipInfo.put("city", "内网");
            return ipInfo;
        }

        // 添加地理位置信息
        enrichGeoInfo(ipAddress, ipInfo);
        return ipInfo;
    }

    /**
     * 丰富IP地理位置信息
     *
     * @param ipAddress IP地址
     * @param ipInfo    IP信息Map
     */
    private void enrichGeoInfo(String ipAddress, Map<String, Object> ipInfo) {
        try {
            // 获取城市信息
            CityResponse cityResponse = GeoIpUtils.getAddrInfo(ipAddress);
            if (cityResponse != null) {
                addCityInfo(cityResponse, ipInfo);
            }

            // 获取ASN信息
            AsnResponse asnResponse = GeoIpUtils.getAsnInfo(ipAddress);
            if (asnResponse != null) {
                addAsnInfo(asnResponse, ipInfo);
            }
        } catch (Exception e) {
            log.warn("获取IP地理信息失败: {}", ipAddress, e);
        }
    }

    /**
     * 添加城市信息到IP信息Map
     *
     * @param cityResponse 城市响应
     * @param ipInfo       IP信息Map
     */
    private void addCityInfo(CityResponse cityResponse, Map<String, Object> ipInfo) {
        // 添加国家信息
        String country = GeoIpUtils.getCountry(cityResponse);
        if (!StringUtils.isEmpty(country)) {
            ipInfo.put("country", country);
        }

        // 添加国家代码
        String countryCode = cityResponse.getCountry().getIsoCode();
        if (!StringUtils.isEmpty(countryCode)) {
            ipInfo.put("country_code", countryCode);
        }

        // 添加省份信息
        String province = GeoIpUtils.getProvince(cityResponse);
        if (!StringUtils.isEmpty(province)) {
            ipInfo.put("province", province);
        }

        // 添加城市信息
        String city = GeoIpUtils.getCity(cityResponse);
        if (!StringUtils.isEmpty(city)) {
            ipInfo.put("city", city);
        }

        // 添加经纬度
        Double latitude = GeoIpUtils.getLatitude(cityResponse);
        Double longitude = GeoIpUtils.getLongitude(cityResponse);
        if (latitude != 0.0 && longitude != 0.0) {
            ipInfo.put("latitude", latitude);
            ipInfo.put("longitude", longitude);
        }

        // 添加邮政编码
        if (cityResponse.getPostal() != null) {
            String postalCode = cityResponse.getPostal().getCode();
            if (!StringUtils.isEmpty(postalCode)) {
                ipInfo.put("postal_code", postalCode);
            }
        }

        // 添加大洲信息
        if (cityResponse.getContinent() != null && cityResponse.getContinent().getNames() != null) {
            String continent = cityResponse.getContinent().getNames().get("zh-CN");
            if (!StringUtils.isEmpty(continent)) {
                ipInfo.put("continent", continent);
            }
        }
    }

    /**
     * 添加ASN信息到IP信息Map
     *
     * @param asnResponse ASN响应
     * @param ipInfo      IP信息Map
     */
    private void addAsnInfo(AsnResponse asnResponse, Map<String, Object> ipInfo) {
        String asn = GeoIpUtils.getAsn(asnResponse);
        if (!StringUtils.isEmpty(asn)) {
            ipInfo.put("asn", asn);
        }

        String asnOrg = asnResponse.getAutonomousSystemOrganization();
        if (!StringUtils.isEmpty(asnOrg)) {
            ipInfo.put("asn_org", asnOrg);
        }
    }

    /**
     * 创建符合维度表结构的IP维度记录
     *
     * @param ipAddress      IP地址
     * @param enrichmentInfo 丰富化信息
     * @return 符合dim_ipv4/dim_ipv6表结构的Row
     */
    private Row createDimensionRow(String ipAddress, Map<String, Object> enrichmentInfo) {
        Row dimensionRow = Row.withNames();

        // 设置主键字段
        dimensionRow.setField("ip", ipAddress);

        // 设置地理位置信息字段
        dimensionRow.setField("city", enrichmentInfo.get("city"));
        dimensionRow.setField("country", enrichmentInfo.get("country"));
        dimensionRow.setField("country_code", enrichmentInfo.get("country_code"));
        dimensionRow.setField("province", enrichmentInfo.get("province"));
        dimensionRow.setField("latitude", enrichmentInfo.get("latitude"));
        dimensionRow.setField("longitude", enrichmentInfo.get("longitude"));
        dimensionRow.setField("postal_code", enrichmentInfo.get("postal_code"));
        dimensionRow.setField("continent", enrichmentInfo.get("continent"));

        // 设置ASN信息字段
        dimensionRow.setField("asn", enrichmentInfo.get("asn"));
        dimensionRow.setField("asn_org", enrichmentInfo.get("asn_org"));

        // 设置是否内网IP
        dimensionRow.setField("is_internal", enrichmentInfo.get("is_internal"));

        // 设置默认值字段
        dimensionRow.setField("threat_score", null);
        dimensionRow.setField("trust_score", null);
        dimensionRow.setField("remark", null);

        // 设置聚合字段初始值
        dimensionRow.setField("total_bytes", 0L);
        dimensionRow.setField("total_packets", 0L);
        dimensionRow.setField("recv_total_bytes", 0L);
        dimensionRow.setField("send_total_bytes", 0L);
        dimensionRow.setField("session_count", 1);
        dimensionRow.setField("total_duration", 0L);

        // 设置时间字段
        DateTimeFormatter formatter = DateTimeUtils.DEFAULT_DATE_TIME_FORMATTER != null
                ? DateTimeUtils.DEFAULT_DATE_TIME_FORMATTER
                : DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String currentTime = LocalDateTime.now().format(formatter);
        dimensionRow.setField("first_seen", currentTime);
        dimensionRow.setField("last_seen", currentTime);
        dimensionRow.setField("create_time", currentTime);
        dimensionRow.setField("update_time", currentTime);

        return dimensionRow;
    }
}
