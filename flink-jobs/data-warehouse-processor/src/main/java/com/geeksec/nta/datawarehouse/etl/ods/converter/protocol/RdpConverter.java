package com.geeksec.nta.datawarehouse.etl.ods.converter.protocol;

import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import com.geeksec.common.utils.time.DateTimeUtils;
import com.geeksec.nta.datawarehouse.etl.constant.FieldConstants;
import com.geeksec.nta.datawarehouse.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.datawarehouse.etl.ods.tag.MessageOutputTag;
import com.geeksec.proto.ZMPNMsg;

/**
 * Converter for RDP protocol messages.
 * Maps RDP protocol data from protobuf messages to Doris
 * ods_rdp_protocol_metadata table format.
 * This converter is aligned with the latest ods_rdp_protocol_metadata table
 * schema.
 *
 * <AUTHOR> Team
 */
public class RdpConverter extends AbstractProtobufMessageConverter {

    @Override
    protected Row convertMessage(ZMPNMsg.JKNmsg msg) {
        ZMPNMsg.rdp_msg rdp = msg.getRdp();
        ZMPNMsg.Comm_msg commMsg = rdp.getCommMsg();

        Row row = Row.withNames();

        // Common fields from Comm_msg
        row.setField(FieldConstants.FIELD_SESSION_ID, commMsg.getSessionId());
        row.setField(FieldConstants.FIELD_BEGIN_TIME, DateTimeUtils.fromTimestampSeconds(commMsg.getBeginTime()));
        row.setField(FieldConstants.FIELD_SRC_IP, commMsg.getSrcIp());
        row.setField(FieldConstants.FIELD_DST_IP, commMsg.getDstIp());
        row.setField(FieldConstants.FIELD_SRC_PORT, commMsg.getSrcPort());
        row.setField(FieldConstants.FIELD_DST_PORT, commMsg.getDstPort());
        row.setField(FieldConstants.FIELD_IP_PROTOCOL, commMsg.getIppro());
        row.setField(FieldConstants.FIELD_SERVER_IP, commMsg.getServerIp());
        row.setField(FieldConstants.FIELD_APP_ID, commMsg.getAppId());
        row.setField(FieldConstants.FIELD_APP_NAME, commMsg.getAppName());
        row.setField(FieldConstants.FIELD_THREAD_ID, commMsg.getThreadId());
        row.setField(FieldConstants.FIELD_TASK_ID, commMsg.getTaskId());
        row.setField(FieldConstants.FIELD_BEGIN_NSEC, commMsg.getBeginNsec());
        row.setField(FieldConstants.FIELD_BATCH_ID, commMsg.getBatchId());

        // RDP client fields (rdp_client_msg)
        if (rdp.hasRdpClient()) {
            ZMPNMsg.rdp_client_msg client = rdp.getRdpClient();
            row.setField(FieldConstants.FIELD_CLIENT_VERSION_MAJOR, client.getVersionMajor());
            row.setField(FieldConstants.FIELD_CLIENT_VERSION_MINOR, client.getVersionMinor());
            row.setField(FieldConstants.FIELD_DESKTOP_WIDTH, client.getDesktopWidth());
            row.setField(FieldConstants.FIELD_DESKTOP_HEIGHT, client.getDesktopHeight());
            row.setField(FieldConstants.FIELD_COLOR_DEPTH, client.getColorDepth());
            row.setField(FieldConstants.FIELD_SAS_SEQUENCE, client.getSasSequence());
            row.setField(FieldConstants.FIELD_KEYBOARD_LAYOUT, client.getKeyboardLayout());
            row.setField(FieldConstants.FIELD_CLIENT_BUILD, client.getClientBuild());
            row.setField(FieldConstants.FIELD_CLIENT_NAME, client.getClientName());
            row.setField(FieldConstants.FIELD_KEYBOARD_TYPE, client.getKeyboardType());
            row.setField(FieldConstants.FIELD_KEYBOARD_SUBTYPE, client.getKeyboardSubtype());
            row.setField(FieldConstants.FIELD_KEYBOARD_FUNCKEY, client.getKeyboardFunckey());
            row.setField(FieldConstants.FIELD_IME_FILENAME, client.getImeFilename());
            row.setField(FieldConstants.FIELD_CLIENT_PRODUCTID, client.getClientProductid());
            row.setField(FieldConstants.FIELD_CONNECTION_TYPE, client.getConnectionType());
            row.setField(FieldConstants.FIELD_ENCRYPTION_METHODS, client.getEncryptionMethods());
            row.setField(FieldConstants.FIELD_CHANNEL_COUNT, client.getChannelCount());
            row.setField(FieldConstants.FIELD_COOKIE, bytesToBase64String(client.getCookie()));
        }

        // RDP server fields (rdp_server_msg)
        if (rdp.hasRdpServer()) {
            ZMPNMsg.rdp_server_msg server = rdp.getRdpServer();
            row.setField(FieldConstants.FIELD_SERVER_VERSION_MAJOR, server.getVersionMajor());
            row.setField(FieldConstants.FIELD_SERVER_VERSION_MINOR, server.getVersionMinor());
            row.setField(FieldConstants.FIELD_MCS_CHANNELID, server.getMcsChannelid());
            row.setField(FieldConstants.FIELD_SERVER_CHANNEL_COUNT, server.getChannelCount());
            row.setField(FieldConstants.FIELD_ENCRYPTION_METHOD, server.getEncryptionMethod());
            row.setField(FieldConstants.FIELD_ENCRYPTION_LEVEL, server.getEncryptionLevel());
            row.setField(FieldConstants.FIELD_SERVER_RANDOMLEN, server.getServerRandomlen());
            row.setField(FieldConstants.FIELD_SERVER_CERTLEN, server.getServerCertlen());
            row.setField(FieldConstants.FIELD_SERVER_RANDOM, bytesToBase64String(server.getServerRandom()));
            row.setField(FieldConstants.FIELD_SERVER_CERT, bytesToBase64String(server.getServerCert()));
            row.setField(FieldConstants.FIELD_SELECTED_PROTOCOLS, server.getSelectedProtocols());
        }

        // RDP negotiate fields (rdp_negotiate_msg)
        if (rdp.hasRdpNegotiate()) {
            ZMPNMsg.rdp_negotiate_msg negotiate = rdp.getRdpNegotiate();
            row.setField(FieldConstants.FIELD_C_FLAG_PROTOCOLS,
                    convertProtoListToJavaList(negotiate.getCFlagProtocolsList()));
            row.setField(FieldConstants.FIELD_C_REQUESTED_PROTOCOLS,
                    convertProtoListToJavaList(negotiate.getCRequestedProtocolsList()));
            row.setField(FieldConstants.FIELD_S_FLAG_PROTOCOLS,
                    convertProtoListToJavaList(negotiate.getSFlagProtocolsList()));
            row.setField(FieldConstants.FIELD_S_SELECTED_PROTOCOLS,
                    convertProtoListToJavaList(negotiate.getSSelectedProtocolsList()));
        }

        return row;
    }

    @Override
    public OutputTag<Row> getOutputTag() {
        return MessageOutputTag.RDP_STREAM;
    }
}
