package com.geeksec.nta.datawarehouse.etl.dim.function;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.geeksec.common.utils.net.MacUtils;

/**
 * MAC地址丰富化功能使用示例
 * 展示如何使用MacUtils和MacDimensionEnrichmentFunction
 *
 * <AUTHOR>
 */
public class MacEnrichmentExample {

    private static final Logger log = LoggerFactory.getLogger(MacEnrichmentExample.class);

    public static void main(String[] args) {
        // 示例MAC地址列表
        String[] macAddresses = {
            "F0:EE:7A:12:34:56",  // Apple
            "E8:0A:B9:11:22:33",  // Cisco
            "E4:C7:67:44:55:66",  // Intel
            "CC:EB:5E:77:88:99",  // Xiaomi
            "10:06:1C:AA:BB:CC",  // Espressif (IoT)
            "02:00:00:12:34:56",  // 本地管理地址
            "01:00:5E:12:34:56",  // 组播地址
            "FF:FF:FF:12:34:56",  // 未知厂商
            "invalid-mac",        // 无效MAC地址
        };

        log.info("=== MAC地址丰富化功能演示 ===");
        log.info("OUI数据库已加载 {} 条记录", MacUtils.getOuiCacheSize());
        log.info("");

        for (String macAddress : macAddresses) {
            demonstrateMacEnrichment(macAddress);
            log.info("");
        }
    }

    /**
     * 演示MAC地址丰富化功能
     *
     * @param macAddress MAC地址
     */
    private static void demonstrateMacEnrichment(String macAddress) {
        log.info("处理MAC地址: {}", macAddress);

        // 1. 基本验证
        boolean isValid = MacUtils.isValidMac(macAddress);
        log.info("  格式有效性: {}", isValid);

        if (!isValid) {
            log.info("  跳过无效MAC地址");
            return;
        }

        // 2. 标准化
        String normalizedMac = MacUtils.normalizeMac(macAddress);
        log.info("  标准化格式: {}", normalizedMac);

        // 3. 获取OUI
        String oui = MacUtils.getOui(macAddress);
        log.info("  OUI: {}", oui);

        // 4. 获取厂商信息
        String vendor = MacUtils.getVendor(macAddress);
        log.info("  厂商: {}", vendor);

        // 5. 地址类型分析
        boolean isLocallyAdministered = MacUtils.isLocallyAdministered(macAddress);
        boolean isMulticast = MacUtils.isMulticast(macAddress);
        log.info("  本地管理地址: {}", isLocallyAdministered);
        log.info("  组播地址: {}", isMulticast);

        // 6. 获取完整信息
        Map<String, Object> macInfo = MacUtils.getMacInfo(macAddress);
        log.info("  完整信息: {}", macInfo);

        // 7. 设备类型推断（使用MacDimensionTableFunction的逻辑）
        String deviceType = inferDeviceType(vendor);
        log.info("  推断设备类型: {}", deviceType);
    }

    /**
     * 推断设备类型（简化版本，与MacDimensionTableFunction中的逻辑类似）
     *
     * @param vendor 厂商名称
     * @return 设备类型
     */
    private static String inferDeviceType(String vendor) {
        if (vendor == null || "Unknown".equals(vendor)) {
            return "Unknown";
        }

        String vendorLower = vendor.toLowerCase();

        if (vendorLower.contains("apple") || vendorLower.contains("iphone") || vendorLower.contains("ipad")) {
            return "Mobile Device";
        } else if (vendorLower.contains("cisco") || vendorLower.contains("juniper") || vendorLower.contains("huawei")) {
            return "Network Equipment";
        } else if (vendorLower.contains("intel") || vendorLower.contains("realtek") || vendorLower.contains("broadcom")) {
            return "Network Interface";
        } else if (vendorLower.contains("samsung") || vendorLower.contains("xiaomi") || vendorLower.contains("honor")) {
            return "Mobile Device";
        } else if (vendorLower.contains("hp") || vendorLower.contains("dell") || vendorLower.contains("lenovo")) {
            return "Computer";
        } else if (vendorLower.contains("nintendo") || vendorLower.contains("sony")) {
            return "Gaming Device";
        } else if (vendorLower.contains("espressif") || vendorLower.contains("arduino")) {
            return "IoT Device";
        }

        return "Unknown";
    }

    /**
     * 演示在Flink流处理中如何使用MAC丰富化功能
     */
    public static void demonstrateFlinkUsage() {
        log.info("=== Flink流处理中的MAC丰富化使用示例 ===");
        log.info("");
        log.info("1. 在Flink作业中使用MacDimensionEnrichmentFunction:");
        log.info("   DataStream<Row> enrichedStream = originalStream");
        log.info("       .process(new MacDimensionEnrichmentFunction(\"src_mac\"));");
        log.info("");
        log.info("2. 在Flink作业中使用MacDimensionTableFunction:");
        log.info("   SingleOutputStreamOperator<Row> mainStream = originalStream");
        log.info("       .process(new MacDimensionTableFunction(\"src_mac\"));");
        log.info("   DataStream<Row> macDimStream = mainStream");
        log.info("       .getSideOutput(MacDimensionTableFunction.MAC_DIM_TAG);");
        log.info("");
        log.info("3. 丰富化后的数据包含以下字段:");
        log.info("   - enriched_mac: 标准化的MAC地址");
        log.info("   - enriched_is_valid: MAC地址格式是否有效");
        log.info("   - enriched_oui: OUI（前3个字节）");
        log.info("   - enriched_vendor: 厂商名称");
        log.info("   - enriched_device_type: 推断的设备类型");
        log.info("   - enriched_is_locally_administered: 是否为本地管理地址");
        log.info("   - enriched_is_multicast: 是否为组播地址");
        log.info("   - enriched_is_potentially_randomized: 是否可能为随机MAC");
        log.info("   - enriched_is_virtualized: 是否为虚拟化厂商");
    }
}
