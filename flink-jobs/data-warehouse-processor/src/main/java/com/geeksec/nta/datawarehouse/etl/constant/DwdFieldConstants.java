package com.geeksec.nta.datawarehouse.etl.constant;

/**
 * DWD层字段名常量定义
 * 对应dwd_session_logs表和相关DWD层表的字段名
 *
 * <AUTHOR>
 */
public final class DwdFieldConstants {

    // ========== 基础会话标识字段 ==========

    /** protocol字段 */
    public static final String PROTOCOL = "protocol";
    /** session_start_time字段 */
    public static final String SESSION_START_TIME = "session_start_time";
    /** session_start_nsec字段 */
    public static final String SESSION_START_NSEC = "session_start_nsec";
    /** session_end_time字段 */
    public static final String SESSION_END_TIME = "session_end_time";
    /** session_end_nsec字段 */
    public static final String SESSION_END_NSEC = "session_end_nsec";

    // ========== 会话基础信息字段 ==========

    /** src_mac字段 */
    public static final String SRC_MAC = "src_mac";
    /** dst_mac字段 */
    public static final String DST_MAC = "dst_mac";
    /** rule_count字段 */
    public static final String RULE_COUNT = "rule_count";
    /** syn_data字段 */
    public static final String SYN_DATA = "syn_data";
    /** syn_ack_data字段 */
    public static final String SYN_ACK_DATA = "syn_ack_data";
    /** rule_messages字段 */
    public static final String RULE_MESSAGES = "rule_messages";

    // ========== 会话统计信息字段 ==========

    /** src_total_sign字段 */
    public static final String SRC_TOTAL_SIGN = "src_total_sign";
    /** dst_total_sign字段 */
    public static final String DST_TOTAL_SIGN = "dst_total_sign";
    /** payload_bytes_distribution字段 */
    public static final String PAYLOAD_BYTES_DISTRIBUTION = "payload_bytes_distribution";
    /** payload_bytes_count字段 */
    public static final String PAYLOAD_BYTES_COUNT = "payload_bytes_count";
    /** distribution_csq字段 */
    public static final String DISTRIBUTION_CSQ = "distribution_csq";
    /** distribution_csqt字段 */
    public static final String DISTRIBUTION_CSQT = "distribution_csqt";
    /** src_payload_length_distribution字段 */
    public static final String SRC_PAYLOAD_LENGTH_DISTRIBUTION = "src_payload_length_distribution";
    /** dst_payload_length_distribution字段 */
    public static final String DST_PAYLOAD_LENGTH_DISTRIBUTION = "dst_payload_length_distribution";
    /** src_duration_distribution字段 */
    public static final String SRC_DURATION_DISTRIBUTION = "src_duration_distribution";
    /** dst_duration_distribution字段 */
    public static final String DST_DURATION_DISTRIBUTION = "dst_duration_distribution";
    /** duration_distribution字段 */
    public static final String DURATION_DISTRIBUTION = "duration_distribution";
    /** protocol_stack_count字段 */
    public static final String PROTOCOL_STACK_COUNT = "protocol_stack_count";
    /** protocol_stack字段 */
    public static final String PROTOCOL_STACK = "protocol_stack";
    /** src_is_internal字段 */
    public static final String SRC_IS_INTERNAL = "src_is_internal";
    /** dst_is_internal字段 */
    public static final String DST_IS_INTERNAL = "dst_is_internal";
    /** extension_data字段 */
    public static final String EXTENSION_DATA = "extension_data";

    // ========== TCP连接信息字段 ==========

    /** src_mss字段 */
    public static final String SRC_MSS = "src_mss";
    /** dst_mss字段 */
    public static final String DST_MSS = "dst_mss";
    /** src_window_scale字段 */
    public static final String SRC_WINDOW_SCALE = "src_window_scale";
    /** dst_window_scale字段 */
    public static final String DST_WINDOW_SCALE = "dst_window_scale";
    /** src_payload_max_length字段 */
    public static final String SRC_PAYLOAD_MAX_LENGTH = "src_payload_max_length";
    /** dst_payload_max_length字段 */
    public static final String DST_PAYLOAD_MAX_LENGTH = "dst_payload_max_length";
    /** src_ack_payload_max_length字段 */
    public static final String SRC_ACK_PAYLOAD_MAX_LENGTH = "src_ack_payload_max_length";
    /** dst_ack_payload_max_length字段 */
    public static final String DST_ACK_PAYLOAD_MAX_LENGTH = "dst_ack_payload_max_length";
    /** src_ack_payload_min_length字段 */
    public static final String SRC_ACK_PAYLOAD_MIN_LENGTH = "src_ack_payload_min_length";
    /** dst_ack_payload_min_length字段 */
    public static final String DST_ACK_PAYLOAD_MIN_LENGTH = "dst_ack_payload_min_length";
    /** tcp_connection_info字段 */
    public static final String TCP_CONNECTION_INFO = "tcp_connection_info";
    /** syn_sequence_numbers字段 */
    public static final String SYN_SEQUENCE_NUMBERS = "syn_sequence_numbers";
    /** syn_sequence_count字段 */
    public static final String SYN_SEQUENCE_COUNT = "syn_sequence_count";
    /** src_ip_id_offsets字段 */
    public static final String SRC_IP_ID_OFFSETS = "src_ip_id_offsets";
    /** dst_ip_id_offsets字段 */
    public static final String DST_IP_ID_OFFSETS = "dst_ip_id_offsets";
    /** ssl_block_ciphers字段 */
    public static final String SSL_BLOCK_CIPHERS = "ssl_block_ciphers";

    // ========== 会话扩展信息字段 ==========

    /** session_duration字段 */
    public static final String SESSION_DURATION = "session_duration";
    /** first_packet_sender字段 */
    public static final String FIRST_PACKET_SENDER = "first_packet_sender";
    /** first_layer_protocol字段 */
    public static final String FIRST_LAYER_PROTOCOL = "first_layer_protocol";
    /** proxy_real_hostname字段 */
    public static final String PROXY_REAL_HOSTNAME = "proxy_real_hostname";
    /** processing_start_time字段 */
    public static final String PROCESSING_START_TIME = "processing_start_time";
    /** processing_end_time字段 */
    public static final String PROCESSING_END_TIME = "processing_end_time";

    // ========== 协议指纹字段 ==========

    /** tcp_client_fingerprint字段 */
    public static final String TCP_CLIENT_FINGERPRINT = "tcp_client_fingerprint";
    /** tcp_server_fingerprint字段 */
    public static final String TCP_SERVER_FINGERPRINT = "tcp_server_fingerprint";
    /** http_client_fingerprint字段 */
    public static final String HTTP_CLIENT_FINGERPRINT = "http_client_fingerprint";
    /** http_server_fingerprint字段 */
    public static final String HTTP_SERVER_FINGERPRINT = "http_server_fingerprint";
    /** ssl_client_fingerprint字段 */
    public static final String SSL_CLIENT_FINGERPRINT = "ssl_client_fingerprint";
    /** ssl_server_fingerprint字段 */
    public static final String SSL_SERVER_FINGERPRINT = "ssl_server_fingerprint";

    /**
     * 防止实例化
     */
    private DwdFieldConstants() {
        throw new IllegalStateException("Utility class");
    }
}
