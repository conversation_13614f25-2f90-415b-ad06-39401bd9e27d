package com.geeksec.nta.datawarehouse.etl.dim.function;

import java.net.URI;
import java.net.URISyntaxException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import com.geeksec.common.utils.time.DateTimeUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * URL维度表处理函数，用于生成符合dim_url表结构的维度数据
 *
 * 使用Flink Backend State进行缓存管理，支持TTL自动清理
 *
 * <AUTHOR>
 */
@Slf4j
public class UrlDimensionTableFunction extends ProcessFunction<Row, Row> {

    private static final long serialVersionUID = 1L;

    /**
     * URL维度数据输出标签
     */
    public static final OutputTag<Row> URL_DIM_TAG = new OutputTag<Row>("url-dimension") {};

    private final String urlFieldName;
    private final Duration ttl;

    /**
     * 缓存URL维度数据的状态
     */
    private transient ValueState<Map<String, Object>> urlDimensionState;

    /**
     * 构造函数
     *
     * @param urlFieldName URL字段名
     */
    public UrlDimensionTableFunction(String urlFieldName) {
        // 默认12小时TTL
        this(urlFieldName, Duration.ofHours(12));
    }

    /**
     * 构造函数
     *
     * @param urlFieldName URL字段名
     * @param ttl 状态TTL时间
     */
    public UrlDimensionTableFunction(String urlFieldName, Duration ttl) {
        this.urlFieldName = urlFieldName;
        this.ttl = ttl;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 配置状态TTL
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(ttl)
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();

        // 初始化URL维度状态
        ValueStateDescriptor<Map<String, Object>> urlStateDescriptor =
                new ValueStateDescriptor<>("url-dimension-state", Types.MAP(Types.STRING, Types.GENERIC(Object.class)));
        urlStateDescriptor.enableTimeToLive(ttlConfig);
        urlDimensionState = getRuntimeContext().getState(urlStateDescriptor);
    }

    @Override
    public void processElement(Row value, Context ctx, Collector<Row> out) throws Exception {
        try {
            String url = getStringFieldValue(value, urlFieldName);
            if (url == null || url.trim().isEmpty()) {
                out.collect(value);
                return;
            }

            // 生成URL键值
            String urlKey = generateUrlKey(url);

            // 检查状态中是否已存在该URL的维度数据
            Map<String, Object> existingUrlInfo = urlDimensionState.value();
            if (existingUrlInfo == null) {
                // 首次遇到该URL，创建维度数据
                Map<String, Object> urlInfo = createUrlDimensionInfo(url);
                urlDimensionState.update(urlInfo);

                // 创建维度表记录并输出到侧输出流
                Row urlDimensionRow = createDimensionRow(urlKey, url, urlInfo);
                ctx.output(URL_DIM_TAG, urlDimensionRow);

                log.debug("创建新的URL维度数据: {}", url);
            }

            // 继续传递原始数据
            out.collect(value);

        } catch (Exception e) {
            log.error("处理URL维度数据时发生错误: {}", e.getMessage(), e);
            out.collect(value);
        }
    }

    /**
     * 创建URL维度信息
     *
     * @param url URL字符串
     * @return URL维度信息Map
     */
    private Map<String, Object> createUrlDimensionInfo(String url) {
        Map<String, Object> urlInfo = new HashMap<>(16);

        try {
            // 确保URL有协议前缀
            String normalizedUrl = url.startsWith("http") ? url : "http://" + url;
            URI uri = new URI(normalizedUrl);

            // 解析URL组件
            urlInfo.put("url_string", url);
            urlInfo.put("domain", uri.getHost());
            urlInfo.put("path", uri.getPath() != null ? uri.getPath() : "");
            urlInfo.put("query_params", uri.getQuery() != null ? uri.getQuery() : "");
            urlInfo.put("protocol", uri.getScheme() != null ? uri.getScheme() : "http");

        } catch (URISyntaxException e) {
            log.warn("解析URL失败: {}, 错误: {}", url, e.getMessage());
            // 设置默认值
            urlInfo.put("url_string", url);
            urlInfo.put("domain", "Unknown");
            urlInfo.put("path", "");
            urlInfo.put("query_params", "");
            urlInfo.put("protocol", "http");
        }

        return urlInfo;
    }

    /**
     * 创建符合维度表结构的URL维度记录
     *
     * @param urlKey URL键值
     * @param url 原始URL字符串
     * @param urlInfo URL信息
     * @return 符合dim_url表结构的Row
     */
    private Row createDimensionRow(String urlKey, String url, Map<String, Object> urlInfo) {
        Row dimensionRow = Row.withNames();

        // 设置主键字段
        dimensionRow.setField("url_key", urlKey);

        // 设置URL信息字段
        dimensionRow.setField("url_string", urlInfo.get("url_string"));
        dimensionRow.setField("domain", urlInfo.get("domain"));
        dimensionRow.setField("path", urlInfo.get("path"));
        dimensionRow.setField("query_params", urlInfo.get("query_params"));
        dimensionRow.setField("protocol", urlInfo.get("protocol"));

        // 设置聚合字段初始值
        dimensionRow.setField("total_requests", 1);
        dimensionRow.setField("total_bytes", 0L);
        dimensionRow.setField("avg_response_time", 0.0);

        // 设置时间字段
        DateTimeFormatter formatter = DateTimeUtils.DEFAULT_DATE_TIME_FORMATTER != null
                ? DateTimeUtils.DEFAULT_DATE_TIME_FORMATTER
                : DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String currentTime = LocalDateTime.now().format(formatter);
        dimensionRow.setField("first_seen", currentTime);
        dimensionRow.setField("last_seen", currentTime);
        dimensionRow.setField("create_time", currentTime);
        dimensionRow.setField("update_time", currentTime);

        return dimensionRow;
    }

    /**
     * 生成URL键值
     *
     * @param url URL字符串
     * @return URL键值
     */
    private String generateUrlKey(String url) {
        return UUID.nameUUIDFromBytes(url.getBytes()).toString();
    }

    /**
     * 从Row中获取字符串字段值
     *
     * @param row Row对象
     * @param fieldName 字段名
     * @return 字段值，如果不存在或类型不匹配则返回null
     */
    private String getStringFieldValue(Row row, String fieldName) {
        try {
            Set<String> fieldNames = row.getFieldNames(true);
            if (fieldNames != null && fieldNames.contains(fieldName)) {
                Object fieldValue = row.getField(fieldName);
                if (fieldValue instanceof String) {
                    return (String) fieldValue;
                }
            }
        } catch (Exception e) {
            log.warn("获取字符串字段值时发生错误，字段名: {}, 错误: {}", fieldName, e.getMessage());
        }
        return null;
    }
}
