package com.geeksec.nta.datawarehouse.etl.dim.config;

import java.time.Duration;
import java.util.Properties;

/**
 * 维度丰富化配置类
 * 管理IP和域名维度丰富化的相关配置参数
 *
 * <AUTHOR>
 */
public class DimensionEnrichmentConfig {

    /** 默认缓存TTL时间 */
    public static final Duration DEFAULT_CACHE_TTL = Duration.ofHours(24);
    
    /** 默认IP缓存TTL时间 */
    public static final Duration DEFAULT_IP_CACHE_TTL = Duration.ofHours(12);
    
    /** 默认域名缓存TTL时间 */
    public static final Duration DEFAULT_DOMAIN_CACHE_TTL = Duration.ofHours(24);
    
    /** 配置键前缀 */
    private static final String CONFIG_PREFIX = "dimension.enrichment.";
    
    /** IP丰富化配置键 */
    public static final String IP_ENRICHMENT_ENABLED = CONFIG_PREFIX + "ip.enabled";
    public static final String IP_CACHE_TTL_HOURS = CONFIG_PREFIX + "ip.cache.ttl.hours";
    public static final String IP_FIELD_NAME = CONFIG_PREFIX + "ip.field.name";
    
    /** 域名丰富化配置键 */
    public static final String DOMAIN_ENRICHMENT_ENABLED = CONFIG_PREFIX + "domain.enabled";
    public static final String DOMAIN_CACHE_TTL_HOURS = CONFIG_PREFIX + "domain.cache.ttl.hours";
    public static final String DOMAIN_FIELD_NAME = CONFIG_PREFIX + "domain.field.name";
    
    /** 通用配置键 */
    public static final String ENRICHMENT_PARALLELISM = CONFIG_PREFIX + "parallelism";
    public static final String ENRICHMENT_FIELD_PREFIX = CONFIG_PREFIX + "field.prefix";
    
    /** 配置属性 */
    private final Properties properties;
    
    /**
     * 构造函数
     *
     * @param properties 配置属性
     */
    public DimensionEnrichmentConfig(Properties properties) {
        this.properties = properties != null ? properties : new Properties();
    }
    
    /**
     * 创建默认配置
     *
     * @return 默认配置实例
     */
    public static DimensionEnrichmentConfig createDefault() {
        Properties defaultProps = new Properties();
        
        // IP丰富化默认配置
        defaultProps.setProperty(IP_ENRICHMENT_ENABLED, "true");
        defaultProps.setProperty(IP_CACHE_TTL_HOURS, "12");
        defaultProps.setProperty(IP_FIELD_NAME, "src_ip");
        
        // 域名丰富化默认配置
        defaultProps.setProperty(DOMAIN_ENRICHMENT_ENABLED, "true");
        defaultProps.setProperty(DOMAIN_CACHE_TTL_HOURS, "24");
        defaultProps.setProperty(DOMAIN_FIELD_NAME, "domain");
        
        // 通用配置
        defaultProps.setProperty(ENRICHMENT_PARALLELISM, "4");
        defaultProps.setProperty(ENRICHMENT_FIELD_PREFIX, "enriched_");
        
        return new DimensionEnrichmentConfig(defaultProps);
    }
    
    /**
     * 是否启用IP丰富化
     *
     * @return true如果启用，false否则
     */
    public boolean isIpEnrichmentEnabled() {
        return Boolean.parseBoolean(properties.getProperty(IP_ENRICHMENT_ENABLED, "true"));
    }
    
    /**
     * 获取IP缓存TTL时间
     *
     * @return TTL时间
     */
    public Duration getIpCacheTtl() {
        int hours = Integer.parseInt(properties.getProperty(IP_CACHE_TTL_HOURS, "12"));
        return Duration.ofHours(hours);
    }
    
    /**
     * 获取IP字段名
     *
     * @return IP字段名
     */
    public String getIpFieldName() {
        return properties.getProperty(IP_FIELD_NAME, "src_ip");
    }
    
    /**
     * 是否启用域名丰富化
     *
     * @return true如果启用，false否则
     */
    public boolean isDomainEnrichmentEnabled() {
        return Boolean.parseBoolean(properties.getProperty(DOMAIN_ENRICHMENT_ENABLED, "true"));
    }
    
    /**
     * 获取域名缓存TTL时间
     *
     * @return TTL时间
     */
    public Duration getDomainCacheTtl() {
        int hours = Integer.parseInt(properties.getProperty(DOMAIN_CACHE_TTL_HOURS, "24"));
        return Duration.ofHours(hours);
    }
    
    /**
     * 获取域名字段名
     *
     * @return 域名字段名
     */
    public String getDomainFieldName() {
        return properties.getProperty(DOMAIN_FIELD_NAME, "domain");
    }
    
    /**
     * 获取丰富化处理并行度
     *
     * @return 并行度
     */
    public int getEnrichmentParallelism() {
        return Integer.parseInt(properties.getProperty(ENRICHMENT_PARALLELISM, "4"));
    }
    
    /**
     * 获取丰富化字段前缀
     *
     * @return 字段前缀
     */
    public String getEnrichmentFieldPrefix() {
        return properties.getProperty(ENRICHMENT_FIELD_PREFIX, "enriched_");
    }
    
    /**
     * 设置IP丰富化启用状态
     *
     * @param enabled 是否启用
     */
    public void setIpEnrichmentEnabled(boolean enabled) {
        properties.setProperty(IP_ENRICHMENT_ENABLED, String.valueOf(enabled));
    }
    
    /**
     * 设置IP缓存TTL时间
     *
     * @param ttl TTL时间
     */
    public void setIpCacheTtl(Duration ttl) {
        properties.setProperty(IP_CACHE_TTL_HOURS, String.valueOf(ttl.toHours()));
    }
    
    /**
     * 设置IP字段名
     *
     * @param fieldName IP字段名
     */
    public void setIpFieldName(String fieldName) {
        properties.setProperty(IP_FIELD_NAME, fieldName);
    }
    
    /**
     * 设置域名丰富化启用状态
     *
     * @param enabled 是否启用
     */
    public void setDomainEnrichmentEnabled(boolean enabled) {
        properties.setProperty(DOMAIN_ENRICHMENT_ENABLED, String.valueOf(enabled));
    }
    
    /**
     * 设置域名缓存TTL时间
     *
     * @param ttl TTL时间
     */
    public void setDomainCacheTtl(Duration ttl) {
        properties.setProperty(DOMAIN_CACHE_TTL_HOURS, String.valueOf(ttl.toHours()));
    }
    
    /**
     * 设置域名字段名
     *
     * @param fieldName 域名字段名
     */
    public void setDomainFieldName(String fieldName) {
        properties.setProperty(DOMAIN_FIELD_NAME, fieldName);
    }
    
    /**
     * 设置丰富化处理并行度
     *
     * @param parallelism 并行度
     */
    public void setEnrichmentParallelism(int parallelism) {
        properties.setProperty(ENRICHMENT_PARALLELISM, String.valueOf(parallelism));
    }
    
    /**
     * 设置丰富化字段前缀
     *
     * @param prefix 字段前缀
     */
    public void setEnrichmentFieldPrefix(String prefix) {
        properties.setProperty(ENRICHMENT_FIELD_PREFIX, prefix);
    }
    
    /**
     * 获取所有配置属性
     *
     * @return 配置属性
     */
    public Properties getProperties() {
        return new Properties(properties);
    }
    
    /**
     * 从配置创建丰富化管理器配置
     *
     * @return 丰富化管理器配置
     */
    public com.geeksec.nta.datawarehouse.etl.dim.function.DimensionEnrichmentManager.EnrichmentConfig 
            createManagerConfig() {
        return new com.geeksec.nta.datawarehouse.etl.dim.function.DimensionEnrichmentManager.EnrichmentConfig(
                getIpFieldName(),
                getDomainFieldName(),
                Duration.ofHours(Math.max(getIpCacheTtl().toHours(), getDomainCacheTtl().toHours())),
                isIpEnrichmentEnabled(),
                isDomainEnrichmentEnabled()
        );
    }
    
    @Override
    public String toString() {
        return "DimensionEnrichmentConfig{" +
                "ipEnabled=" + isIpEnrichmentEnabled() +
                ", ipField='" + getIpFieldName() + '\'' +
                ", ipCacheTtl=" + getIpCacheTtl() +
                ", domainEnabled=" + isDomainEnrichmentEnabled() +
                ", domainField='" + getDomainFieldName() + '\'' +
                ", domainCacheTtl=" + getDomainCacheTtl() +
                ", parallelism=" + getEnrichmentParallelism() +
                ", fieldPrefix='" + getEnrichmentFieldPrefix() + '\'' +
                '}';
    }
}
