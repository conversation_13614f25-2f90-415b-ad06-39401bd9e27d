package com.geeksec.nta.datawarehouse.etl.dim.function;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import com.geeksec.common.utils.time.DateTimeUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 应用服务维度表处理函数，用于生成符合dim_app_service表结构的维度数据
 *
 * 使用Flink Backend State进行缓存管理，支持TTL自动清理
 *
 * <AUTHOR>
 */
@Slf4j
public class AppServiceDimensionTableFunction extends ProcessFunction<Row, Row> {

    private static final long serialVersionUID = 1L;

    /**
     * 应用服务维度数据输出标签
     */
    public static final OutputTag<Row> APP_SERVICE_DIM_TAG = new OutputTag<Row>("app-service-dimension") {};

    private final String serviceNameFieldName;
    private final Duration ttl;

    // State for caching App Service dimension data
    private transient ValueState<Map<String, Object>> appServiceDimensionState;

    /**
     * 构造函数
     *
     * @param serviceNameFieldName 服务名称字段名
     */
    public AppServiceDimensionTableFunction(String serviceNameFieldName) {
        this(serviceNameFieldName, Duration.ofHours(24)); // 默认24小时TTL
    }

    /**
     * 构造函数
     *
     * @param serviceNameFieldName 服务名称字段名
     * @param ttl 状态TTL时间
     */
    public AppServiceDimensionTableFunction(String serviceNameFieldName, Duration ttl) {
        this.serviceNameFieldName = serviceNameFieldName;
        this.ttl = ttl;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 配置状态TTL
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(ttl)
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();

        // 初始化应用服务维度状态
        ValueStateDescriptor<Map<String, Object>> appServiceStateDescriptor =
                new ValueStateDescriptor<>("app-service-dimension-state", Types.MAP(Types.STRING, Types.GENERIC(Object.class)));
        appServiceStateDescriptor.enableTimeToLive(ttlConfig);
        appServiceDimensionState = getRuntimeContext().getState(appServiceStateDescriptor);
    }

    @Override
    public void processElement(Row value, Context ctx, Collector<Row> out) throws Exception {
        try {
            String serviceName = getStringFieldValue(value, serviceNameFieldName);
            if (serviceName == null || serviceName.trim().isEmpty()) {
                serviceName = "Unknown"; // 默认值
            }

            // 检查状态中是否已存在该应用服务的维度数据
            Map<String, Object> existingAppServiceInfo = appServiceDimensionState.value();
            if (existingAppServiceInfo == null) {
                // 首次遇到该应用服务，创建维度数据
                Map<String, Object> appServiceInfo = createAppServiceDimensionInfo(serviceName);
                appServiceDimensionState.update(appServiceInfo);

                // 创建维度表记录并输出到侧输出流
                Row appServiceDimensionRow = createDimensionRow(serviceName, appServiceInfo);
                ctx.output(APP_SERVICE_DIM_TAG, appServiceDimensionRow);

                log.debug("创建新的应用服务维度数据: {}", serviceName);
            }

            // 继续传递原始数据
            out.collect(value);

        } catch (Exception e) {
            log.error("处理应用服务维度数据时发生错误: {}", e.getMessage(), e);
            out.collect(value);
        }
    }

    /**
     * 创建应用服务维度信息
     *
     * @param serviceName 服务名称
     * @return 应用服务维度信息Map
     */
    private Map<String, Object> createAppServiceDimensionInfo(String serviceName) {
        Map<String, Object> appServiceInfo = new HashMap<>();

        // 基础信息
        appServiceInfo.put("service_name", serviceName);

        // TODO: 可以在这里添加服务相关的额外信息
        // 例如服务分类、端口号、协议类型等
        appServiceInfo.put("service_category", getServiceCategory(serviceName));
        appServiceInfo.put("default_port", getDefaultPort(serviceName));

        return appServiceInfo;
    }

    /**
     * 根据服务名称获取服务分类
     *
     * @param serviceName 服务名称
     * @return 服务分类
     */
    private String getServiceCategory(String serviceName) {
        if (serviceName == null) {
            return "Unknown";
        }

        String lowerServiceName = serviceName.toLowerCase();
        if (lowerServiceName.contains("http") || lowerServiceName.contains("web")) {
            return "Web";
        } else if (lowerServiceName.contains("dns")) {
            return "DNS";
        } else if (lowerServiceName.contains("ssl") || lowerServiceName.contains("tls")) {
            return "Security";
        } else if (lowerServiceName.contains("ftp") || lowerServiceName.contains("ssh")) {
            return "File Transfer";
        } else if (lowerServiceName.contains("mail") || lowerServiceName.contains("smtp") || lowerServiceName.contains("pop")) {
            return "Email";
        } else {
            return "Other";
        }
    }

    /**
     * 根据服务名称获取默认端口
     *
     * @param serviceName 服务名称
     * @return 默认端口
     */
    private Integer getDefaultPort(String serviceName) {
        if (serviceName == null) {
            return null;
        }

        String lowerServiceName = serviceName.toLowerCase();
        return switch (lowerServiceName) {
            case "http" -> 80;
            case "https" -> 443;
            case "dns" -> 53;
            case "ftp" -> 21;
            case "ssh" -> 22;
            case "smtp" -> 25;
            case "pop3" -> 110;
            case "imap" -> 143;
            default -> null;
        };
    }

    /**
     * 创建符合维度表结构的应用服务维度记录
     *
     * @param serviceName 服务名称
     * @param appServiceInfo 应用服务信息
     * @return 符合dim_app_service表结构的Row
     */
    private Row createDimensionRow(String serviceName, Map<String, Object> appServiceInfo) {
        Row dimensionRow = Row.withNames();

        // 设置主键字段
        dimensionRow.setField("service_name", serviceName);

        // 设置服务信息字段
        dimensionRow.setField("service_category", appServiceInfo.get("service_category"));
        dimensionRow.setField("default_port", appServiceInfo.get("default_port"));

        // 设置聚合字段初始值
        dimensionRow.setField("total_bytes", 0L);
        dimensionRow.setField("total_packets", 0L);
        dimensionRow.setField("total_bytes_sent", 0L);
        dimensionRow.setField("total_bytes_received", 0L);
        dimensionRow.setField("total_packets_sent", 0L);
        dimensionRow.setField("total_packets_received", 0L);
        dimensionRow.setField("session_count", 1);
        dimensionRow.setField("total_duration", 0L);
        dimensionRow.setField("avg_bps", 0.0);

        // 设置时间字段
        DateTimeFormatter formatter = DateTimeUtils.DEFAULT_DATE_TIME_FORMATTER != null
                ? DateTimeUtils.DEFAULT_DATE_TIME_FORMATTER
                : DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String currentTime = LocalDateTime.now().format(formatter);
        dimensionRow.setField("first_seen", currentTime);
        dimensionRow.setField("last_seen", currentTime);
        dimensionRow.setField("create_time", currentTime);
        dimensionRow.setField("update_time", currentTime);

        return dimensionRow;
    }

    /**
     * 从Row中获取字符串字段值
     *
     * @param row Row对象
     * @param fieldName 字段名
     * @return 字段值，如果不存在或类型不匹配则返回null
     */
    private String getStringFieldValue(Row row, String fieldName) {
        try {
            Set<String> fieldNames = row.getFieldNames(true);
            if (fieldNames != null && fieldNames.contains(fieldName)) {
                Object fieldValue = row.getField(fieldName);
                if (fieldValue instanceof String) {
                    return (String) fieldValue;
                }
            }
        } catch (Exception e) {
            log.warn("获取字符串字段值时发生错误，字段名: {}, 错误: {}", fieldName, e.getMessage());
        }
        return null;
    }
}
