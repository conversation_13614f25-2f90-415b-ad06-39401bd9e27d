package com.geeksec.nta.datawarehouse.etl.constant;

/**
 * ODS层包统计信息字段名常量定义
 * 对应ods_session_logs表中的包统计相关字段
 * 
 * <AUTHOR>
 */
public final class OdsPacketFieldConstants {
    
    // ========== 包统计信息字段 ==========
    
    /** pkt_smaxlen字段 */
    public static final String PKT_SMAXLEN = "pkt_smaxlen";
    /** pkt_dmaxlen字段 */
    public static final String PKT_DMAXLEN = "pkt_dmaxlen";
    /** pkt_snum字段 */
    public static final String PKT_SNUM = "pkt_snum";
    /** pkt_dnum字段 */
    public static final String PKT_DNUM = "pkt_dnum";
    /** pkt_spayloadnum字段 */
    public static final String PKT_SPAYLOADNUM = "pkt_spayloadnum";
    /** pkt_dpayloadnum字段 */
    public static final String PKT_DPAYLOADNUM = "pkt_dpayloadnum";
    /** pkt_sbytes字段 */
    public static final String PKT_SBYTES = "pkt_sbytes";
    /** pkt_dbytes字段 */
    public static final String PKT_DBYTES = "pkt_dbytes";
    /** pkt_spayloadbytes字段 */
    public static final String PKT_SPAYLOADBYTES = "pkt_spayloadbytes";
    /** pkt_dpayloadbytes字段 */
    public static final String PKT_DPAYLOADBYTES = "pkt_dpayloadbytes";
    /** pkt_sfinnum字段 */
    public static final String PKT_SFINNUM = "pkt_sfinnum";
    /** pkt_dfinnum字段 */
    public static final String PKT_DFINNUM = "pkt_dfinnum";
    /** pkt_srstnum字段 */
    public static final String PKT_SRSTNUM = "pkt_srstnum";
    /** pkt_drstnum字段 */
    public static final String PKT_DRSTNUM = "pkt_drstnum";
    /** pkt_ssynnum字段 */
    public static final String PKT_SSYNNUM = "pkt_ssynnum";
    /** pkt_dsynnum字段 */
    public static final String PKT_DSYNNUM = "pkt_dsynnum";
    /** pkt_ssynbytes字段 */
    public static final String PKT_SSYNBYTES = "pkt_ssynbytes";
    /** pkt_dsynbytes字段 */
    public static final String PKT_DSYNBYTES = "pkt_dsynbytes";
    /** pkt_sttlmax字段 */
    public static final String PKT_STTLMAX = "pkt_sttlmax";
    /** pkt_dttlmax字段 */
    public static final String PKT_DTTLMAX = "pkt_dttlmax";
    /** pkt_sttlmin字段 */
    public static final String PKT_STTLMIN = "pkt_sttlmin";
    /** pkt_dttlmin字段 */
    public static final String PKT_DTTLMIN = "pkt_dttlmin";
    /** pkt_sdurmax字段 */
    public static final String PKT_SDURMAX = "pkt_sdurmax";
    /** pkt_ddurmax字段 */
    public static final String PKT_DDURMAX = "pkt_ddurmax";
    /** pkt_sdurmin字段 */
    public static final String PKT_SDURMIN = "pkt_sdurmin";
    /** pkt_ddurmin字段 */
    public static final String PKT_DDURMIN = "pkt_ddurmin";
    /** pkt_sdisorder字段 */
    public static final String PKT_SDISORDER = "pkt_sdisorder";
    /** pkt_ddisorder字段 */
    public static final String PKT_DDISORDER = "pkt_ddisorder";
    /** pkt_sresend字段 */
    public static final String PKT_SRESEND = "pkt_sresend";
    /** pkt_dresend字段 */
    public static final String PKT_DRESEND = "pkt_dresend";
    /** pkt_slost字段 */
    public static final String PKT_SLOST = "pkt_slost";
    /** pkt_dlost字段 */
    public static final String PKT_DLOST = "pkt_dlost";
    /** pkt_spshnum字段 */
    public static final String PKT_SPSHNUM = "pkt_spshnum";
    /** pkt_dpshnum字段 */
    public static final String PKT_DPSHNUM = "pkt_dpshnum";
    /** pkt_pronum字段 */
    public static final String PKT_PRONUM = "pkt_pronum";
    /** pkt_unkonw_pronum字段 */
    public static final String PKT_UNKONW_PRONUM = "pkt_unkonw_pronum";
    /** pkt_syn_data字段 */
    public static final String PKT_SYN_DATA = "pkt_syn_data";
    /** pkt_sbadnum字段 */
    public static final String PKT_SBADNUM = "pkt_sbadnum";
    /** pkt_dbadnum字段 */
    public static final String PKT_DBADNUM = "pkt_dbadnum";
    /** app_pkt_id字段 */
    public static final String APP_PKT_ID = "app_pkt_id";
    /** pkt_spayload字段 */
    public static final String PKT_SPAYLOAD = "pkt_spayload";
    /** pkt_dpayload字段 */
    public static final String PKT_DPAYLOAD = "pkt_dpayload";
    /** pkt_infor字段 */
    public static final String PKT_INFOR = "pkt_infor";
    
    /**
     * 防止实例化
     */
    private OdsPacketFieldConstants() {
        throw new IllegalStateException("Utility class");
    }
}
