package com.geeksec.nta.datawarehouse.etl.constant;

/**
 * ODS层协议指纹字段名常量定义
 * 对应ods_session_logs表中的协议指纹相关字段
 * 
 * <AUTHOR>
 */
public final class OdsFingerprintFieldConstants {
    
    // ========== 协议指纹字段 ==========
    
    /** tcp_c_finger字段 */
    public static final String TCP_C_FINGER = "tcp_c_finger";
    /** tcp_s_finger字段 */
    public static final String TCP_S_FINGER = "tcp_s_finger";
    /** http_c_finger字段 */
    public static final String HTTP_C_FINGER = "http_c_finger";
    /** http_s_finger字段 */
    public static final String HTTP_S_FINGER = "http_s_finger";
    /** ssl_c_finger字段 */
    public static final String SSL_C_FINGER = "ssl_c_finger";
    /** ssl_s_finger字段 */
    public static final String SSL_S_FINGER = "ssl_s_finger";
    
    // ========== TCP客户端指纹特征字段 ==========
    
    /** tcp_c_feature_ecn_ip_ect字段 */
    public static final String TCP_C_FEATURE_ECN_IP_ECT = "tcp_c_feature_ecn_ip_ect";
    /** tcp_c_feature_qk_dfnz_ipid字段 */
    public static final String TCP_C_FEATURE_QK_DFNZ_IPID = "tcp_c_feature_qk_dfnz_ipid";
    /** tcp_c_feature_flag_cwr字段 */
    public static final String TCP_C_FEATURE_FLAG_CWR = "tcp_c_feature_flag_cwr";
    /** tcp_c_feature_flag_ece字段 */
    public static final String TCP_C_FEATURE_FLAG_ECE = "tcp_c_feature_flag_ece";
    /** tcp_c_feature_qk_opt_zero_ts1字段 */
    public static final String TCP_C_FEATURE_QK_OPT_ZERO_TS1 = "tcp_c_feature_qk_opt_zero_ts1";
    /** tcp_c_feature_ttl字段 */
    public static final String TCP_C_FEATURE_TTL = "tcp_c_feature_ttl";
    /** tcp_c_feature_tcpopt_eol_padnum字段 */
    public static final String TCP_C_FEATURE_TCPOPT_EOL_PADNUM = "tcp_c_feature_tcpopt_eol_padnum";
    /** tcp_c_feature_tcpopt_wscale字段 */
    public static final String TCP_C_FEATURE_TCPOPT_WSCALE = "tcp_c_feature_tcpopt_wscale";
    /** tcp_c_feature_qk_win_mss字段 */
    public static final String TCP_C_FEATURE_QK_WIN_MSS = "tcp_c_feature_qk_win_mss";
    /** tcp_c_feature_tcpopt_layout字段 */
    public static final String TCP_C_FEATURE_TCPOPT_LAYOUT = "tcp_c_feature_tcpopt_layout";
    
    // ========== TCP服务端指纹特征字段 ==========
    
    /** tcp_s_feature_ecn_ip_ect字段 */
    public static final String TCP_S_FEATURE_ECN_IP_ECT = "tcp_s_feature_ecn_ip_ect";
    /** tcp_s_feature_qk_dfnz_ipid字段 */
    public static final String TCP_S_FEATURE_QK_DFNZ_IPID = "tcp_s_feature_qk_dfnz_ipid";
    /** tcp_s_feature_flag_cwr字段 */
    public static final String TCP_S_FEATURE_FLAG_CWR = "tcp_s_feature_flag_cwr";
    /** tcp_s_feature_flag_ece字段 */
    public static final String TCP_S_FEATURE_FLAG_ECE = "tcp_s_feature_flag_ece";
    /** tcp_s_feature_qk_opt_zero_ts1字段 */
    public static final String TCP_S_FEATURE_QK_OPT_ZERO_TS1 = "tcp_s_feature_qk_opt_zero_ts1";
    /** tcp_s_feature_ttl字段 */
    public static final String TCP_S_FEATURE_TTL = "tcp_s_feature_ttl";
    /** tcp_s_feature_tcpopt_eol_padnum字段 */
    public static final String TCP_S_FEATURE_TCPOPT_EOL_PADNUM = "tcp_s_feature_tcpopt_eol_padnum";
    /** tcp_s_feature_tcpopt_wscale字段 */
    public static final String TCP_S_FEATURE_TCPOPT_WSCALE = "tcp_s_feature_tcpopt_wscale";
    /** tcp_s_feature_qk_win_mss字段 */
    public static final String TCP_S_FEATURE_QK_WIN_MSS = "tcp_s_feature_qk_win_mss";
    /** tcp_s_feature_tcpopt_layout字段 */
    public static final String TCP_S_FEATURE_TCPOPT_LAYOUT = "tcp_s_feature_tcpopt_layout";
    
    /**
     * 防止实例化
     */
    private OdsFingerprintFieldConstants() {
        throw new IllegalStateException("Utility class");
    }
}
