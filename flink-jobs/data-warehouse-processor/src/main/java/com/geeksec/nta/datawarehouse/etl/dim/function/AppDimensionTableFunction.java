package com.geeksec.nta.datawarehouse.etl.dim.function;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import com.geeksec.common.utils.time.DateTimeUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 应用维度表处理函数，用于生成符合dim_app表结构的维度数据
 *
 * 使用Flink Backend State进行缓存管理，支持TTL自动清理
 *
 * <AUTHOR>
 */
@Slf4j
public class AppDimensionTableFunction extends ProcessFunction<Row, Row> {

    private static final long serialVersionUID = 1L;

    /**
     * 应用维度数据输出标签
     */
    public static final OutputTag<Row> APP_DIM_TAG = new OutputTag<Row>("app-dimension") {};

    private final String appNameFieldName;
    private final Duration ttl;

    /**
     * 缓存应用维度数据的状态
     */
    private transient ValueState<Map<String, Object>> appDimensionState;

    /**
     * 构造函数
     *
     * @param appNameFieldName 应用名称字段名
     */
    public AppDimensionTableFunction(String appNameFieldName) {
        // 默认24小时TTL
        this(appNameFieldName, Duration.ofHours(24));
    }

    /**
     * 构造函数
     *
     * @param appNameFieldName 应用名称字段名
     * @param ttl 状态TTL时间
     */
    public AppDimensionTableFunction(String appNameFieldName, Duration ttl) {
        this.appNameFieldName = appNameFieldName;
        this.ttl = ttl;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 配置状态TTL
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(ttl)
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();

        // 初始化应用维度状态
        ValueStateDescriptor<Map<String, Object>> appStateDescriptor =
                new ValueStateDescriptor<>("app-dimension-state", Types.MAP(Types.STRING, Types.GENERIC(Object.class)));
        appStateDescriptor.enableTimeToLive(ttlConfig);
        appDimensionState = getRuntimeContext().getState(appStateDescriptor);
    }

    @Override
    public void processElement(Row value, Context ctx, Collector<Row> out) throws Exception {
        try {
            String appName = getStringFieldValue(value, appNameFieldName);
            if (appName == null || appName.trim().isEmpty()) {
                out.collect(value);
                return;
            }

            // 检查状态中是否已存在该应用的维度数据
            Map<String, Object> existingAppInfo = appDimensionState.value();
            if (existingAppInfo == null) {
                // 首次遇到该应用，创建维度数据
                Map<String, Object> appInfo = createAppDimensionInfo(value, appName);
                appDimensionState.update(appInfo);

                // 创建维度表记录并输出到侧输出流
                Row appDimensionRow = createDimensionRow(appName, appInfo);
                ctx.output(APP_DIM_TAG, appDimensionRow);

                log.debug("创建新的应用维度数据: {}", appName);
            }

            // 继续传递原始数据
            out.collect(value);

        } catch (Exception e) {
            log.error("处理应用维度数据时发生错误: {}", e.getMessage(), e);
            out.collect(value);
        }
    }

    /**
     * 创建应用维度信息
     *
     * @param value 原始数据Row
     * @param appName 应用名称
     * @return 应用维度信息Map
     */
    private Map<String, Object> createAppDimensionInfo(Row value, String appName) {
        Map<String, Object> appInfo = new HashMap<>(8);

        // 基础信息
        appInfo.put("app_name", appName);

        // 获取应用分类信息
        String appCategoryName = getStringFieldValue(value, "app_category_name");
        appInfo.put("app_category_name", appCategoryName != null ? appCategoryName : "Unknown");

        String appSubCategoryName = getStringFieldValue(value, "app_sub_category_name");
        appInfo.put("app_sub_category_name", appSubCategoryName != null ? appSubCategoryName : "Unknown");

        String appBehaviorName = getStringFieldValue(value, "app_behavior_name");
        appInfo.put("app_behavior_name", appBehaviorName != null ? appBehaviorName : "Unknown");

        return appInfo;
    }

    /**
     * 创建符合维度表结构的应用维度记录
     *
     * @param appName 应用名称
     * @param appInfo 应用信息
     * @return 符合dim_app表结构的Row
     */
    private Row createDimensionRow(String appName, Map<String, Object> appInfo) {
        Row dimensionRow = Row.withNames();

        // 设置主键字段
        dimensionRow.setField("app_name", appName);

        // 设置应用分类信息字段
        dimensionRow.setField("app_category_name", appInfo.get("app_category_name"));
        dimensionRow.setField("app_sub_category_name", appInfo.get("app_sub_category_name"));
        dimensionRow.setField("app_behavior_name", appInfo.get("app_behavior_name"));

        // 设置聚合字段初始值
        dimensionRow.setField("total_bytes", 0L);
        dimensionRow.setField("total_packets", 0L);
        dimensionRow.setField("total_bytes_sent", 0L);
        dimensionRow.setField("total_bytes_received", 0L);
        dimensionRow.setField("total_packets_sent", 0L);
        dimensionRow.setField("total_packets_received", 0L);
        dimensionRow.setField("session_count", 1);
        dimensionRow.setField("total_duration", 0L);
        dimensionRow.setField("avg_bps", 0.0);

        // 设置时间字段
        DateTimeFormatter formatter = DateTimeUtils.DEFAULT_DATE_TIME_FORMATTER != null
                ? DateTimeUtils.DEFAULT_DATE_TIME_FORMATTER
                : DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String currentTime = LocalDateTime.now().format(formatter);
        dimensionRow.setField("first_seen", currentTime);
        dimensionRow.setField("last_seen", currentTime);
        dimensionRow.setField("create_time", currentTime);
        dimensionRow.setField("update_time", currentTime);

        return dimensionRow;
    }

    /**
     * 从Row中获取字符串字段值
     *
     * @param row Row对象
     * @param fieldName 字段名
     * @return 字段值，如果不存在或类型不匹配则返回null
     */
    private String getStringFieldValue(Row row, String fieldName) {
        try {
            Set<String> fieldNames = row.getFieldNames(true);
            if (fieldNames != null && fieldNames.contains(fieldName)) {
                Object fieldValue = row.getField(fieldName);
                if (fieldValue instanceof String) {
                    return (String) fieldValue;
                }
            }
        } catch (Exception e) {
            log.warn("获取字符串字段值时发生错误，字段名: {}, 错误: {}", fieldName, e.getMessage());
        }
        return null;
    }
}
