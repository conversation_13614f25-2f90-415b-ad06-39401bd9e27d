package com.geeksec.nta.datawarehouse.etl.dwd.converter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.geeksec.nta.datawarehouse.common.MessageType;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;

import java.io.Serializable;

/**
 * 协议JSON转换器
 * 负责将各协议的Row数据转换为JSON格式，用于存储在DWD层的JSON数组字段中
 *
 * <AUTHOR>
 */
@Slf4j
public class ProtocolJsonConverter implements Serializable {

    private static final long serialVersionUID = 1L;

    /** JSON对象映射器 */
    private transient ObjectMapper objectMapper;

    /**
     * 构造函数
     */
    public ProtocolJsonConverter() {
        initObjectMapper();
    }

    /**
     * 初始化ObjectMapper
     */
    private void initObjectMapper() {
        if (objectMapper == null) {
            objectMapper = new ObjectMapper();
        }
    }

    /**
     * 将Row数据转换为JSON字符串
     *
     * @param row 协议数据行
     * @param protocolType 协议类型
     * @return JSON字符串
     */
    public String convertToJson(Row row, MessageType protocolType) {
        if (row == null || protocolType == null) {
            return null;
        }

        initObjectMapper();

        try {
            ObjectNode jsonNode = objectMapper.createObjectNode();

            // 根据协议类型转换对应字段
            switch (protocolType) {
                case HTTP -> convertHttpFields(row, jsonNode);
                case SSL -> convertSslFields(row, jsonNode);
                case DNS -> convertDnsFields(row, jsonNode);
                case SSH -> convertSshFields(row, jsonNode);
                case VNC -> convertVncFields(row, jsonNode);
                case TELNET -> convertTelnetFields(row, jsonNode);
                case RLOGIN -> convertRloginFields(row, jsonNode);
                case RDP -> convertRdpFields(row, jsonNode);
                case ICMP -> convertIcmpFields(row, jsonNode);
                case NTP -> convertNtpFields(row, jsonNode);
                case XDMCP -> convertXdmcpFields(row, jsonNode);
                case S7 -> convertS7Fields(row, jsonNode);
                case MODBUS -> convertModbusFields(row, jsonNode);
                case IEC104 -> convertIec104Fields(row, jsonNode);
                case EIP -> convertEipFields(row, jsonNode);
                case OPC -> convertOpcFields(row, jsonNode);
                case ESP -> convertEspFields(row, jsonNode);
                case L2TP -> convertL2tpFields(row, jsonNode);
                default -> {
                    log.warn("不支持的协议类型: {}", protocolType);
                    return null;
                }
            }

            return objectMapper.writeValueAsString(jsonNode);

        } catch (Exception e) {
            log.error("转换协议数据为JSON失败: protocolType={}, error={}", protocolType, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 转换HTTP协议字段
     * 注意：移除了通用字段（session_id、src_ip、dst_ip等），避免与DWD层会话表字段重复
     */
    private void convertHttpFields(Row row, ObjectNode jsonNode) {
        // 只转换HTTP协议特定字段，不包含通用会话字段
        addFieldIfExists(jsonNode, "method", row, "http_method");
        addFieldIfExists(jsonNode, "url", row, "http_url");
        addFieldIfExists(jsonNode, "host", row, "http_host");
        addFieldIfExists(jsonNode, "status_code", row, "http_status_code");
        addFieldIfExists(jsonNode, "user_agent", row, "http_user_agent");
        addFieldIfExists(jsonNode, "request_headers", row, "http_request_headers");
        addFieldIfExists(jsonNode, "response_headers", row, "http_response_headers");
        addFieldIfExists(jsonNode, "request_size", row, "http_request_size");
        addFieldIfExists(jsonNode, "response_size", row, "http_response_size");
    }

    /**
     * 转换SSL协议字段
     * 注意：移除了通用字段，只保留SSL协议特定字段
     */
    private void convertSslFields(Row row, ObjectNode jsonNode) {
        // 只转换SSL协议特定字段，不包含通用会话字段
        addFieldIfExists(jsonNode, "version", row, "ssl_version");
        addFieldIfExists(jsonNode, "cipher_suite", row, "ssl_cipher_suite");
        addFieldIfExists(jsonNode, "server_name", row, "ssl_server_name");
        addFieldIfExists(jsonNode, "cert_subject", row, "ssl_cert_subject");
        addFieldIfExists(jsonNode, "cert_issuer", row, "ssl_cert_issuer");
        addFieldIfExists(jsonNode, "cert_serial", row, "ssl_cert_serial");
        addFieldIfExists(jsonNode, "cert_not_before", row, "ssl_cert_not_before");
        addFieldIfExists(jsonNode, "cert_not_after", row, "ssl_cert_not_after");
        addFieldIfExists(jsonNode, "cert_sha1", row, "ssl_cert_sha1");
        addFieldIfExists(jsonNode, "cert_sha256", row, "ssl_cert_sha256");
        addFieldIfExists(jsonNode, "ja3", row, "ssl_ja3");
        addFieldIfExists(jsonNode, "ja3s", row, "ssl_ja3s");
        addFieldIfExists(jsonNode, "is_self_signed", row, "ssl_is_self_signed");
    }

    /**
     * 转换DNS协议字段
     * 注意：移除了通用字段，只保留DNS协议特定字段
     */
    private void convertDnsFields(Row row, ObjectNode jsonNode) {
        // 只转换DNS协议特定字段，不包含通用会话字段
        addFieldIfExists(jsonNode, "transaction_id", row, "dns_transaction_id");
        addFieldIfExists(jsonNode, "query_name", row, "dns_query_name");
        addFieldIfExists(jsonNode, "query_type", row, "dns_query_type");
        addFieldIfExists(jsonNode, "query_class", row, "dns_query_class");
        addFieldIfExists(jsonNode, "response_code", row, "dns_response_code");
        addFieldIfExists(jsonNode, "answer_count", row, "dns_answer_count");
        addFieldIfExists(jsonNode, "authority_count", row, "dns_authority_count");
        addFieldIfExists(jsonNode, "additional_count", row, "dns_additional_count");
        addFieldIfExists(jsonNode, "resolved_addresses", row, "dns_resolved_addresses");
        addFieldIfExists(jsonNode, "ttl", row, "dns_ttl");
        addFieldIfExists(jsonNode, "is_recursive", row, "dns_is_recursive");
    }

    /**
     * 转换SSH协议字段
     * 注意：移除了通用字段，只保留SSH协议特定字段
     */
    private void convertSshFields(Row row, ObjectNode jsonNode) {
        // 只转换SSH协议特定字段，不包含通用会话字段
        addFieldIfExists(jsonNode, "protocol_version", row, "ssh_protocol_version");
        addFieldIfExists(jsonNode, "client_software", row, "ssh_client_software");
        addFieldIfExists(jsonNode, "server_software", row, "ssh_server_software");
        addFieldIfExists(jsonNode, "kex_algorithm", row, "ssh_kex_algorithm");
        addFieldIfExists(jsonNode, "host_key_algorithm", row, "ssh_host_key_algorithm");
        addFieldIfExists(jsonNode, "encryption_algorithm", row, "ssh_encryption_algorithm");
        addFieldIfExists(jsonNode, "mac_algorithm", row, "ssh_mac_algorithm");
        addFieldIfExists(jsonNode, "compression_algorithm", row, "ssh_compression_algorithm");
        addFieldIfExists(jsonNode, "hassh_client", row, "ssh_hassh_client");
        addFieldIfExists(jsonNode, "hassh_server", row, "ssh_hassh_server");
        addFieldIfExists(jsonNode, "auth_methods", row, "ssh_auth_methods");
        addFieldIfExists(jsonNode, "auth_success", row, "ssh_auth_success");
    }

    /**
     * 转换VNC协议字段
     * 注意：移除了通用字段，只保留VNC协议特定字段
     */
    private void convertVncFields(Row row, ObjectNode jsonNode) {
        // 只转换VNC协议特定字段，不包含通用会话字段
        addFieldIfExists(jsonNode, "fb_width", row, "vnc_fb_width");
        addFieldIfExists(jsonNode, "fb_height", row, "vnc_fb_height");
        addFieldIfExists(jsonNode, "desktop_name", row, "vnc_desktop_name");
        addFieldIfExists(jsonNode, "server_protocol_ver", row, "vnc_server_protocol_ver");
        addFieldIfExists(jsonNode, "client_protocol_ver", row, "vnc_client_protocol_ver");
        addFieldIfExists(jsonNode, "auth_result", row, "vnc_auth_result");
        addFieldIfExists(jsonNode, "encoding_types", row, "vnc_encoding_types");
        addFieldIfExists(jsonNode, "bits_per_pixel", row, "vnc_bits_per_pixel");
        addFieldIfExists(jsonNode, "depth", row, "vnc_depth");
        addFieldIfExists(jsonNode, "big_endian", row, "vnc_big_endian");
        addFieldIfExists(jsonNode, "true_color", row, "vnc_true_color");
    }

    /**
     * 添加字段到JSON节点（如果存在）
     */
    private void addFieldIfExists(ObjectNode jsonNode, String jsonFieldName, Row row, String rowFieldName) {
        try {
            Object value = row.getField(rowFieldName);
            if (value != null) {
                if (value instanceof String) {
                    jsonNode.put(jsonFieldName, (String) value);
                } else if (value instanceof Integer) {
                    jsonNode.put(jsonFieldName, (Integer) value);
                } else if (value instanceof Long) {
                    jsonNode.put(jsonFieldName, (Long) value);
                } else if (value instanceof Double) {
                    jsonNode.put(jsonFieldName, (Double) value);
                } else if (value instanceof Boolean) {
                    jsonNode.put(jsonFieldName, (Boolean) value);
                } else {
                    jsonNode.put(jsonFieldName, value.toString());
                }
            }
        } catch (Exception e) {
            // 字段不存在或获取失败，忽略
        }
    }

    /**
     * 转换TELNET协议字段
     * 注意：移除了通用字段，只保留TELNET协议特定字段
     */
    private void convertTelnetFields(Row row, ObjectNode jsonNode) {
        // 只转换TELNET协议特定字段，不包含通用会话字段
        addFieldIfExists(jsonNode, "term_width", row, "telnet_term_width");
        addFieldIfExists(jsonNode, "term_height", row, "telnet_term_height");
        addFieldIfExists(jsonNode, "term_type", row, "telnet_term_type");
        addFieldIfExists(jsonNode, "username", row, "telnet_username");
        addFieldIfExists(jsonNode, "options_negotiated", row, "telnet_options_negotiated");
    }

    /**
     * 转换RLOGIN协议字段
     * 注意：移除了通用字段，只保留RLOGIN协议特定字段
     */
    private void convertRloginFields(Row row, ObjectNode jsonNode) {
        // 只转换RLOGIN协议特定字段，不包含通用会话字段
        addFieldIfExists(jsonNode, "rows", row, "rlogin_rows");
        addFieldIfExists(jsonNode, "columns", row, "rlogin_columns");
        addFieldIfExists(jsonNode, "term_type", row, "rlogin_term_type");
        addFieldIfExists(jsonNode, "client_username", row, "rlogin_client_username");
        addFieldIfExists(jsonNode, "server_username", row, "rlogin_server_username");
        addFieldIfExists(jsonNode, "terminal_speed", row, "rlogin_terminal_speed");
    }

    /**
     * 转换RDP协议字段
     * 注意：移除了通用字段，只保留RDP协议特定字段
     */
    private void convertRdpFields(Row row, ObjectNode jsonNode) {
        // 只转换RDP协议特定字段，不包含通用会话字段
        addFieldIfExists(jsonNode, "desktop_width", row, "rdp_desktop_width");
        addFieldIfExists(jsonNode, "desktop_height", row, "rdp_desktop_height");
        addFieldIfExists(jsonNode, "client_name", row, "rdp_client_name");
        addFieldIfExists(jsonNode, "encryption_method", row, "rdp_encryption_method");
        addFieldIfExists(jsonNode, "color_depth", row, "rdp_color_depth");
        addFieldIfExists(jsonNode, "keyboard_layout", row, "rdp_keyboard_layout");
        addFieldIfExists(jsonNode, "client_build", row, "rdp_client_build");
    }

    /**
     * 转换ICMP协议字段
     * 注意：移除了通用字段，只保留ICMP协议特定字段
     */
    private void convertIcmpFields(Row row, ObjectNode jsonNode) {
        // 只转换ICMP协议特定字段，不包含通用会话字段
        addFieldIfExists(jsonNode, "msg_type", row, "icmp_msg_type");
        addFieldIfExists(jsonNode, "info_code", row, "icmp_info_code");
        addFieldIfExists(jsonNode, "echo_seq_num", row, "icmp_echo_seq_num");
        addFieldIfExists(jsonNode, "response_time", row, "icmp_response_time");
        addFieldIfExists(jsonNode, "payload_size", row, "icmp_payload_size");
        addFieldIfExists(jsonNode, "identifier", row, "icmp_identifier");
    }

    /**
     * 转换NTP协议字段
     * 注意：移除了通用字段，只保留NTP协议特定字段
     */
    private void convertNtpFields(Row row, ObjectNode jsonNode) {
        // 只转换NTP协议特定字段，不包含通用会话字段
        addFieldIfExists(jsonNode, "version", row, "ntp_version");
        addFieldIfExists(jsonNode, "client_stratum", row, "ntp_client_stratum");
        addFieldIfExists(jsonNode, "server_stratum", row, "ntp_server_stratum");
        addFieldIfExists(jsonNode, "client_reference_id", row, "ntp_client_reference_id");
        addFieldIfExists(jsonNode, "server_reference_id", row, "ntp_server_reference_id");
        addFieldIfExists(jsonNode, "precision", row, "ntp_precision");
        addFieldIfExists(jsonNode, "root_delay", row, "ntp_root_delay");
        addFieldIfExists(jsonNode, "root_dispersion", row, "ntp_root_dispersion");
    }

    /**
     * 转换XDMCP协议字段
     * 注意：移除了通用字段，只保留XDMCP协议特定字段
     */
    private void convertXdmcpFields(Row row, ObjectNode jsonNode) {
        // 只转换XDMCP协议特定字段，不包含通用会话字段
        addFieldIfExists(jsonNode, "version", row, "xdmcp_version");
        addFieldIfExists(jsonNode, "display_number", row, "xdmcp_display_number");
        addFieldIfExists(jsonNode, "session_id", row, "xdmcp_session_id");
        addFieldIfExists(jsonNode, "hostname", row, "xdmcp_hostname");
        addFieldIfExists(jsonNode, "status", row, "xdmcp_status");
        addFieldIfExists(jsonNode, "authentication_name", row, "xdmcp_authentication_name");
    }

    /**
     * 转换S7协议字段
     * 注意：移除了通用字段，只保留S7协议特定字段
     */
    private void convertS7Fields(Row row, ObjectNode jsonNode) {
        // 只转换S7协议特定字段，不包含通用会话字段
        addFieldIfExists(jsonNode, "tpkt_version", row, "s7_tpkt_version");
        addFieldIfExists(jsonNode, "cotp_type", row, "s7_cotp_type");
        addFieldIfExists(jsonNode, "s7_type", row, "s7_type");
        addFieldIfExists(jsonNode, "s7_function", row, "s7_function");
        addFieldIfExists(jsonNode, "system_type", row, "s7_system_type");
        addFieldIfExists(jsonNode, "system_group_function", row, "s7_system_group_function");
        addFieldIfExists(jsonNode, "system_sub_function", row, "s7_system_sub_function");
        addFieldIfExists(jsonNode, "dst_ref", row, "s7_dst_ref");
        addFieldIfExists(jsonNode, "src_ref", row, "s7_src_ref");
        addFieldIfExists(jsonNode, "pdu_size", row, "s7_pdu_size");
        addFieldIfExists(jsonNode, "src_connect_type", row, "s7_src_connect_type");
        addFieldIfExists(jsonNode, "src_rack", row, "s7_src_rack");
        addFieldIfExists(jsonNode, "src_slot", row, "s7_src_slot");
        addFieldIfExists(jsonNode, "dst_connect_type", row, "s7_dst_connect_type");
        addFieldIfExists(jsonNode, "dst_rack", row, "s7_dst_rack");
        addFieldIfExists(jsonNode, "dst_slot", row, "s7_dst_slot");
        addFieldIfExists(jsonNode, "packet_c2s", row, "s7_packet_c2s");
    }

    /**
     * 转换Modbus协议字段
     * 注意：移除了通用字段，只保留Modbus协议特定字段
     */
    private void convertModbusFields(Row row, ObjectNode jsonNode) {
        // 只转换Modbus协议特定字段，不包含通用会话字段
        addFieldIfExists(jsonNode, "trans_id", row, "modbus_trans_id");
        addFieldIfExists(jsonNode, "protocol_id", row, "modbus_protocol_id");
        addFieldIfExists(jsonNode, "slave_id", row, "modbus_slave_id");
        addFieldIfExists(jsonNode, "func_code", row, "modbus_func_code");
        addFieldIfExists(jsonNode, "packet_c2s", row, "modbus_packet_c2s");
    }

    /**
     * 转换IEC104协议字段
     * 注意：移除了通用字段，只保留IEC104协议特定字段
     */
    private void convertIec104Fields(Row row, ObjectNode jsonNode) {
        // 只转换IEC104协议特定字段，不包含通用会话字段
        addFieldIfExists(jsonNode, "trans_id", row, "iec104_trans_id");
        addFieldIfExists(jsonNode, "protocol_id", row, "iec104_protocol_id");
        addFieldIfExists(jsonNode, "slave_id", row, "iec104_slave_id");
        addFieldIfExists(jsonNode, "func_code", row, "iec104_func_code");
    }

    /**
     * 转换EIP协议字段
     * 注意：移除了通用字段，只保留EIP协议特定字段
     */
    private void convertEipFields(Row row, ObjectNode jsonNode) {
        // 只转换EIP协议特定字段，不包含通用会话字段
        addFieldIfExists(jsonNode, "trans_id", row, "eip_trans_id");
        addFieldIfExists(jsonNode, "protocol_id", row, "eip_protocol_id");
        addFieldIfExists(jsonNode, "slave_id", row, "eip_slave_id");
        addFieldIfExists(jsonNode, "func_code", row, "eip_func_code");
    }

    /**
     * 转换OPC协议字段
     * 注意：移除了通用字段，只保留OPC协议特定字段
     */
    private void convertOpcFields(Row row, ObjectNode jsonNode) {
        // 只转换OPC协议特定字段，不包含通用会话字段
        addFieldIfExists(jsonNode, "trans_id", row, "opc_trans_id");
        addFieldIfExists(jsonNode, "protocol_id", row, "opc_protocol_id");
        addFieldIfExists(jsonNode, "slave_id", row, "opc_slave_id");
        addFieldIfExists(jsonNode, "func_code", row, "opc_func_code");
    }

    /**
     * 转换ESP协议字段
     * 注意：移除了通用字段，只保留ESP协议特定字段
     */
    private void convertEspFields(Row row, ObjectNode jsonNode) {
        // 只转换ESP协议特定字段，不包含通用会话字段
        addFieldIfExists(jsonNode, "protocol_family", row, "esp_protocol_family");
        addFieldIfExists(jsonNode, "communication_rate", row, "esp_communication_rate");
        addFieldIfExists(jsonNode, "direction", row, "esp_direction");
        addFieldIfExists(jsonNode, "encapsulation_mode", row, "esp_encapsulation_mode");
        addFieldIfExists(jsonNode, "esp_spi", row, "esp_spi");
        addFieldIfExists(jsonNode, "esp_seq", row, "esp_seq");
        addFieldIfExists(jsonNode, "esp_data_len", row, "esp_data_len");
        addFieldIfExists(jsonNode, "esp_data", row, "esp_data");
    }

    /**
     * 转换L2TP协议字段
     * 注意：移除了通用字段，只保留L2TP协议特定字段
     */
    private void convertL2tpFields(Row row, ObjectNode jsonNode) {
        // 只转换L2TP协议特定字段，不包含通用会话字段
        addFieldIfExists(jsonNode, "protocol_family", row, "l2tp_protocol_family");
        addFieldIfExists(jsonNode, "communication_rate", row, "l2tp_communication_rate");
        addFieldIfExists(jsonNode, "direction", row, "l2tp_direction");
        addFieldIfExists(jsonNode, "protocol_version", row, "l2tp_protocol_version");
        addFieldIfExists(jsonNode, "framing_capabilities", row, "l2tp_framing_capabilities");
        addFieldIfExists(jsonNode, "bearer_capabilities", row, "l2tp_bearer_capabilities");
        addFieldIfExists(jsonNode, "server_hostname", row, "l2tp_server_hostname");
        addFieldIfExists(jsonNode, "client_hostname", row, "l2tp_client_hostname");
        addFieldIfExists(jsonNode, "server_vendorname", row, "l2tp_server_vendorname");
        addFieldIfExists(jsonNode, "client_vendorname", row, "l2tp_client_vendorname");
        addFieldIfExists(jsonNode, "calling_number", row, "l2tp_calling_number");
        addFieldIfExists(jsonNode, "proxy_authen_type", row, "l2tp_proxy_authen_type");
        addFieldIfExists(jsonNode, "proxy_authen_name", row, "l2tp_proxy_authen_name");
        addFieldIfExists(jsonNode, "is_negotiate_success", row, "l2tp_is_negotiate_success");
    }
}
