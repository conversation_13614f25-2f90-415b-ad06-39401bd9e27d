package com.geeksec.nta.datawarehouse.etl.dim.function;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.geeksec.common.utils.net.GeoIpUtils;
import com.geeksec.common.utils.net.IpUtils;
import com.maxmind.geoip2.model.AsnResponse;
import com.maxmind.geoip2.model.CityResponse;

/**
 * IP维度数据丰富化处理函数
 * 使用Flink Backend State管理IP地理信息缓存，提供容错性和可扩展性
 *
 * <AUTHOR>
 */
public class IpDimensionEnrichmentFunction extends ProcessFunction<Row, Row> {

    private static final Logger log = LoggerFactory.getLogger(IpDimensionEnrichmentFunction.class);

    /** IP信息缓存状态，使用MapState管理 */
    private transient MapState<String, Map<String, Object>> ipInfoCache;

    /** 缓存TTL配置，默认24小时 */
    private final Duration cacheTtl;

    /** IP字段名 */
    private final String ipFieldName;

    /**
     * 构造函数
     *
     * @param ipFieldName IP字段名
     */
    public IpDimensionEnrichmentFunction(String ipFieldName) {
        this(ipFieldName, Duration.ofHours(24));
    }

    /**
     * 构造函数
     *
     * @param ipFieldName IP字段名
     * @param cacheTtl 缓存TTL时间
     */
    public IpDimensionEnrichmentFunction(String ipFieldName, Duration cacheTtl) {
        this.ipFieldName = ipFieldName;
        this.cacheTtl = cacheTtl;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 配置状态TTL
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(cacheTtl)
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();

        // 创建MapState描述符
        MapStateDescriptor<String, Map<String, Object>> descriptor = new MapStateDescriptor<>(
                "ip-enrichment-cache",
                TypeInformation.of(new TypeHint<String>() {}),
                TypeInformation.of(new TypeHint<Map<String, Object>>() {})
        );

        // 启用TTL
        descriptor.enableTimeToLive(ttlConfig);

        // 获取状态
        ipInfoCache = getRuntimeContext().getMapState(descriptor);
    }

    @Override
    public void processElement(Row value, Context ctx, Collector<Row> out) throws Exception {
        // 获取IP地址
        String ipAddress = getIpFromRow(value);

        if (StringUtils.isEmpty(ipAddress) || !IpUtils.isValidIp(ipAddress)) {
            // IP无效，直接输出原始数据
            out.collect(value);
            return;
        }

        // 获取丰富化的IP信息
        Map<String, Object> enrichedInfo = getEnrichedIpInfo(ipAddress);

        // 将丰富化信息添加到Row中
        Row enrichedRow = addEnrichmentToRow(value, enrichedInfo);

        // 输出丰富化后的数据
        out.collect(enrichedRow);
    }

    /**
     * 从Row中获取IP地址
     *
     * @param row 输入Row
     * @return IP地址字符串
     */
    private String getIpFromRow(Row row) {
        try {
            Object ipValue = row.getField(ipFieldName);
            return ipValue != null ? ipValue.toString() : null;
        } catch (Exception e) {
            log.warn("无法从Row中获取IP字段 {}: {}", ipFieldName, e.getMessage());
            return null;
        }
    }

    /**
     * 获取丰富化的IP信息，优先从缓存获取
     *
     * @param ipAddress IP地址
     * @return 丰富化的IP信息
     * @throws Exception 状态访问异常
     */
    private Map<String, Object> getEnrichedIpInfo(String ipAddress) throws Exception {
        // 检查缓存
        Map<String, Object> cachedInfo = ipInfoCache.get(ipAddress);
        if (cachedInfo != null) {
            return cachedInfo;
        }

        // 缓存未命中，重新查询并缓存
        Map<String, Object> enrichedInfo = enrichIpInfo(ipAddress);
        ipInfoCache.put(ipAddress, enrichedInfo);

        return enrichedInfo;
    }

    /**
     * 丰富IP信息
     * 为IP地址添加地理位置信息、ASN信息等
     *
     * @param ipAddress IP地址
     * @return 丰富后的IP信息Map
     */
    private Map<String, Object> enrichIpInfo(String ipAddress) {
        Map<String, Object> ipInfo = new HashMap<>(16);
        ipInfo.put("ip", ipAddress);
        ipInfo.put("is_valid", true);

        // 检查是否为内网IP
        boolean isInternal = IpUtils.isInternalIp(ipAddress);
        ipInfo.put("is_internal", isInternal);

        if (isInternal) {
            ipInfo.put("country", "内网");
            ipInfo.put("province", "内网");
            ipInfo.put("city", "内网");
            return ipInfo;
        }

        // 添加地理位置信息
        enrichGeoInfo(ipAddress, ipInfo);
        return ipInfo;
    }

    /**
     * 丰富IP地理位置信息
     *
     * @param ipAddress IP地址
     * @param ipInfo IP信息Map
     */
    private void enrichGeoInfo(String ipAddress, Map<String, Object> ipInfo) {
        try {
            // 获取城市信息
            CityResponse cityResponse = GeoIpUtils.getAddrInfo(ipAddress);
            if (cityResponse != null) {
                addCityInfo(cityResponse, ipInfo);
            }

            // 获取ASN信息
            AsnResponse asnResponse = GeoIpUtils.getAsnInfo(ipAddress);
            if (asnResponse != null) {
                addAsnInfo(asnResponse, ipInfo);
            }
        } catch (Exception e) {
            log.warn("获取IP地理信息失败: {}", ipAddress, e);
        }
    }

    /**
     * 添加城市信息到IP信息Map
     *
     * @param cityResponse 城市响应
     * @param ipInfo IP信息Map
     */
    private void addCityInfo(CityResponse cityResponse, Map<String, Object> ipInfo) {
        // 添加国家信息
        String country = GeoIpUtils.getCountry(cityResponse);
        if (!StringUtils.isEmpty(country)) {
            ipInfo.put("country", country);
        }

        // 添加国家代码
        String countryCode = cityResponse.getCountry().getIsoCode();
        if (!StringUtils.isEmpty(countryCode)) {
            ipInfo.put("country_code", countryCode);
        }

        // 添加省份信息
        String province = GeoIpUtils.getProvince(cityResponse);
        if (!StringUtils.isEmpty(province)) {
            ipInfo.put("province", province);
        }

        // 添加城市信息
        String city = GeoIpUtils.getCity(cityResponse);
        if (!StringUtils.isEmpty(city)) {
            ipInfo.put("city", city);
        }

        // 添加经纬度
        Double latitude = GeoIpUtils.getLatitude(cityResponse);
        Double longitude = GeoIpUtils.getLongitude(cityResponse);
        if (latitude != 0.0 && longitude != 0.0) {
            ipInfo.put("latitude", latitude);
            ipInfo.put("longitude", longitude);
        }
    }

    /**
     * 添加ASN信息到IP信息Map
     *
     * @param asnResponse ASN响应
     * @param ipInfo IP信息Map
     */
    private void addAsnInfo(AsnResponse asnResponse, Map<String, Object> ipInfo) {
        String asn = GeoIpUtils.getAsn(asnResponse);
        if (!StringUtils.isEmpty(asn)) {
            ipInfo.put("asn", asn);
        }

        String asnOrg = asnResponse.getAutonomousSystemOrganization();
        if (!StringUtils.isEmpty(asnOrg)) {
            ipInfo.put("asn_org", asnOrg);
        }
    }



    /**
     * 将丰富化信息添加到Row中（保持原有功能用于向后兼容）
     *
     * @param originalRow 原始Row
     * @param enrichmentInfo 丰富化信息
     * @return 丰富化后的Row
     */
    private Row addEnrichmentToRow(Row originalRow, Map<String, Object> enrichmentInfo) {
        // 创建新的Row，包含原始数据和丰富化信息
        Row enrichedRow = Row.withNames();

        // 复制原始字段
        Set<String> fieldNames = originalRow.getFieldNames(true);
        if (fieldNames != null) {
            for (String fieldName : fieldNames) {
                enrichedRow.setField(fieldName, originalRow.getField(fieldName));
            }
        }

        // 添加丰富化字段（使用前缀避免冲突）
        String prefix = "enriched_";
        for (Map.Entry<String, Object> entry : enrichmentInfo.entrySet()) {
            String fieldName = prefix + entry.getKey();
            enrichedRow.setField(fieldName, entry.getValue());
        }

        return enrichedRow;
    }
}
