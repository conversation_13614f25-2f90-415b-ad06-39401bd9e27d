package com.geeksec.nta.datawarehouse.etl.constant;

/**
 * ODS层字段名常量定义
 * 对应ods_session_logs表和相关ODS层表的字段名
 * 
 * <AUTHOR>
 */
public final class OdsFieldConstants {
    
    // ========== 基础会话标识字段 ==========
    
    /** session_id字段 */
    public static final String SESSION_ID = "session_id";
    /** src_ip字段 */
    public static final String SRC_IP = "src_ip";
    /** dst_ip字段 */
    public static final String DST_IP = "dst_ip";
    /** src_port字段 */
    public static final String SRC_PORT = "src_port";
    /** dst_port字段 */
    public static final String DST_PORT = "dst_port";
    /** ippro字段 */
    public static final String IP_PROTOCOL = "ippro";
    /** begin_time字段 */
    public static final String BEGIN_TIME = "begin_time";
    /** begin_nsec字段 */
    public static final String BEGIN_NSEC = "begin_nsec";
    /** end_time字段 */
    public static final String END_TIME = "end_time";
    /** end_nsec字段 */
    public static final String END_NSEC = "end_nsec";
    /** server_ip字段 */
    public static final String SERVER_IP = "server_ip";
    /** app_id字段 */
    public static final String APP_ID = "app_id";
    /** app_name字段 */
    public static final String APP_NAME = "app_name";
    /** thread_id字段 */
    public static final String THREAD_ID = "thread_id";
    /** task_id字段 */
    public static final String TASK_ID = "task_id";
    /** batch_id字段 */
    public static final String BATCH_ID = "batch_id";
    
    // ========== 会话基础信息字段 ==========
    
    /** smac字段 */
    public static final String SMAC = "smac";
    /** dmac字段 */
    public static final String DMAC = "dmac";
    /** rule_num字段 */
    public static final String RULE_NUM = "rule_num";
    /** rule_level字段 */
    public static final String RULE_LEVEL = "rule_level";
    /** syn字段 */
    public static final String SYN = "syn";
    /** syn_ack字段 */
    public static final String SYN_ACK = "syn_ack";
    /** rule_msg字段 */
    public static final String RULE_MSG = "rule_msg";
    /** rule_labels字段 */
    public static final String RULE_LABELS = "rule_labels";
    /** port_list字段 */
    public static final String PORT_LIST = "port_list";
    
    // ========== 会话统计信息字段 ==========
    
    /** stats_stotalsign字段 */
    public static final String STATS_STOTALSIGN = "stats_stotalsign";
    /** stats_dtotalsign字段 */
    public static final String STATS_DTOTALSIGN = "stats_dtotalsign";
    /** stats_distbytes字段 */
    public static final String STATS_DISTBYTES = "stats_distbytes";
    /** stats_distbytesnum字段 */
    public static final String STATS_DISTBYTESNUM = "stats_distbytesnum";
    /** stats_distcsq字段 */
    public static final String STATS_DISTCSQ = "stats_distcsq";
    /** stats_distcsqt字段 */
    public static final String STATS_DISTCSQT = "stats_distcsqt";
    /** stats_sdistlen字段 */
    public static final String STATS_SDISTLEN = "stats_sdistlen";
    /** stats_ddistlen字段 */
    public static final String STATS_DDISTLEN = "stats_ddistlen";
    /** stats_sdistdur字段 */
    public static final String STATS_SDISTDUR = "stats_sdistdur";
    /** stats_ddistdur字段 */
    public static final String STATS_DDISTDUR = "stats_ddistdur";
    /** stats_distdur字段 */
    public static final String STATS_DISTDUR = "stats_distdur";
    /** stats_prolist_num字段 */
    public static final String STATS_PROLIST_NUM = "stats_prolist_num";
    /** stats_prolist字段 */
    public static final String STATS_PROLIST = "stats_prolist";
    /** sio_sign字段 */
    public static final String SIO_SIGN = "sio_sign";
    /** dio_sign字段 */
    public static final String DIO_SIGN = "dio_sign";
    /** ext_json字段 */
    public static final String EXT_JSON = "ext_json";
    
    // ========== TCP连接信息字段 ==========
    
    /** stats_src_mss字段 */
    public static final String STATS_SRC_MSS = "stats_src_mss";
    /** stats_dst_mss字段 */
    public static final String STATS_DST_MSS = "stats_dst_mss";
    /** stats_src_window_scale字段 */
    public static final String STATS_SRC_WINDOW_SCALE = "stats_src_window_scale";
    /** stats_dst_window_scale字段 */
    public static final String STATS_DST_WINDOW_SCALE = "stats_dst_window_scale";
    /** stats_spayload_maxlen字段 */
    public static final String STATS_SPAYLOAD_MAXLEN = "stats_spayload_maxlen";
    /** stats_dpayload_maxlen字段 */
    public static final String STATS_DPAYLOAD_MAXLEN = "stats_dpayload_maxlen";
    /** stats_sack_payload_maxlen字段 */
    public static final String STATS_SACK_PAYLOAD_MAXLEN = "stats_sack_payload_maxlen";
    /** stats_dack_payload_maxlen字段 */
    public static final String STATS_DACK_PAYLOAD_MAXLEN = "stats_dack_payload_maxlen";
    /** stats_sack_payload_minlen字段 */
    public static final String STATS_SACK_PAYLOAD_MINLEN = "stats_sack_payload_minlen";
    /** stats_dack_payload_minlen字段 */
    public static final String STATS_DACK_PAYLOAD_MINLEN = "stats_dack_payload_minlen";
    /** stats_tcp_info字段 */
    public static final String STATS_TCP_INFO = "stats_tcp_info";
    /** syn_seq字段 */
    public static final String SYN_SEQ = "syn_seq";
    /** syn_seq_num字段 */
    public static final String SYN_SEQ_NUM = "syn_seq_num";
    /** stats_sipid_offset字段 */
    public static final String STATS_SIPID_OFFSET = "stats_sipid_offset";
    /** stats_dipid_offset字段 */
    public static final String STATS_DIPID_OFFSET = "stats_dipid_offset";
    /** block_cipher字段 */
    public static final String BLOCK_CIPHER = "block_cipher";
    
    // ========== 会话扩展信息字段 ==========
    
    /** duration字段 */
    public static final String DURATION = "duration";
    /** first_sender字段 */
    public static final String FIRST_SENDER = "first_sender";
    /** device_id字段 */
    public static final String DEVICE_ID = "device_id";
    /** first_proto字段 */
    public static final String FIRST_PROTO = "first_proto";
    /** proxy_ip字段 */
    public static final String PROXY_IP = "proxy_ip";
    /** proxy_port字段 */
    public static final String PROXY_PORT = "proxy_port";
    /** proxy_real_host字段 */
    public static final String PROXY_REAL_HOST = "proxy_real_host";
    /** proxy_type字段 */
    public static final String PROXY_TYPE = "proxy_type";
    /** handle_begin_time字段 */
    public static final String HANDLE_BEGIN_TIME = "handle_begin_time";
    /** handle_end_time字段 */
    public static final String HANDLE_END_TIME = "handle_end_time";
    
    /**
     * 防止实例化
     */
    private OdsFieldConstants() {
        throw new IllegalStateException("Utility class");
    }
}
