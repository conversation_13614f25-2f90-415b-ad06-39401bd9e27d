# MAC地址丰富化功能

本文档介绍如何使用MAC地址丰富化功能，包括MAC地址验证、厂商信息查询和设备类型推断。

## 功能概述

MAC地址丰富化功能提供以下能力：

1. **MAC地址验证和标准化** - 支持多种MAC地址格式的验证和标准化
2. **厂商信息查询** - 基于IEEE OUI数据库查询MAC地址对应的厂商信息
3. **设备类型推断** - 根据厂商信息推断设备类型
4. **地址类型分析** - 识别本地管理地址、组播地址等特殊类型
5. **Flink流处理集成** - 提供Flink ProcessFunction用于实时数据丰富化

## 核心组件

### 1. MacUtils工具类

位置：`flink-jobs/shared-core/src/main/java/com/geeksec/common/utils/net/MacUtils.java`

提供MAC地址处理的基础功能：

```java
// MAC地址验证
boolean isValid = MacUtils.isValidMac("AA:BB:CC:DD:EE:FF");

// MAC地址标准化
String normalized = MacUtils.normalizeMac("aa-bb-cc-dd-ee-ff");
// 结果: "AA:BB:CC:DD:EE:FF"

// 获取OUI（前3个字节）
String oui = MacUtils.getOui("AA:BB:CC:DD:EE:FF");
// 结果: "AA:BB:CC"

// 获取厂商信息
String vendor = MacUtils.getVendor("F0:EE:7A:12:34:56");
// 结果: "Apple, Inc."

// 获取完整MAC信息
Map<String, Object> macInfo = MacUtils.getMacInfo("F0:EE:7A:12:34:56");
```

### 2. MacDimensionEnrichmentFunction

位置：`flink-jobs/data-warehouse-processor/src/main/java/com/geeksec/nta/datawarehouse/etl/dim/function/MacDimensionEnrichmentFunction.java`

用于Flink流处理的MAC地址丰富化函数：

```java
// 创建丰富化函数
MacDimensionEnrichmentFunction enrichmentFunction = 
    new MacDimensionEnrichmentFunction("src_mac");

// 在Flink流中使用
DataStream<Row> enrichedStream = originalStream
    .process(enrichmentFunction);
```

### 3. MacDimensionTableFunction

位置：`flink-jobs/data-warehouse-processor/src/main/java/com/geeksec/nta/datawarehouse/etl/dim/function/MacDimensionTableFunction.java`

用于生成MAC维度表数据的函数：

```java
// 创建维度表函数
MacDimensionTableFunction dimFunction = 
    new MacDimensionTableFunction("src_mac");

// 在Flink流中使用
SingleOutputStreamOperator<Row> mainStream = originalStream
    .process(dimFunction);

// 获取维度表数据
DataStream<Row> macDimStream = mainStream
    .getSideOutput(MacDimensionTableFunction.MAC_DIM_TAG);
```

## 支持的MAC地址格式

工具类支持以下MAC地址格式：

- `AA:BB:CC:DD:EE:FF` (冒号分隔)
- `AA-BB-CC-DD-EE-FF` (连字符分隔)
- `AABBCCDDEEFF` (无分隔符)
- `AA.BB.CC.DD.EE.FF` (点分隔)

所有格式都会被标准化为 `AA:BB:CC:DD:EE:FF` 格式。

## 厂商信息查询

基于IEEE OUI数据库（`standards-oui.ieee.org.txt`）进行厂商信息查询：

- 数据库包含超过20万条OUI记录
- 支持自动加载和缓存
- 提供缓存刷新功能
- 线程安全的并发访问

## 设备类型推断

根据厂商名称推断设备类型：

| 设备类型 | 厂商关键词示例 |
|---------|---------------|
| Mobile Device | apple, iphone, ipad, samsung, xiaomi, honor |
| Network Equipment | cisco, juniper, huawei |
| Network Interface | intel, realtek, broadcom |
| Computer | hp, dell, lenovo |
| Gaming Device | nintendo, sony |
| IoT Device | espressif, arduino |

## 地址类型分析

### 本地管理地址 (Locally Administered)
- 第一个字节的第二位为1
- 通常用于虚拟化环境或隐私保护
- 示例：`02:00:00:00:00:00`

### 组播地址 (Multicast)
- 第一个字节的最低位为1
- 用于组播通信
- 示例：`01:00:5E:00:00:00`

## 丰富化字段说明

使用丰富化函数后，原始数据会添加以下字段（带`enriched_`前缀）：

| 字段名 | 类型 | 说明 |
|-------|------|------|
| enriched_mac | String | 标准化的MAC地址 |
| enriched_is_valid | Boolean | MAC地址格式是否有效 |
| enriched_oui | String | OUI（前3个字节） |
| enriched_vendor | String | 厂商名称 |
| enriched_device_type | String | 推断的设备类型 |
| enriched_is_locally_administered | Boolean | 是否为本地管理地址 |
| enriched_is_multicast | Boolean | 是否为组播地址 |
| enriched_is_potentially_randomized | Boolean | 是否可能为随机MAC |
| enriched_is_virtualized | Boolean | 是否为虚拟化厂商 |

## 使用示例

### 基础使用

```java
import com.geeksec.common.utils.net.MacUtils;

// 验证MAC地址
if (MacUtils.isValidMac(macAddress)) {
    // 获取厂商信息
    String vendor = MacUtils.getVendor(macAddress);
    System.out.println("厂商: " + vendor);
    
    // 获取完整信息
    Map<String, Object> macInfo = MacUtils.getMacInfo(macAddress);
    System.out.println("MAC信息: " + macInfo);
}
```

### Flink流处理使用

```java
import com.geeksec.nta.datawarehouse.etl.dim.function.MacDimensionEnrichmentFunction;

// 创建数据流
DataStream<Row> sourceStream = env.addSource(new MySource());

// 添加MAC地址丰富化
DataStream<Row> enrichedStream = sourceStream
    .process(new MacDimensionEnrichmentFunction("src_mac"));

// 处理丰富化后的数据
enrichedStream.addSink(new MySink());
```

## 性能特性

- **缓存机制**: 使用Flink Backend State进行缓存，支持容错和状态恢复
- **TTL支持**: 默认24小时TTL，可配置
- **线程安全**: 支持并发访问
- **内存优化**: 使用ConcurrentHashMap进行OUI缓存

## 配置选项

### 缓存TTL配置

```java
// 自定义缓存TTL（默认24小时）
MacDimensionEnrichmentFunction enrichmentFunction = 
    new MacDimensionEnrichmentFunction("src_mac", Duration.ofHours(12));
```

### OUI数据库刷新

```java
// 手动刷新OUI数据库
MacUtils.refreshOuiDatabase();

// 获取缓存大小
int cacheSize = MacUtils.getOuiCacheSize();
```

## 测试

运行测试用例：

```bash
mvn test -Dtest=MacUtilsTest
```

运行示例程序：

```bash
mvn exec:java -Dexec.mainClass="com.geeksec.nta.datawarehouse.etl.dim.function.MacEnrichmentExample"
```

## 注意事项

1. **OUI数据库文件**: 确保`standards-oui.ieee.org.txt`文件位于classpath中
2. **内存使用**: OUI数据库会占用一定内存，建议在生产环境中监控内存使用情况
3. **更新频率**: IEEE定期更新OUI数据库，建议定期更新数据文件
4. **错误处理**: 对于无效的MAC地址，函数会返回默认值而不会抛出异常

## 扩展开发

如需扩展功能，可以：

1. 在`MacUtils`中添加新的分析方法
2. 在设备类型推断中添加新的厂商规则
3. 扩展丰富化字段，添加更多分析维度
4. 集成其他MAC地址相关的数据源
