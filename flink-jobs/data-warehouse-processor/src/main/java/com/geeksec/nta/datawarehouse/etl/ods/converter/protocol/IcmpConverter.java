package com.geeksec.nta.datawarehouse.etl.ods.converter.protocol;

import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import com.geeksec.common.utils.time.DateTimeUtils;
import com.geeksec.nta.datawarehouse.etl.constant.FieldConstants;
import com.geeksec.nta.datawarehouse.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.datawarehouse.etl.ods.tag.MessageOutputTag;
import com.geeksec.proto.ZMPNMsg;

import lombok.extern.slf4j.Slf4j;

/**
 * Converter for ICMP protocol messages.
 * Maps ICMP protocol data from protobuf messages to Doris
 * ods_icmp_protocol_metadata table format.
 * This converter is aligned with the latest ods_icmp_protocol_metadata table
 * schema.
 *
 * <AUTHOR> Team
 */
@Slf4j
public class IcmpConverter extends AbstractProtobufMessageConverter {

    @Override
    protected Row convertMessage(ZMPNMsg.JKNmsg msg) {
        if (!msg.hasIcmp()) {
            log.warn("JKNmsg does not contain ICMP message");
            return Row.withNames();
        }

        ZMPNMsg.icmp_msg icmp = msg.getIcmp();
        ZMPNMsg.Comm_msg commMsg = icmp.getCommMsg();

        // 创建带有命名字段的Row
        Row row = Row.withNames();

        // 添加通用字段
        addCommonFields(row, commMsg);

        // 添加ICMP特定字段
        addIcmpFields(row, icmp);

        return row;
    }

    @Override
    public OutputTag<Row> getOutputTag() {
        return MessageOutputTag.ICMP_STREAM;
    }

    /**
     * 添加通用字段到Row
     *
     * @param row     目标Row对象
     * @param commMsg 通用消息对象
     */
    private void addCommonFields(Row row, ZMPNMsg.Comm_msg commMsg) {
        row.setField(FieldConstants.FIELD_SESSION_ID, commMsg.getSessionId());
        row.setField(FieldConstants.FIELD_BEGIN_TIME, DateTimeUtils.fromTimestampSeconds(commMsg.getBeginTime()));
        row.setField(FieldConstants.FIELD_SRC_IP, commMsg.getSrcIp());
        row.setField(FieldConstants.FIELD_DST_IP, commMsg.getDstIp());
        row.setField(FieldConstants.FIELD_SRC_PORT, commMsg.getSrcPort());
        row.setField(FieldConstants.FIELD_DST_PORT, commMsg.getDstPort());
        row.setField(FieldConstants.FIELD_IP_PROTOCOL, commMsg.getIppro());
        row.setField(FieldConstants.FIELD_BEGIN_NSEC, commMsg.getBeginNsec());
        row.setField(FieldConstants.FIELD_SERVER_IP, commMsg.getServerIp());
        row.setField(FieldConstants.FIELD_APP_ID, commMsg.getAppId());
        row.setField(FieldConstants.FIELD_APP_NAME, commMsg.getAppName());
        row.setField(FieldConstants.FIELD_THREAD_ID, commMsg.getThreadId());
        row.setField(FieldConstants.FIELD_TASK_ID, commMsg.getTaskId());
        row.setField(FieldConstants.FIELD_BATCH_ID, commMsg.getBatchId());
    }

    /**
     * 添加ICMP特定字段到Row
     *
     * @param row  目标Row对象
     * @param icmp ICMP消息对象
     */
    private void addIcmpFields(Row row, ZMPNMsg.icmp_msg icmp) {
        addIcmpBasicFields(row, icmp);
        addIcmpTimestampFields(row, icmp);
        addIcmpNetworkFields(row, icmp);
        addIcmpExceptionFields(row, icmp);
        addIcmpQueryFields(row, icmp);
        addIcmpNdpFields(row, icmp);
        addIcmpMiscFields(row, icmp);
    }

    /**
     * 添加基本ICMP字段
     */
    private void addIcmpBasicFields(Row row, ZMPNMsg.icmp_msg icmp) {
        row.setField(FieldConstants.FIELD_MSG_TYPE, icmp.getMsgType());
        row.setField(FieldConstants.FIELD_INFO_CODE, icmp.getInfoCode());
        row.setField(FieldConstants.FIELD_ECHO_SEQ_NUM, icmp.getEchoSeqNum());
        row.setField(FieldConstants.FIELD_DATA_CON, bytesToBase64String(icmp.getDataCon()));
        row.setField(FieldConstants.FIELD_VER, icmp.getVer());
    }

    /**
     * 添加时间戳相关字段
     */
    private void addIcmpTimestampFields(Row row, ZMPNMsg.icmp_msg icmp) {
        row.setField(FieldConstants.FIELD_ORIG_TIME_STAMP, icmp.getOrigTimeStamp());
        row.setField(FieldConstants.FIELD_RECV_TIME_STAMP, icmp.getRecvTimeStamp());
        row.setField(FieldConstants.FIELD_TRANS_TIME_STAMP, icmp.getTransTimeStamp());
        row.setField(FieldConstants.FIELD_RES_TIME, icmp.getResTime());
        row.setField(FieldConstants.FIELD_RESPONSE_TIME, icmp.getResponseTime());
    }

    /**
     * 添加网络相关字段
     */
    private void addIcmpNetworkFields(Row row, ZMPNMsg.icmp_msg icmp) {
        if (icmp.hasUnrSrcAddr()) {
            row.setField(FieldConstants.FIELD_UNR_SRC_ADDR, icmp.getUnrSrcAddr());
        }
        if (icmp.hasUnrDstAddr()) {
            row.setField(FieldConstants.FIELD_UNR_DST_ADDR, icmp.getUnrDstAddr());
        }

        row.setField(FieldConstants.FIELD_UNR_PROT, icmp.getUnrProt());
        row.setField(FieldConstants.FIELD_UNC_TTL, icmp.getUncTtl());
        row.setField(FieldConstants.FIELD_MASK, icmp.getMask());
        row.setField(FieldConstants.FIELD_NEXT_HOP_MTU, icmp.getNextHopMtu());
        row.setField(FieldConstants.FIELD_TTL, icmp.getTtl());
        row.setField(FieldConstants.FIELD_REP_TTL, icmp.getRepTtl());
        row.setField(FieldConstants.FIELD_UNREACHABLE_SOURCE_PORT, icmp.getUnreachableSourcePort());
        row.setField(FieldConstants.FIELD_UNREACHABLE_DESTINATION_PORT, icmp.getUnreachableDestinationPort());
    }

    /**
     * 添加异常相关字段
     */
    private void addIcmpExceptionFields(Row row, ZMPNMsg.icmp_msg icmp) {
        row.setField(FieldConstants.FIELD_EXC_POINTER, icmp.getExcPointer());
        row.setField(FieldConstants.FIELD_EXC_PROT, icmp.getExcProt());
        row.setField(FieldConstants.FIELD_EXC_SRC_ADDR, icmp.getExcSrcAddr());
        row.setField(FieldConstants.FIELD_EXC_DST_ADDR, icmp.getExcDstAddr());
        row.setField(FieldConstants.FIELD_EXC_SRC_PORT, icmp.getExcSrcPort());
        row.setField(FieldConstants.FIELD_EXC_DST_PORT, icmp.getExcDstPort());
        row.setField(FieldConstants.FIELD_EXC_TTL, icmp.getExcTtl());
    }

    /**
     * 添加查询相关字段
     */
    private void addIcmpQueryFields(Row row, ZMPNMsg.icmp_msg icmp) {
        row.setField(FieldConstants.FIELD_QUR_TYPE, icmp.getQurType());

        if (icmp.hasQurIpv6Addr()) {
            row.setField(FieldConstants.FIELD_QUR_IPV6_ADDR, icmp.getQurIpv6Addr());
        }
        if (icmp.hasQurIpv4Addr()) {
            row.setField(FieldConstants.FIELD_QUR_IPV4_ADDR, icmp.getQurIpv4Addr());
        }
        if (icmp.hasQurDns()) {
            row.setField(FieldConstants.FIELD_QUR_DNS, icmp.getQurDns());
        }
    }

    /**
     * 添加NDP (Neighbor Discovery Protocol) 相关字段
     */
    private void addIcmpNdpFields(Row row, ZMPNMsg.icmp_msg icmp) {
        row.setField(FieldConstants.FIELD_NDP_LIFE_TIME, icmp.getNdpLifeTime());
        row.setField(FieldConstants.FIELD_NDP_PRE_LEN, icmp.getNdpPreLen());
        row.setField(FieldConstants.FIELD_NDP_VAL_LIFE_TIME, icmp.getNdpValLifeTime());
        row.setField(FieldConstants.FIELD_NDP_CUR_MTU, icmp.getNdpCurMtu());

        if (icmp.hasNdpLinkAddr()) {
            row.setField(FieldConstants.FIELD_NDP_LINK_ADDR, icmp.getNdpLinkAddr());
        }
        if (icmp.hasNdpPreFix()) {
            row.setField(FieldConstants.FIELD_NDP_PRE_FIX, icmp.getNdpPreFix());
        }
        if (icmp.hasNdpTarAddr()) {
            row.setField(FieldConstants.FIELD_NDP_TAR_ADDR, icmp.getNdpTarAddr());
        }
    }

    /**
     * 添加其他杂项字段
     */
    private void addIcmpMiscFields(Row row, ZMPNMsg.icmp_msg icmp) {
        if (icmp.hasGwAddr()) {
            row.setField(FieldConstants.FIELD_GW_ADDR, icmp.getGwAddr());
        }
        if (icmp.hasMulCastAddr()) {
            row.setField(FieldConstants.FIELD_MUL_CAST_ADDR, icmp.getMulCastAddr());
        }

        row.setField(FieldConstants.FIELD_CHECK_SUM, icmp.getCheckSum());
        row.setField(FieldConstants.FIELD_CHECK_SUM_REPLY, icmp.getCheckSumReply());
        row.setField(FieldConstants.FIELD_RTRADDR, icmp.getRtraddr());
    }
}