package com.geeksec.nta.datawarehouse.etl.dim.function;

import java.time.Duration;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import org.apache.commons.lang3.StringUtils;

/**
 * 维度丰富化管理器
 * 统一管理IP和域名维度的丰富化处理，使用Flink Backend State管理缓存
 *
 * <AUTHOR>
 */
public class DimensionEnrichmentManager extends ProcessFunction<Row, Row> {

    /** IP维度丰富化处理函数 */
    private transient IpDimensionEnrichmentFunction ipEnrichmentFunction;

    /** 域名维度丰富化处理函数 */
    private transient DomainDimensionEnrichmentFunction domainEnrichmentFunction;

    /** 丰富化配置 */
    private final EnrichmentConfig config;

    /**
     * 丰富化配置类
     */
    public static class EnrichmentConfig {
        private final String ipFieldName;
        private final String domainFieldName;
        private final Duration cacheTtl;
        private final boolean enableIpEnrichment;
        private final boolean enableDomainEnrichment;

        public EnrichmentConfig(String ipFieldName, String domainFieldName) {
            this(ipFieldName, domainFieldName, Duration.ofHours(24), true, true);
        }

        public EnrichmentConfig(String ipFieldName, String domainFieldName, Duration cacheTtl,
                              boolean enableIpEnrichment, boolean enableDomainEnrichment) {
            this.ipFieldName = ipFieldName;
            this.domainFieldName = domainFieldName;
            this.cacheTtl = cacheTtl;
            this.enableIpEnrichment = enableIpEnrichment;
            this.enableDomainEnrichment = enableDomainEnrichment;
        }

        public String getIpFieldName() { return ipFieldName; }
        public String getDomainFieldName() { return domainFieldName; }
        public Duration getCacheTtl() { return cacheTtl; }
        public boolean isEnableIpEnrichment() { return enableIpEnrichment; }
        public boolean isEnableDomainEnrichment() { return enableDomainEnrichment; }
    }

    /**
     * 构造函数
     *
     * @param config 丰富化配置
     */
    public DimensionEnrichmentManager(EnrichmentConfig config) {
        this.config = config;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 初始化IP维度丰富化处理函数
        if (config.isEnableIpEnrichment() && !StringUtils.isEmpty(config.getIpFieldName())) {
            ipEnrichmentFunction = new IpDimensionEnrichmentFunction(
                config.getIpFieldName(), config.getCacheTtl());
            ipEnrichmentFunction.setRuntimeContext(getRuntimeContext());
            ipEnrichmentFunction.open(parameters);
        }

        // 初始化域名维度丰富化处理函数
        if (config.isEnableDomainEnrichment() && !StringUtils.isEmpty(config.getDomainFieldName())) {
            domainEnrichmentFunction = new DomainDimensionEnrichmentFunction(
                config.getDomainFieldName(), config.getCacheTtl());
            domainEnrichmentFunction.setRuntimeContext(getRuntimeContext());
            domainEnrichmentFunction.open(parameters);
        }
    }

    @Override
    public void processElement(Row value, Context ctx, Collector<Row> out) throws Exception {
        Row currentRow = value;

        // 执行IP维度丰富化
        if (ipEnrichmentFunction != null) {
            EnrichmentCollector ipCollector = new EnrichmentCollector();
            ipEnrichmentFunction.processElement(currentRow, ctx, ipCollector);
            if (!ipCollector.getResults().isEmpty()) {
                currentRow = ipCollector.getResults().get(0);
            }
        }

        // 执行域名维度丰富化
        if (domainEnrichmentFunction != null) {
            EnrichmentCollector domainCollector = new EnrichmentCollector();
            domainEnrichmentFunction.processElement(currentRow, ctx, domainCollector);
            if (!domainCollector.getResults().isEmpty()) {
                currentRow = domainCollector.getResults().get(0);
            }
        }

        // 输出最终结果
        out.collect(currentRow);
    }

    /**
     * 内部收集器，用于收集丰富化结果
     */
    private static class EnrichmentCollector implements Collector<Row> {
        private final java.util.List<Row> results = new java.util.ArrayList<>();

        @Override
        public void collect(Row record) {
            results.add(record);
        }

        @Override
        public void close() {
            // 不需要实现
        }

        public java.util.List<Row> getResults() {
            return results;
        }
    }

    /**
     * 创建默认配置的丰富化管理器
     *
     * @param ipFieldName IP字段名
     * @param domainFieldName 域名字段名
     * @return 丰富化管理器实例
     */
    public static DimensionEnrichmentManager createDefault(String ipFieldName, String domainFieldName) {
        return new DimensionEnrichmentManager(new EnrichmentConfig(ipFieldName, domainFieldName));
    }

    /**
     * 创建仅IP丰富化的管理器
     *
     * @param ipFieldName IP字段名
     * @return 丰富化管理器实例
     */
    public static DimensionEnrichmentManager createIpOnly(String ipFieldName) {
        return new DimensionEnrichmentManager(new EnrichmentConfig(
            ipFieldName, null, Duration.ofHours(24), true, false));
    }

    /**
     * 创建仅域名丰富化的管理器
     *
     * @param domainFieldName 域名字段名
     * @return 丰富化管理器实例
     */
    public static DimensionEnrichmentManager createDomainOnly(String domainFieldName) {
        return new DimensionEnrichmentManager(new EnrichmentConfig(
            null, domainFieldName, Duration.ofHours(24), false, true));
    }
}
