package com.geeksec.nta.datawarehouse.etl.constant;

/**
 * DWD层包统计信息字段名常量定义
 * 对应dwd_session_logs表中的包统计相关字段
 * 
 * <AUTHOR>
 */
public final class DwdPacketFieldConstants {
    
    // ========== 包统计信息字段 ==========
    
    /** src_max_packet_length字段 */
    public static final String SRC_MAX_PACKET_LENGTH = "src_max_packet_length";
    /** dst_max_packet_length字段 */
    public static final String DST_MAX_PACKET_LENGTH = "dst_max_packet_length";
    /** src_packet_count字段 */
    public static final String SRC_PACKET_COUNT = "src_packet_count";
    /** dst_packet_count字段 */
    public static final String DST_PACKET_COUNT = "dst_packet_count";
    /** src_payload_packet_count字段 */
    public static final String SRC_PAYLOAD_PACKET_COUNT = "src_payload_packet_count";
    /** dst_payload_packet_count字段 */
    public static final String DST_PAYLOAD_PACKET_COUNT = "dst_payload_packet_count";
    /** src_total_bytes字段 */
    public static final String SRC_TOTAL_BYTES = "src_total_bytes";
    /** dst_total_bytes字段 */
    public static final String DST_TOTAL_BYTES = "dst_total_bytes";
    /** src_payload_bytes字段 */
    public static final String SRC_PAYLOAD_BYTES = "src_payload_bytes";
    /** dst_payload_bytes字段 */
    public static final String DST_PAYLOAD_BYTES = "dst_payload_bytes";
    /** src_fin_packet_count字段 */
    public static final String SRC_FIN_PACKET_COUNT = "src_fin_packet_count";
    /** dst_fin_packet_count字段 */
    public static final String DST_FIN_PACKET_COUNT = "dst_fin_packet_count";
    /** src_rst_packet_count字段 */
    public static final String SRC_RST_PACKET_COUNT = "src_rst_packet_count";
    /** dst_rst_packet_count字段 */
    public static final String DST_RST_PACKET_COUNT = "dst_rst_packet_count";
    /** src_syn_packet_count字段 */
    public static final String SRC_SYN_PACKET_COUNT = "src_syn_packet_count";
    /** dst_syn_packet_count字段 */
    public static final String DST_SYN_PACKET_COUNT = "dst_syn_packet_count";
    /** src_syn_bytes字段 */
    public static final String SRC_SYN_BYTES = "src_syn_bytes";
    /** dst_syn_bytes字段 */
    public static final String DST_SYN_BYTES = "dst_syn_bytes";
    /** src_ttl_max字段 */
    public static final String SRC_TTL_MAX = "src_ttl_max";
    /** dst_ttl_max字段 */
    public static final String DST_TTL_MAX = "dst_ttl_max";
    /** src_ttl_min字段 */
    public static final String SRC_TTL_MIN = "src_ttl_min";
    /** dst_ttl_min字段 */
    public static final String DST_TTL_MIN = "dst_ttl_min";
    /** src_duration_max字段 */
    public static final String SRC_DURATION_MAX = "src_duration_max";
    /** dst_duration_max字段 */
    public static final String DST_DURATION_MAX = "dst_duration_max";
    /** src_duration_min字段 */
    public static final String SRC_DURATION_MIN = "src_duration_min";
    /** dst_duration_min字段 */
    public static final String DST_DURATION_MIN = "dst_duration_min";
    /** src_disorder_packet_count字段 */
    public static final String SRC_DISORDER_PACKET_COUNT = "src_disorder_packet_count";
    /** dst_disorder_packet_count字段 */
    public static final String DST_DISORDER_PACKET_COUNT = "dst_disorder_packet_count";
    /** src_resend_packet_count字段 */
    public static final String SRC_RESEND_PACKET_COUNT = "src_resend_packet_count";
    /** dst_resend_packet_count字段 */
    public static final String DST_RESEND_PACKET_COUNT = "dst_resend_packet_count";
    /** src_lost_packet_length字段 */
    public static final String SRC_LOST_PACKET_LENGTH = "src_lost_packet_length";
    /** dst_lost_packet_length字段 */
    public static final String DST_LOST_PACKET_LENGTH = "dst_lost_packet_length";
    /** src_psh_packet_count字段 */
    public static final String SRC_PSH_PACKET_COUNT = "src_psh_packet_count";
    /** dst_psh_packet_count字段 */
    public static final String DST_PSH_PACKET_COUNT = "dst_psh_packet_count";
    /** protocol_packet_count字段 */
    public static final String PROTOCOL_PACKET_COUNT = "protocol_packet_count";
    /** unknown_protocol_packet_count字段 */
    public static final String UNKNOWN_PROTOCOL_PACKET_COUNT = "unknown_protocol_packet_count";
    /** syn_with_data_count字段 */
    public static final String SYN_WITH_DATA_COUNT = "syn_with_data_count";
    /** src_bad_packet_count字段 */
    public static final String SRC_BAD_PACKET_COUNT = "src_bad_packet_count";
    /** dst_bad_packet_count字段 */
    public static final String DST_BAD_PACKET_COUNT = "dst_bad_packet_count";
    /** app_detection_packet_id字段 */
    public static final String APP_DETECTION_PACKET_ID = "app_detection_packet_id";
    /** src_payload_samples字段 */
    public static final String SRC_PAYLOAD_SAMPLES = "src_payload_samples";
    /** dst_payload_samples字段 */
    public static final String DST_PAYLOAD_SAMPLES = "dst_payload_samples";
    /** packet_info字段 */
    public static final String PACKET_INFO = "packet_info";
    
    /**
     * 防止实例化
     */
    private DwdPacketFieldConstants() {
        throw new IllegalStateException("Utility class");
    }
}
