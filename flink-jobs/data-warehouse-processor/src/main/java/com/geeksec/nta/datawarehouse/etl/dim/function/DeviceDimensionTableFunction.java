package com.geeksec.nta.datawarehouse.etl.dim.function;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import com.geeksec.common.utils.time.DateTimeUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 设备维度表处理函数，用于生成符合dim_device表结构的维度数据
 *
 * 使用Flink Backend State进行缓存管理，支持TTL自动清理
 *
 * <AUTHOR>
 */
@Slf4j
public class DeviceDimensionTableFunction extends ProcessFunction<Row, Row> {

    private static final long serialVersionUID = 1L;

    /**
     * 设备维度数据输出标签
     */
    public static final OutputTag<Row> DEVICE_DIM_TAG = new OutputTag<Row>("device-dimension") {};

    private final String deviceTypeFieldName;
    private final Duration ttl;

    // State for caching Device dimension data
    private transient ValueState<Map<String, Object>> deviceDimensionState;

    /**
     * 构造函数
     *
     * @param deviceTypeFieldName 设备类型字段名
     */
    public DeviceDimensionTableFunction(String deviceTypeFieldName) {
        this(deviceTypeFieldName, Duration.ofHours(24)); // 默认24小时TTL
    }

    /**
     * 构造函数
     *
     * @param deviceTypeFieldName 设备类型字段名
     * @param ttl 状态TTL时间
     */
    public DeviceDimensionTableFunction(String deviceTypeFieldName, Duration ttl) {
        this.deviceTypeFieldName = deviceTypeFieldName;
        this.ttl = ttl;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 配置状态TTL
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(ttl)
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();

        // 初始化设备维度状态
        ValueStateDescriptor<Map<String, Object>> deviceStateDescriptor =
                new ValueStateDescriptor<>("device-dimension-state", Types.MAP(Types.STRING, Types.GENERIC(Object.class)));
        deviceStateDescriptor.enableTimeToLive(ttlConfig);
        deviceDimensionState = getRuntimeContext().getState(deviceStateDescriptor);
    }

    @Override
    public void processElement(Row value, Context ctx, Collector<Row> out) throws Exception {
        try {
            String deviceType = getStringFieldValue(value, deviceTypeFieldName);
            if (deviceType == null || deviceType.trim().isEmpty()) {
                out.collect(value);
                return;
            }

            // 检查状态中是否已存在该设备的维度数据
            Map<String, Object> existingDeviceInfo = deviceDimensionState.value();
            if (existingDeviceInfo == null) {
                // 首次遇到该设备，创建维度数据
                Map<String, Object> deviceInfo = createDeviceDimensionInfo(value, deviceType);
                deviceDimensionState.update(deviceInfo);

                // 创建维度表记录并输出到侧输出流
                Row deviceDimensionRow = createDimensionRow(deviceType, deviceInfo);
                ctx.output(DEVICE_DIM_TAG, deviceDimensionRow);

                log.debug("创建新的设备维度数据: {}", deviceType);
            }

            // 继续传递原始数据
            out.collect(value);

        } catch (Exception e) {
            log.error("处理设备维度数据时发生错误: {}", e.getMessage(), e);
            out.collect(value);
        }
    }

    /**
     * 创建设备维度信息
     *
     * @param value 原始数据Row
     * @param deviceType 设备类型
     * @return 设备维度信息Map
     */
    private Map<String, Object> createDeviceDimensionInfo(Row value, String deviceType) {
        Map<String, Object> deviceInfo = new HashMap<>();

        // 基础信息
        deviceInfo.put("device_type", deviceType);

        // 获取设备厂商信息
        String deviceVendor = null;

        // 尝试从不同字段获取厂商信息
        if (deviceTypeFieldName.contains("client")) {
            deviceVendor = getStringFieldValue(value, "client_device_vendor");
        } else if (deviceTypeFieldName.contains("server")) {
            deviceVendor = getStringFieldValue(value, "server_device_vendor");
        } else {
            // 通用字段名
            deviceVendor = getStringFieldValue(value, "device_vendor");
        }

        deviceInfo.put("device_vendor", deviceVendor != null ? deviceVendor : "Unknown");

        return deviceInfo;
    }

    /**
     * 创建符合维度表结构的设备维度记录
     *
     * @param deviceType 设备类型
     * @param deviceInfo 设备信息
     * @return 符合dim_device表结构的Row
     */
    private Row createDimensionRow(String deviceType, Map<String, Object> deviceInfo) {
        Row dimensionRow = Row.withNames();

        // 设置主键字段
        dimensionRow.setField("device_type", deviceType);

        // 设置设备厂商信息字段
        dimensionRow.setField("device_vendor", deviceInfo.get("device_vendor"));

        // 设置聚合字段初始值
        dimensionRow.setField("total_bytes", 0L);
        dimensionRow.setField("total_packets", 0L);
        dimensionRow.setField("total_bytes_sent", 0L);
        dimensionRow.setField("total_bytes_received", 0L);
        dimensionRow.setField("total_packets_sent", 0L);
        dimensionRow.setField("total_packets_received", 0L);
        dimensionRow.setField("session_count", 1);
        dimensionRow.setField("total_duration", 0L);
        dimensionRow.setField("avg_bps", 0.0);

        // 设置时间字段
        DateTimeFormatter formatter = DateTimeUtils.DEFAULT_DATE_TIME_FORMATTER != null
                ? DateTimeUtils.DEFAULT_DATE_TIME_FORMATTER
                : DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String currentTime = LocalDateTime.now().format(formatter);
        dimensionRow.setField("first_seen", currentTime);
        dimensionRow.setField("last_seen", currentTime);
        dimensionRow.setField("create_time", currentTime);
        dimensionRow.setField("update_time", currentTime);

        return dimensionRow;
    }

    /**
     * 从Row中获取字符串字段值
     *
     * @param row Row对象
     * @param fieldName 字段名
     * @return 字段值，如果不存在或类型不匹配则返回null
     */
    private String getStringFieldValue(Row row, String fieldName) {
        try {
            Set<String> fieldNames = row.getFieldNames(true);
            if (fieldNames != null && fieldNames.contains(fieldName)) {
                Object fieldValue = row.getField(fieldName);
                if (fieldValue instanceof String) {
                    return (String) fieldValue;
                }
            }
        } catch (Exception e) {
            log.warn("获取字符串字段值时发生错误，字段名: {}, 错误: {}", fieldName, e.getMessage());
        }
        return null;
    }
}
