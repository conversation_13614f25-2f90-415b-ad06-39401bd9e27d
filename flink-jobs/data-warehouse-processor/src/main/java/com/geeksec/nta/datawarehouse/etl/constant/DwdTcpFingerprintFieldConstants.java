package com.geeksec.nta.datawarehouse.etl.constant;

/**
 * DWD层TCP指纹特征字段名常量定义
 * 对应dwd_session_logs表中的TCP指纹特征相关字段
 * 
 * <AUTHOR>
 */
public final class DwdTcpFingerprintFieldConstants {
    
    // ========== TCP客户端指纹特征字段 ==========
    
    /** tcp_client_ecn_ip_ect字段 */
    public static final String TCP_CLIENT_ECN_IP_ECT = "tcp_client_ecn_ip_ect";
    /** tcp_client_df_nonzero_ipid字段 */
    public static final String TCP_CLIENT_DF_NONZERO_IPID = "tcp_client_df_nonzero_ipid";
    /** tcp_client_flag_cwr字段 */
    public static final String TCP_CLIENT_FLAG_CWR = "tcp_client_flag_cwr";
    /** tcp_client_flag_ece字段 */
    public static final String TCP_CLIENT_FLAG_ECE = "tcp_client_flag_ece";
    /** tcp_client_zero_timestamp字段 */
    public static final String TCP_CLIENT_ZERO_TIMESTAMP = "tcp_client_zero_timestamp";
    /** tcp_client_ttl字段 */
    public static final String TCP_CLIENT_TTL = "tcp_client_ttl";
    /** tcp_client_eol_padding_bytes字段 */
    public static final String TCP_CLIENT_EOL_PADDING_BYTES = "tcp_client_eol_padding_bytes";
    /** tcp_client_window_scale字段 */
    public static final String TCP_CLIENT_WINDOW_SCALE = "tcp_client_window_scale";
    /** tcp_client_window_mss_ratio字段 */
    public static final String TCP_CLIENT_WINDOW_MSS_RATIO = "tcp_client_window_mss_ratio";
    /** tcp_client_options_layout字段 */
    public static final String TCP_CLIENT_OPTIONS_LAYOUT = "tcp_client_options_layout";
    
    // ========== TCP服务端指纹特征字段 ==========
    
    /** tcp_server_ecn_ip_ect字段 */
    public static final String TCP_SERVER_ECN_IP_ECT = "tcp_server_ecn_ip_ect";
    /** tcp_server_df_nonzero_ipid字段 */
    public static final String TCP_SERVER_DF_NONZERO_IPID = "tcp_server_df_nonzero_ipid";
    /** tcp_server_flag_cwr字段 */
    public static final String TCP_SERVER_FLAG_CWR = "tcp_server_flag_cwr";
    /** tcp_server_flag_ece字段 */
    public static final String TCP_SERVER_FLAG_ECE = "tcp_server_flag_ece";
    /** tcp_server_zero_timestamp字段 */
    public static final String TCP_SERVER_ZERO_TIMESTAMP = "tcp_server_zero_timestamp";
    /** tcp_server_ttl字段 */
    public static final String TCP_SERVER_TTL = "tcp_server_ttl";
    /** tcp_server_eol_padding_bytes字段 */
    public static final String TCP_SERVER_EOL_PADDING_BYTES = "tcp_server_eol_padding_bytes";
    /** tcp_server_window_scale字段 */
    public static final String TCP_SERVER_WINDOW_SCALE = "tcp_server_window_scale";
    /** tcp_server_window_mss_ratio字段 */
    public static final String TCP_SERVER_WINDOW_MSS_RATIO = "tcp_server_window_mss_ratio";
    /** tcp_server_options_layout字段 */
    public static final String TCP_SERVER_OPTIONS_LAYOUT = "tcp_server_options_layout";
    
    /**
     * 防止实例化
     */
    private DwdTcpFingerprintFieldConstants() {
        throw new IllegalStateException("Utility class");
    }
}
