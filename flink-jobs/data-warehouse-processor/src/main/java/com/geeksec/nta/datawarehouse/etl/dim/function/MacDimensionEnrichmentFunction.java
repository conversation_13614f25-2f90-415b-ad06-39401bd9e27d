package com.geeksec.nta.datawarehouse.etl.dim.function;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.geeksec.common.utils.net.MacUtils;

/**
 * MAC地址维度数据丰富化处理函数
 * 使用Flink Backend State管理MAC地址厂商信息缓存，提供容错性和可扩展性
 *
 * <AUTHOR>
 */
public class MacDimensionEnrichmentFunction extends ProcessFunction<Row, Row> {

    private static final Logger log = LoggerFactory.getLogger(MacDimensionEnrichmentFunction.class);

    /** MAC信息缓存状态，使用MapState管理 */
    private transient MapState<String, Map<String, Object>> macInfoCache;

    /** 缓存TTL配置，默认24小时 */
    private final Duration cacheTtl;

    /** MAC字段名 */
    private final String macFieldName;

    /**
     * 构造函数
     *
     * @param macFieldName MAC字段名
     */
    public MacDimensionEnrichmentFunction(String macFieldName) {
        this(macFieldName, Duration.ofHours(24));
    }

    /**
     * 构造函数
     *
     * @param macFieldName MAC字段名
     * @param cacheTtl 缓存TTL时间
     */
    public MacDimensionEnrichmentFunction(String macFieldName, Duration cacheTtl) {
        this.macFieldName = macFieldName;
        this.cacheTtl = cacheTtl;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 配置状态TTL
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(cacheTtl)
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();

        // 创建MapState描述符
        MapStateDescriptor<String, Map<String, Object>> descriptor = new MapStateDescriptor<>(
                "mac-enrichment-cache",
                TypeInformation.of(new TypeHint<String>() {}),
                TypeInformation.of(new TypeHint<Map<String, Object>>() {})
        );

        // 启用TTL
        descriptor.enableTimeToLive(ttlConfig);

        // 获取状态
        macInfoCache = getRuntimeContext().getMapState(descriptor);
    }

    @Override
    public void processElement(Row value, Context ctx, Collector<Row> out) throws Exception {
        // 获取MAC地址
        String macAddress = getMacFromRow(value);

        if (StringUtils.isEmpty(macAddress) || !MacUtils.isValidMac(macAddress)) {
            // MAC地址无效，直接输出原始数据
            out.collect(value);
            return;
        }

        // 标准化MAC地址
        String normalizedMac = MacUtils.normalizeMac(macAddress);
        if (normalizedMac == null) {
            // MAC地址格式化失败，直接输出原始数据
            out.collect(value);
            return;
        }

        // 获取丰富化的MAC信息
        Map<String, Object> enrichedInfo = getEnrichedMacInfo(normalizedMac);

        // 将丰富化信息添加到Row中
        Row enrichedRow = addEnrichmentToRow(value, enrichedInfo);

        // 输出丰富化后的数据
        out.collect(enrichedRow);
    }

    /**
     * 从Row中获取MAC地址
     *
     * @param row 输入Row
     * @return MAC地址字符串
     */
    private String getMacFromRow(Row row) {
        try {
            Object macValue = row.getField(macFieldName);
            return macValue != null ? macValue.toString() : null;
        } catch (Exception e) {
            log.warn("无法从Row中获取MAC字段 {}: {}", macFieldName, e.getMessage());
            return null;
        }
    }

    /**
     * 获取丰富化的MAC信息，优先从缓存获取
     *
     * @param macAddress 标准化的MAC地址
     * @return 丰富化的MAC信息
     * @throws Exception 状态访问异常
     */
    private Map<String, Object> getEnrichedMacInfo(String macAddress) throws Exception {
        // 检查缓存
        Map<String, Object> cachedInfo = macInfoCache.get(macAddress);
        if (cachedInfo != null) {
            return cachedInfo;
        }

        // 缓存未命中，重新查询并缓存
        Map<String, Object> enrichedInfo = enrichMacInfo(macAddress);
        macInfoCache.put(macAddress, enrichedInfo);

        return enrichedInfo;
    }

    /**
     * 丰富MAC地址信息
     * 为MAC地址添加厂商信息、OUI信息等
     *
     * @param macAddress 标准化的MAC地址
     * @return 丰富后的MAC信息Map
     */
    private Map<String, Object> enrichMacInfo(String macAddress) {
        Map<String, Object> macInfo = new HashMap<>(16);
        
        // 使用MacUtils获取详细信息
        Map<String, Object> detailedInfo = MacUtils.getMacInfo(macAddress);
        macInfo.putAll(detailedInfo);

        // 添加额外的分析信息
        addAnalysisInfo(macAddress, macInfo);

        return macInfo;
    }

    /**
     * 添加MAC地址分析信息
     *
     * @param macAddress MAC地址
     * @param macInfo MAC信息Map
     */
    private void addAnalysisInfo(String macAddress, Map<String, Object> macInfo) {
        try {
            // 获取设备类型推断
            String deviceType = inferDeviceType(macAddress, (String) macInfo.get("vendor"));
            macInfo.put("device_type", deviceType);

            // 添加安全相关信息
            addSecurityInfo(macAddress, macInfo);

        } catch (Exception e) {
            log.warn("添加MAC地址分析信息失败: {}", macAddress, e);
        }
    }

    /**
     * 推断设备类型
     *
     * @param macAddress MAC地址
     * @param vendor 厂商名称
     * @return 设备类型
     */
    private String inferDeviceType(String macAddress, String vendor) {
        if (vendor == null || "Unknown".equals(vendor)) {
            return "Unknown";
        }

        // 基于厂商名称推断设备类型
        String vendorLower = vendor.toLowerCase();
        
        if (vendorLower.contains("apple") || vendorLower.contains("iphone") || vendorLower.contains("ipad")) {
            return "Mobile Device";
        } else if (vendorLower.contains("cisco") || vendorLower.contains("juniper") || vendorLower.contains("huawei")) {
            return "Network Equipment";
        } else if (vendorLower.contains("intel") || vendorLower.contains("realtek") || vendorLower.contains("broadcom")) {
            return "Network Interface";
        } else if (vendorLower.contains("samsung") || vendorLower.contains("xiaomi") || vendorLower.contains("honor")) {
            return "Mobile Device";
        } else if (vendorLower.contains("hp") || vendorLower.contains("dell") || vendorLower.contains("lenovo")) {
            return "Computer";
        } else if (vendorLower.contains("nintendo") || vendorLower.contains("sony")) {
            return "Gaming Device";
        } else if (vendorLower.contains("espressif") || vendorLower.contains("arduino")) {
            return "IoT Device";
        }

        return "Unknown";
    }

    /**
     * 添加安全相关信息
     *
     * @param macAddress MAC地址
     * @param macInfo MAC信息Map
     */
    private void addSecurityInfo(String macAddress, Map<String, Object> macInfo) {
        // 检查是否为随机MAC地址（本地管理地址可能是随机的）
        boolean isLocallyAdministered = (Boolean) macInfo.getOrDefault("is_locally_administered", false);
        boolean isMulticast = (Boolean) macInfo.getOrDefault("is_multicast", false);
        
        // 随机MAC地址通常用于隐私保护
        boolean isPotentiallyRandomized = isLocallyAdministered && !isMulticast;
        macInfo.put("is_potentially_randomized", isPotentiallyRandomized);

        // 检查是否为常见的虚拟化厂商
        String vendor = (String) macInfo.get("vendor");
        boolean isVirtualized = isVirtualizedVendor(vendor);
        macInfo.put("is_virtualized", isVirtualized);
    }

    /**
     * 判断是否为虚拟化厂商
     *
     * @param vendor 厂商名称
     * @return 如果是虚拟化厂商则返回true
     */
    private boolean isVirtualizedVendor(String vendor) {
        if (vendor == null) {
            return false;
        }

        String vendorLower = vendor.toLowerCase();
        return vendorLower.contains("vmware") ||
               vendorLower.contains("virtualbox") ||
               vendorLower.contains("parallels") ||
               vendorLower.contains("microsoft") && vendorLower.contains("virtual") ||
               vendorLower.contains("xen") ||
               vendorLower.contains("qemu") ||
               vendorLower.contains("kvm");
    }

    /**
     * 将丰富化信息添加到Row中
     *
     * @param originalRow 原始Row
     * @param enrichmentInfo 丰富化信息
     * @return 丰富化后的Row
     */
    private Row addEnrichmentToRow(Row originalRow, Map<String, Object> enrichmentInfo) {
        // 创建新的Row，包含原始数据和丰富化信息
        Row enrichedRow = Row.withNames();

        // 复制原始字段
        Set<String> fieldNames = originalRow.getFieldNames(true);
        if (fieldNames != null) {
            for (String fieldName : fieldNames) {
                enrichedRow.setField(fieldName, originalRow.getField(fieldName));
            }
        }

        // 添加丰富化字段（使用前缀避免冲突）
        String prefix = "enriched_";
        for (Map.Entry<String, Object> entry : enrichmentInfo.entrySet()) {
            String fieldName = prefix + entry.getKey();
            enrichedRow.setField(fieldName, entry.getValue());
        }

        return enrichedRow;
    }
}
