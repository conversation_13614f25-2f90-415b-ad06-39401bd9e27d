package com.geeksec.nta.datawarehouse.etl.ods.converter.protocol;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import com.geeksec.common.utils.time.DateTimeUtils;
import com.geeksec.nta.datawarehouse.etl.constant.FieldConstants;
import com.geeksec.nta.datawarehouse.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.datawarehouse.etl.ods.tag.MessageOutputTag;
import com.geeksec.proto.ZMPNMsg;

/**
 * Converter for HTTP protocol messages.
 * Maps HTTP protocol data from protobuf messages to Doris
 * ods_http_protocol_metadata table format.
 * This converter is aligned with the latest ods_http_protocol_metadata table
 * schema.
 *
 * <AUTHOR> Team
 */
public class HttpConverter extends AbstractProtobufMessageConverter {

    @Override
    protected Row convertMessage(ZMPNMsg.JKNmsg msg) {
        ZMPNMsg.http_header_msg httpHeader = msg.getHttpHeader();
        ZMPNMsg.Comm_msg commMsg = httpHeader.getCommMsg();

        // 创建带有命名字段的Row
        Row row = Row.withNames();

        // 设置通用字段
        row.setField(FieldConstants.FIELD_SESSION_ID, commMsg.getSessionId());
        row.setField(FieldConstants.FIELD_SRC_IP, commMsg.getSrcIp());
        row.setField(FieldConstants.FIELD_DST_IP, commMsg.getDstIp());
        row.setField(FieldConstants.FIELD_SRC_PORT, commMsg.getSrcPort());
        row.setField(FieldConstants.FIELD_DST_PORT, commMsg.getDstPort());
        row.setField(FieldConstants.FIELD_IP_PROTOCOL, commMsg.getIppro());
        row.setField(FieldConstants.FIELD_BEGIN_TIME, DateTimeUtils.fromTimestampSeconds(commMsg.getBeginTime()));
        row.setField(FieldConstants.FIELD_SERVER_IP, commMsg.getServerIp());
        row.setField(FieldConstants.FIELD_APP_ID, commMsg.getAppId());
        row.setField(FieldConstants.FIELD_APP_NAME, commMsg.getAppName());
        row.setField(FieldConstants.FIELD_THREAD_ID, commMsg.getThreadId());
        row.setField(FieldConstants.FIELD_TASK_ID, commMsg.getTaskId());
        row.setField(FieldConstants.FIELD_BEGIN_NSEC, commMsg.getBeginNsec());
        row.setField(FieldConstants.FIELD_BATCH_ID, commMsg.getBatchId());

        // 设置HTTP特定字段
        row.setField(FieldConstants.FIELD_HTTP_URL, httpHeader.getUrl());
        row.setField(FieldConstants.FIELD_HTTP_ACT, httpHeader.getAct());
        row.setField(FieldConstants.FIELD_HTTP_HOST, httpHeader.getHost());
        row.setField(FieldConstants.FIELD_HTTP_RESPONSE, httpHeader.getResponse());
        row.setField(FieldConstants.FIELD_HTTP_C_FINGER, httpHeader.getHttpCFinger());
        row.setField(FieldConstants.FIELD_HTTP_S_FINGER, httpHeader.getHttpSFinger());

        // 设置客户端KV信息
        if (httpHeader.getHttpClientKvCount() > 0) {
            Map<String, Object> clientKv = new HashMap<>(httpHeader.getHttpClientKvCount());
            for (ZMPNMsg.key_val_msg kv : httpHeader.getHttpClientKvList()) {
                clientKv.put(kv.getKey(), kv.getVal());
            }
            row.setField(FieldConstants.FIELD_HTTP_CLIENT_KV, clientKv);
        }

        // 设置服务端KV信息
        if (httpHeader.getHttpServerKvCount() > 0) {
            Map<String, Object> serverKv = new HashMap<>(httpHeader.getHttpServerKvCount());
            for (ZMPNMsg.key_val_msg kv : httpHeader.getHttpServerKvList()) {
                serverKv.put(kv.getKey(), kv.getVal());
            }
            row.setField(FieldConstants.FIELD_HTTP_SERVER_KV, serverKv);
        }

        // 设置客户端标题信息
        if (httpHeader.getHttpClientTitleCount() > 0) {
            List<String> clientTitles = new ArrayList<>(httpHeader.getHttpClientTitleCount());
            clientTitles.addAll(httpHeader.getHttpClientTitleList());
            row.setField(FieldConstants.FIELD_HTTP_CLIENT_TITLE, clientTitles);
        }

        // 设置服务端标题信息
        if (httpHeader.getHttpServerTitleCount() > 0) {
            List<String> serverTitles = new ArrayList<>(httpHeader.getHttpServerTitleCount());
            serverTitles.addAll(httpHeader.getHttpServerTitleList());
            row.setField(FieldConstants.FIELD_HTTP_SERVER_TITLE, serverTitles);
        }

        return row;
    }

    @Override
    public OutputTag<Row> getOutputTag() {
        return MessageOutputTag.HTTP_STREAM;
    }
}
