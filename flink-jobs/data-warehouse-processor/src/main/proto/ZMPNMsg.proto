syntax = "proto2";

package com.geeksec.proto;

message JKNmsg {
  optional int32 type = 1;//消息类型
  optional dns_msg dns = 5;//DNS消息
  optional esp_msg esp = 62;//esp
  optional http_header_msg http_header = 81;//请求响应头，部分载荷数据 如果使用，替换http_comm_msg即可
  optional l2tp_msg l2tp = 27;
  optional ssl_msg ssl = 30;
  optional single_session_msg single_session = 31;
  optional s7_msg s7 = 41;
  optional modbus_msg modbus = 42;
  optional eip_msg eip = 43;
  optional iec104_msg iec104 = 44;
  optional opc_msg opc = 45;
  optional mac_con_msg mac_con = 1000;
  optional mac_con_msg noip_con = 1001;
  optional rdp_msg rdp = 1002;
  optional ssh_msg ssh = 1003;
  optional rlogin_msg rlogin = 1004;
  optional telnet_msg telnet = 1005;
  optional vnc_msg vnc = 1006;
  optional xdmcp_msg xdmcp = 1007;
  optional ntp_msg ntp = 1008;
  optional icmp_msg icmp = 1009;
}

message rdp_msg {
  optional Comm_msg comm_msg = 1;//公共消息内容
  optional rdp_client_msg rdp_client = 2;//rdp请求
  optional rdp_server_msg rdp_server = 3;//rdp响应
  optional rdp_negotiate_msg rdp_negotiate = 4;//协商信息
}

message rdp_client_msg {
  optional uint32 version_major = 1;
  optional uint32 version_minor = 2;
  optional uint32 desktop_width = 3;
  optional uint32 desktop_height = 4;
  optional uint32 color_depth = 5;
  optional uint32 sas_sequence = 6;
  optional uint32 keyboard_layout = 7;
  optional uint32 client_build = 8;
  optional string client_name = 9;
  optional uint32 keyboard_type = 10;
  optional uint32 keyboard_subtype = 11;
  optional uint32 keyboard_funckey = 12;
  optional string ime_filename = 13;
  optional uint32 pb2_colordepth = 14;
  optional uint32 client_productid = 15;
  optional uint32 serial_num = 16;
  optional uint32 high_colordepth = 17;
  optional uint32 support_colordepth = 18;
  optional uint32 early_cflags = 19;
  optional string client_dproductid = 20;
  optional uint32 connection_type = 21;
  optional uint32 pad1octet = 22;
  optional uint32 sselected_protocol = 23;
  optional uint32 cluster_flags = 24;
  optional uint32 redirect_sessionid = 25;
  optional uint32 encryption_methods = 26;
  optional uint32 ext_encrytionmethod = 27;
  optional uint32 channel_count = 28;
  repeated rdp_channeldef channeldef = 29;
  optional uint32 request_protocols = 30;
  optional uint32 rdp_c_flag = 31;
  optional bytes cookie = 32;
}

message rdp_channeldef {
  optional string name = 1;
  optional uint32 option = 2;
}

message rdp_server_msg {
  optional uint32 version_major = 1;
  optional uint32 version_minor = 2;
  optional uint32 mcs_channelid = 3;
  optional uint32 channel_count = 4;
  repeated uint32 channel_id = 5;
  optional uint32 encryption_method = 6;
  optional uint32 encryption_level = 7;
  optional uint32 server_randomlen = 8;
  optional uint32 server_certlen = 9;
  optional bytes server_random = 10;
  optional bytes server_cert = 11;
  optional uint32 selected_protocols = 12;
  optional uint32 rdp_s_flag = 13;
}

message rdp_negotiate_msg {
  repeated string c_flag_protocols = 1;
  repeated string c_requested_protocols = 2;
  repeated string s_flag_protocols = 3;
  repeated string s_selected_protocols = 4;
}

message mac_con_msg {
  optional string mac_a = 1;
  optional string mac_b = 2;
  optional uint32 begin_time = 3;
  optional uint32 end_time = 4;
  optional uint32 pkt_a2b = 5;
  optional uint32 pkt_b2a = 6;
  optional uint32 device_id = 7;
  optional uint32 thread_id = 8;//线程ID
  optional uint32 task_id = 9;//任务ID
  optional uint32 batch_id = 11;//批次ID
}

// 公共字段
message Comm_msg {
  optional string src_ip = 1;//源IP
  optional string dst_ip = 2;//目的IP
  optional uint32 src_port = 3;//源端口
  optional uint32 dst_port = 4;//目的端口
  optional uint32 ippro = 5;//IP协议号
  optional string session_id = 6;// session id
  // optional uint32 session_type = 7;   //[预留]  会话类型： 1: MAC会话 2：IP会话 3：TCP会话 4：UDP会话
  optional uint32 begin_time = 8;//会话起始时间
  // optional string state = 10;         //会话结束状态  1: 自然结束 2：TCP-RST 3：会话超时，结束 4：会话未完成，输出临时数据
  optional string server_ip = 12;//服务器IP       0：未知  1：源IP为服务器IP  2：目的IP为服务器IP
  optional uint32 app_id = 14;//应用ID
  optional string app_name = 15;//应用ID
  // optional uint32 sfocus = 16;        //[预留]  是否重点IP
  // optional uint32 dfocus = 17;        //[预留]  是否重点IP
  optional uint32 thread_id = 18;//线程ID
  optional uint32 task_id = 19;//任务ID
  // optional string src_first_ip = 21;  //多层ip协议时设置 第一层源ip
  // optional string dst_first_ip = 22;  //多层ip协议时设置 第一层目的ip
  optional uint32 begin_nsec = 27;//会话起始时间，在线探针：纳秒数，离线探针：包结构内微秒时间值
  optional uint32 batch_id = 33;//批次ID
}

// http公共字段
message http_comm_msg {
  optional string url = 1;//以下为请求
  optional string referer = 2;//先前网页地址
  optional string accept = 3;//能够接收的内容类型
  optional string accept_language = 4;
  optional string user_agent = 5;//用户代理
  optional string accept_encoding = 6;
  optional string host = 7;
  optional bytes cookie = 8;
  optional string accept_charset = 9;
  optional string via = 10;//经过的网关或代理服务器，使用的通信协议
  optional uint64 content_length = 11;
  optional string content_type = 12;
  optional string x_forwarded_for = 13;//服务器自定义项
  optional string resp_server = 14;// web服务器软件名 响应都以resp_开头
}

message dns_msg {
  optional Comm_msg comm_msg = 1;//公共消息内容
  optional uint32 dns_id = 2;//transactionid
  optional uint32 dns_flags = 3;//flag
  optional uint32 dns_que = 4;//Query数量
  optional uint32 dns_ans = 5;//Answer数量
  optional uint32 dns_auth = 6;//Auth数量
  optional uint32 dns_add = 7;//Add数量
  optional string dns_domain = 8;//本次查询的域名 -- Query中的第一个域名
  optional string dns_domain_ip = 9;//域名IP
  optional string dns_query = 10;//域名IP
  optional string dns_answer = 11;//域名IP
  //optional string auth = 12;        //授权单位 <无需提取>
  //optional string addition = 13;    //附加信息<>
}

//esp
message esp_msg {
  optional Comm_msg comm_msg = 1;//公共消息内容
  optional uint32 protocol_family = 2;//协议名称包括SSL/TLS/SSH/ISAKMP/IKE/L2TP/PPTP/IPSec
  optional double communication_rate = 3;//通信速率
  optional uint32 direction = 4;//"0：不确定  1：c2s  2：s2c  3：双向"
  optional uint32 encapsulation_mode = 5;//封装模式：1-传输模式；2-隧道模式
  optional uint32 esp_spi = 6;//封闭安全载荷（ESP）安全参数索引
  optional uint32 esp_seq = 7;//封闭安全载荷（ESP）序列号
  optional uint32 esp_data_len = 8;//封闭安全载荷（ESP）数据长度
  optional bytes esp_data = 9;//封闭安全载荷（ESP）数据
}

message key_val_msg {
  optional string key = 1;//key
  optional string val = 2;//value
}

//http_header_msg
message http_header_msg {
  optional Comm_msg comm_msg = 1;//公共消息内容
  // optional string http_client = 2;    //请求信息
  // optional string http_server = 3;    //响应信息
  optional string act = 4;//请求动作
  optional string url = 5;
  optional string host = 6;
  optional string response = 7;//应答动作
  optional uint64 http_c_finger = 8;//HTTP请求指纹
  optional uint64 http_s_finger = 9;//HTTP应答指纹
  repeated key_val_msg http_client_kv = 10;// HTTP请求KV提取
  repeated string http_client_title = 11;// HTTP Title数组
  repeated key_val_msg http_server_kv = 12;// HTTP相应KV提取
  repeated string http_server_title = 13;// HTTP Title数组
}

// 加密-l2tp
message l2tp_msg {
  optional Comm_msg comm_msg = 1;//公共消息内容
  optional uint32 protocol_family = 2;//协议编号
  optional double communication_rate = 3;//通信速率
  optional uint32 direction = 4;//数据流方向
  optional uint32 protocol_version = 5;//协议版本
  optional uint32 framing_capabilities = 6;//帧类型
  optional uint32 bearer_capabilities = 7;//接入方式
  optional string server_hostname = 8;//服务器主机名称
  optional string client_hostname = 9;//客户端主机名称
  optional string server_vendorname = 10;//服务器供应商名称
  optional string client_vendorname = 11;//客户端供应商名称
  optional string calling_number = 12;//呼叫方的电话号码
  optional uint32 proxy_authen_type = 13;//代理认证类型
  optional string proxy_authen_name = 14;//代理认证名
  optional uint32 is_negotiate_success = 15;//代理认证名
}//相关字段含义详见文档《加密协议接口》


message ssh_kex_msg {
  optional string protocol = 1;//  版本号
  optional bytes cookie = 2;//  cookie值
  repeated string kex_algorithms = 3;//  支持的密钥交换算法列表
  repeated string server_host_key_algorithms = 4;//  支持的服务器主机密钥算法列表
  repeated string encryption_algorithms_client_to_server = 5;//  支持的C2S加密算法列表
  repeated string encryption_algorithms_server_to_client = 6;//  支持的S2C加密算法列表
  repeated string mac_algorithms_client_to_server = 7;//  支持的C2S消息认证码算法列表
  repeated string mac_algorithms_server_to_client = 8;//  支持的S2C消息认证码算法列表
  repeated string compression_algorithms_client_to_server = 9;//  支持的C2S压缩算法列表
  repeated string compression_algorithms_server_to_client = 10;//  支持的S2C压缩算法列表
}
// 加密-ssh
message ssh_msg {
  optional Comm_msg comm_msg = 1;//  公共字段
  optional ssh_kex_msg client = 2;//  客户端KEX
  optional ssh_kex_msg server = 3;//  服务端KEX

  optional bytes dh_e = 22;//  DH client e
  optional bytes dh_f = 23;//  DH server f
  optional bytes dh_gex_min = 24;//  DH GEX Min
  optional bytes dh_gex_nbits = 25;//  DH GEX Number of Bits
  optional bytes dh_gex_max = 26;//  DH GEX Max
  optional bytes dh_gex_p = 27;//  DH GEX modulus (P)
  optional bytes dh_gex_g = 28;//  DH GEX base (G)
  optional bytes ecdh_q_c = 29;//  ECDH client's ephemeral public key (Q_C)
  optional bytes ecdh_q_s = 30;//  ECDH server's ephemeral public key (Q_S)
  optional string host_key_type = 31;//  Host key type
  optional bytes host_key_rsa_e = 32;//  RSA public exponent (e)
  optional bytes host_key_rsa_n = 33;//  RSA modulus (N)
  optional string host_key_ecdsa_id = 34;//  ECDSA elliptic curve identifier
  optional bytes host_key_ecdsa_q = 35;//  ECDSA public key (Q)
  optional bytes host_key_dsa_p = 36;//  DSA prime modulus (p)
  optional bytes host_key_dsa_q = 37;//  DSA prime divisor (q)
  optional bytes host_key_dsa_g = 38;//  DSA subgroup generator (g)
  optional bytes host_key_dsa_y = 39;//  DSA public key (y)
  optional bytes host_key_eddsa_key = 40;//  EdDSA public key
  optional string kex_h_sig_type = 41;//  KEX H signature type
  optional bytes kex_h_sig = 42;//  KEX H signature
}

// 加密-ssl
message ssl_msg {
  optional Comm_msg comm_msg = 1;
  optional uint32 ssl_version = 2;
  optional uint32 ssl_c_version = 3;
  optional uint32 ssl_hello_c_version = 4;
  optional uint32 ssl_hello_c_time = 5;
  optional bytes ssl_hello_c_random = 6;
  optional bytes ssl_hello_c_sessionid = 7;
  optional uint32 ssl_hello_c_sessionidlen = 8;
  optional bytes ssl_hello_c_ciphersuit = 9;
  optional uint32 ssl_hello_c_ciphersuitnum = 10;
  optional bytes ssl_hello_c_compressionmethod = 11;
  optional uint32 ssl_hello_c_compressionmethodlen = 12;
  optional uint32 ssl_hello_c_extentionnum = 13;
  optional bytes ssl_hello_c_extention = 14;
  optional bytes ssl_hello_c_alpn = 15;
  optional string ssl_hello_c_servername = 16;
  optional uint32 ssl_hello_c_servernametype = 17;
  optional bytes ssl_hello_c_sessionticket = 18;
  optional uint32 ssl_cert_c_num = 19;
  optional bytes ssl_cert_c_hash = 20;
  optional uint32 ssl_hello_s_version = 21;
  optional uint32 ssl_hello_s_time = 22;
  optional bytes ssl_hello_s_random = 23;
  optional bytes ssl_hello_s_sessionid = 24;
  optional uint32 ssl_hello_s_sessionidlen = 25;
  optional bytes ssl_hello_s_cipersuite = 26;
  optional bytes ssl_hello_s_compressionmethod = 27;
  optional uint32 ssl_hello_s_extentionnum = 28;
  optional bytes ssl_hello_s_extention = 29;
  optional bytes ssl_hello_s_alpn = 30;
  optional bytes ssl_hello_s_sessionticket = 31;
  optional uint32 ssl_cert_s_num = 32;
  optional bytes ssl_cert_s_hash = 33;
  optional uint32 ssl_s_newsessionticket_lifetime = 34;
  optional bytes ssl_s_newsessionticket_ticket = 35;
  optional uint32 ssl_s_newsessionticket_ticketlen = 36;
  optional uint32 ssl_c_keyexchangelen = 37;
  optional bytes ssl_c_keyexchange = 38;
  optional uint32 ssl_s_keyexchangelen = 39;
  optional bytes ssl_s_keyexchange = 40;
  optional uint64 ssl_c_finger = 41;//SSL请求指纹
  optional uint64 ssl_s_finger = 42;//SSL应答指纹
}



/*//do not delete this block
// 加密-ssl
message ssl_certificate
{
    optional string certificate_algorithm = 1;                     //  证书公钥算法
    optional uint64 certificate_pubkeylength = 2;                  //  证书公钥长度
    optional bytes certificate_pubkey = 3;                        //  证书公钥
    optional string issuer_countryname = 4;                        //  证书发布者信息（国家）
    optional string issuer_provincename =5;                        //  证书发布者信息（省）
    optional string issuer_localityname =6;                        //  证书发布者信息（地区）
    optional string issuer_organizationname =7;                    //  证书发布者信息（组织名称）
    optional string issuer_organizationalunitname =8;              //  证书发布者信息（组织机构）
    optional string issuer_commonname =9;                          //  证书发布者信息（公共名称）
    optional string issuer_emailaddress=10;                        //  证书发布者信息（邮件地址）
    optional string subject_countryname=11;                        //  证书主题信息(颁发给)（国家）
    optional string subject_provincename=12;                       //  证书主题信息(颁发给)（省）
    optional string subject_localityname=13;                       //  证书主题信息(颁发给)（地区）
    optional string subject_organizationname=14;                   //  证书主题信息(颁发给)（组织）
    optional string subject_organizationalunitname=15;             //  证书主题信息(颁发给)（机构）
    optional string subject_commonname=16;                         //  证书主题信息(颁发给)（公共名称）
    optional string subject_emailaddress=17;                       //  证书主题信息(颁发给)（邮件地址）
    optional bytes certificate_serialnumber=18;                   //  证书序列号
    optional string notbefore=19;                                  //  证书有效期起始时间
    optional string notafter=20;                                   //  证书有效期终止时间
    optional string kusage=21;                                     //  密钥用法
    optional string exkusage=22;                                   //  扩展密钥
    optional string dns_name=23;                                   //  域名信息
}
*/

message rule_msg {
  repeated uint32 connect_rule = 1;
  required uint32 connect_rule_num = 2;
  required uint32 level = 3;
}

//
message single_http {
  optional string url = 1;
  optional string act = 2;
  optional string host = 3;
  optional string response = 4;
  optional string user_agent = 5;
}

message single_dns_answer {
  optional string name = 1;
  optional string value = 2;
}

message single_dns {

  optional string domain = 1;
  optional string domain_ip = 2;
  repeated single_dns_answer answer = 3;
}

message single_ssl {
  optional bytes ch_ciphersuit = 1;
  optional int32 ch_ciphersuit_num = 2;
  optional string ch_server_name = 3;
  optional bytes ch_alpn = 4;
  optional bytes c_cert = 5;
  optional int32 c_cert_num = 6;
  optional bytes s_cert = 7;
  optional int32 s_cert_num = 8;
}

//会话基础信息
message ss_basic_msg {
  optional string smac = 1;//Mac地址
  optional string dmac = 2;//Mac地址
  optional rule_msg rule = 3;//
  optional uint32 rule_num = 4;//
  optional uint32 rule_level = 5;//
  optional bytes syn = 6;//二进制流
  required bytes syn_ack = 7;//二进制流
}

//会话统计信息
message ss_stats_msg {
  optional uint32 stats_stotalsign = 1;//标识
  optional uint32 stats_dtotalsign = 2;//标识
  optional string stats_distbytes = 3;//负载字节分布
  optional uint64 stats_distbytesnum = 4;//
  optional double stats_distcsq = 5;//统计
  optional double stats_distcsqt = 6;//统计的t值
  repeated uint32 stats_sdistlen = 7;//包负载长度分布  -- 数组空间固定为8、16、32、64、128、256、512、1024
  repeated uint32 stats_ddistlen = 8;//
  optional uint32 stats_sdistdur = 9;//[预留] 包时间间隔分布数组空间10      秒： 0x10、0x1     纳秒：0x80000000、0x40000000、0x20000000、0x10000000、0x8000000、0x4000000、0x2000000、0x1000000
  optional uint32 stats_ddistdur = 10;//[预留]
  optional uint32 stats_prolist_num = 11;//包协议栈数量
  optional string stats_prolist = 12;//包协议栈  会话中出现的最长协议栈  为了支持多层IP的显示
  optional uint32 sio_sign = 13;// 源ip是否为 内部IP
  optional uint32 dio_sign = 14;// 目标IP为内部IP
  optional string ext_json = 15;// json 扩展字段
  optional uint32 stats_src_mss = 16;
  optional uint32 stats_dst_mss = 17;
  optional uint32 stats_src_window_scale = 18;//tcp option window scale
  optional uint32 stats_dst_window_scale = 19;//tcp option window scale
  optional uint32 stats_spayload_maxlen = 20;
  optional uint32 stats_dpayload_maxlen = 21;
  optional uint32 stats_sack_payload_maxlen = 22;
  optional uint32 stats_dack_payload_maxlen = 23;
  optional uint32 stats_sack_payload_minlen = 24;
  optional uint32 stats_dack_payload_minlen = 25;
  repeated md_tcp_msg stats_tcp_info = 26;
  repeated uint32 stats_distdur = 27;//包时间间隔分布数组 不区分方向 [0,1) 、[1,2)、[2,4)、[4,8)、[8,16)、[16,32)、[32,64)、[64,以上
  repeated uint32 syn_seq = 28;
  optional uint32 syn_seq_num = 29;
  repeated uint32 stats_sipid_offset = 30;//IP的ID偏移    -64,-4,0,4,64
  repeated uint32 stats_dipid_offset = 31;//IP的ID偏移    -64,-4,0,4,64
  repeated uint32 block_cipher = 32;//SSL会话中，ConnectType=23时，统计Length % 8的取值，统计前255个块；预留： 8字节数组字段
}

message md_tcp_msg {
  optional int64 bytes = 1;
  optional uint32 packet_num = 2;
  optional uint32 psh_num = 3;
  optional uint32 acknowledgement = 4;
  optional uint32 min_sequence = 5;
  optional uint32 max_sequence = 6;
}

message packet_info_msg {
  optional uint32 count = 1;
  required uint32 sec = 6;
  required uint32 nsec = 10;
  optional int32 len = 12;
}

//会话包信息
message ss_pkt_msg {
  optional uint32 pkt_smaxlen = 1;//最大IP包长
  optional uint32 pkt_dmaxlen = 2;//
  optional uint32 pkt_snum = 3;//包数
  optional uint32 pkt_dnum = 4;//
  optional uint32 pkt_spayloadnum = 5;//有负载的包数
  optional uint32 pkt_dpayloadnum = 6;//
  optional uint64 pkt_sbytes = 7;//字节数
  optional uint64 pkt_dbytes = 8;//
  optional uint64 pkt_spayloadbytes = 9;//有负载的字节数
  optional uint64 pkt_dpayloadbytes = 10;//
  optional uint32 pkt_sfinnum = 11;//Fin包数量
  optional uint32 pkt_dfinnum = 12;//
  optional uint32 pkt_srstnum = 13;//Rst包数量
  optional uint32 pkt_drstnum = 14;//
  optional uint32 pkt_ssynnum = 15;//Syn包数量
  optional uint32 pkt_dsynnum = 16;//
  optional uint32 pkt_ssynbytes = 17;//Syn包字节数
  optional uint32 pkt_dsynbytes = 18;//
  optional uint32 pkt_sttlmax = 19;//IP:TTL最大值
  optional uint32 pkt_dttlmax = 20;//
  optional uint32 pkt_sttlmin = 21;//IIP：TTL最小值
  optional uint32 pkt_dttlmin = 22;//
  optional uint32 pkt_sdurmax = 23;//[预留]  包的最大时间间隔
  optional uint32 pkt_ddurmax = 24;//[预留]
  optional uint32 pkt_sdurmin = 25;//[预留]  包的最小时间间隔
  optional uint32 pkt_ddurmin = 26;//[预留]
  optional uint32 pkt_sdisorder = 27;//乱序包数
  optional uint32 pkt_ddisorder = 28;//
  optional uint32 pkt_sresend = 29;//重发包数
  optional uint32 pkt_dresend = 30;//
  optional uint32 pkt_slost = 31;//丢包长度
  optional uint32 pkt_dlost = 32;//
  optional uint32 pkt_spshnum = 33;//
  optional uint32 pkt_dpshnum = 34;//
  optional uint32 pkt_pronum = 35;//
  optional uint32 pkt_unkonw_pronum = 36;//
  repeated packet_info_msg pkt_infor = 37;//包信息
  optional uint32 pkt_syn_data = 38;//syn包包含负载包数
  optional uint32 pkt_sbadnum = 39;//错包数量
  optional uint32 pkt_dbadnum = 40;//
  optional uint32 app_pkt_id = 41;//第几个负载包识别到应用
  repeated bytes pkt_spayload = 42;//包负载客户端前4个
  repeated bytes pkt_dpayload = 43;//包负载服务端前4个
}

message tcp_finger_feature_msg {
  required bool ecn_ip_ect = 1;//表示显示拥塞通知所使用的 IP 协议保留字段后两位中 ECT 标志位的值。该特征直接来源于该标志位提取的值。
  required bool qk_dfnz_ipid = 2;//表示 DF 设置为 1 时 IPID 值是否不为 0。 Mac OS X 10.13 和 10.14 以及 iOS 12 等较新的操作系统在不分片时一直将 ID 设置为 0。该特征需要对DF 标志位和 ID 值进行判断后得到结果。
  required bool flag_cwr = 3;//表示在 TCP 协议 ECN 标志位 CWR 的值。
  required bool flag_ece = 4;//表示在 TCP 协议 ECN 标志位 ECE的值。
  required bool qk_opt_zero_ts1 = 5;//表示TCP选项时间戳前四位是否为0。
  required uint32 ttl = 6;//表示 IP 协议 TTL 字段的值。在原始发送主机上捕获的数据包 TTL 值直接从字段中提取，而其他位置捕获的数据包需要通过向上就近原则计算出原始 TTL 值。
  required uint32 tcpopt_eol_padnum = 7;//表示在 TCP 选项 EOL 后填充字节的数量。该特征项需要通过计算TCP 选项 EOL 后面的填充字节数量得到值。
  required uint32 tcpopt_wscale = 8;//表示 TCP 选项中窗口扩大因子的值。该特征直接从 TCP 选项中提取窗口扩大因子字段的值。
  required uint32 qk_win_mss = 9;//表示 TCP 协议窗口值与 TCP 选项最大报文段长度的比值，如不能整除则该特征项值直接为窗口值。没有直接使用窗口大小和最大报文段长度作为特征项是由于在真实网络环境中的主机这两个字段的值种类非常多，使用比值更具有通用性。
  required string tcpopt_layout = 10;//表示 TCP 选项的每一项按出现的先后顺序排序的字符串值。常见的TCP 选项有选项结束（EOL）、无操作（NOP）、最大报文段长度（MSS）、窗口扩大因子（Window Scale）、选择性确认（SACK Permitted）和时间戳（Timestamps）等。该特征需要对所有的 TCP 选项进行按先后顺序排序后得到值。
}

//单流统计
message single_session_msg {
  required Comm_msg comm_msg = 1;//公共消息内容
  optional ss_basic_msg ss_basic = 2;//会话基础信息
  optional ss_stats_msg ss_stats = 3;//会话统计信息
  optional ss_pkt_msg ss_pkt = 4;//会话包信息
  optional uint64 tcp_c_finger = 5;//tcp client fingerprint
  optional uint64 tcp_s_finger = 6;//tcp server fingerprint
  optional uint64 http_c_finger = 7;//HTTP请求指纹
  optional uint64 http_s_finger = 8;//HTTP应答指纹
  optional uint64 ssl_c_finger = 9;//SSL请求指纹
  optional uint64 ssl_s_finger = 10;//SSL应答指纹
  optional tcp_finger_feature_msg tcp_c_finger_feature = 11;//TCP client 指纹元数据
  optional tcp_finger_feature_msg tcp_s_finger_feature = 12;//TCP server 指纹元数据
  repeated uint32 port_list = 13;
  repeated single_http http = 14;// HTTP 协议元数据
  repeated single_dns dns = 15;// dns 协议元数据
  repeated single_ssl ssl = 16;//  SSL  协议元数据
  optional uint32 end_time = 20;//会话结束时间  末包时间,若会话未结束使用末包时间
  optional string first_sender = 21;//首包的发送发IP    0：未知 1：源IP发送首包 2：目的IP发送首包
  optional uint32 duration = 22;//会话持续时间
  optional uint32 device_id = 23;//设备码
  optional uint32 first_proto = 24;//首层协议
  optional string proxy_ip = 25;//代理IP
  optional uint32 proxy_port = 26;//代理端口
  optional uint32 end_nsec = 28;//会话结束时间，在线探针：纳秒数，离线探针：包结构内微秒时间值
  optional string proxy_real_host = 29;//代理访问的真实域名
  optional uint32 proxy_type = 30;//0:无代理 1:未知代理 2:HTTP CONNECT IP 3:HTTP CONNECT DOMAIN 4:HTTP代理
  optional uint32 handle_begin_time = 31;//开始处理时间
  optional uint32 handle_end_time = 32;//结束处理时间
  repeated uint32 rule_labels = 34;//规则标签
}

//S7 protocol message system_group_function
message s7_msg {
  optional Comm_msg comm_msg = 1;
  optional uint32 tpkt_version = 2;
  optional uint32 cotp_type = 3;
  optional uint32 s7_type = 4;
  optional uint32 s7_function = 5;
  optional uint32 system_type = 6;
  optional uint32 system_group_function = 7;
  optional uint32 system_sub_function = 8;
  optional uint32 dst_ref = 9;
  optional uint32 src_ref = 10;
  optional uint32 pdu_size = 11;
  optional uint32 src_connect_type = 12;
  optional uint32 src_rack = 13;
  optional uint32 src_slot = 14;
  optional uint32 dst_connect_type = 15;
  optional uint32 dst_rack = 16;
  optional uint32 dst_slot = 17;
  optional uint32 packet_c2s = 18;
}

//modbus protocol message
message modbus_msg {
  optional Comm_msg comm_msg = 1;
  optional uint32 trans_id = 2;
  optional uint32 protocol_id = 3;
  optional uint32 slave_id = 4;
  optional uint32 func_code = 5;
  optional uint32 packet_c2s = 6;

}

//modbus protocol message
message eip_msg {
  optional Comm_msg comm_msg = 1;
  optional uint32 trans_id = 2;
  optional uint32 protocol_id = 3;
  optional uint32 slave_id = 4;
  optional uint32 func_code = 5;

}

//modbus protocol message
message iec104_msg {
  optional Comm_msg comm_msg = 1;
  optional uint32 trans_id = 2;
  optional uint32 protocol_id = 3;
  optional uint32 slave_id = 4;
  optional uint32 func_code = 5;

}

//modbus protocol message
message opc_msg {
  optional Comm_msg comm_msg = 1;
  optional uint32 trans_id = 2;
  optional uint32 protocol_id = 3;
  optional uint32 slave_id = 4;
  optional uint32 func_code = 5;

}

message rlogin_userinfo_msg {
  optional string client_username = 1;
  optional string server_username = 2;
  optional string passwd = 3;
}

message rlogin_wininfo_msg {
  optional uint32 magic_cookie = 1;
  optional uint32 winsize_marker = 2;
  optional uint32 rows = 3;
  optional uint32 columns = 4;
  optional uint32 x_pixels = 5;
  optional uint32 y_pixels = 6;
  optional string term_type = 7;
  optional string term_speed = 8;
}

message rlogin_msg {
  optional Comm_msg comm_msg = 1;//公共消息内容
  optional rlogin_userinfo_msg userinfo = 2;
  optional rlogin_wininfo_msg wininfo = 3;

}

message telnet_terminfo_msg {
  optional uint32 term_width = 1;
  optional uint32 term_height = 2;
  optional string term_speed = 3;
  optional string term_type = 4;
}

message telnet_userinfo_msg {
  optional string username = 1;
  optional string passwd = 2;
}

message telnet_msg {
  optional Comm_msg comm_msg = 1;//公共消息内容
  optional telnet_terminfo_msg terminfo = 2;
  optional telnet_userinfo_msg userinfo = 3;
}

message vnc_sever_fb_msg { // fb -> framebuffer
  optional uint32 fb_width = 1;
  optional uint32 fb_height = 2;
  optional uint32 bits_per_pixel = 3;
  optional uint32 depth = 4;
  optional uint32 big_endian_flag = 5;
  optional uint32 true_color_flag = 6;
  optional uint32 red_maximum = 7;
  optional uint32 green_maximum = 8;
  optional uint32 blue_maximum = 9;
  optional uint32 red_shift = 10;
  optional uint32 green_shift = 11;
  optional uint32 blue_shift = 12;
  optional string desktop_name = 13;

}

message vnc_client_set_encoding_msg {
  repeated string encoding_types = 1;
}

message vnc_client_set_pixel_format_msg {
  optional uint32 bits_per_pixel = 1;
  optional uint32 depth = 2;
  optional uint32 big_endian_flag = 3;
  optional uint32 true_color_flag = 4;
  optional uint32 red_maximum = 5;
  optional uint32 green_maximum = 6;
  optional uint32 blue_maximum = 7;
  optional uint32 red_shift = 8;
  optional uint32 green_shift = 9;
  optional uint32 blue_shift = 10;

}

message vnc_msg {
  optional Comm_msg comm_msg = 1;//公共消息内容
  optional vnc_sever_fb_msg server_fb = 2;
  optional vnc_client_set_encoding_msg encoding = 3;
  optional vnc_client_set_pixel_format_msg pixel_format = 4;
  optional string server_protocol_ver = 5;
  optional string client_protocol_ver = 6;
  repeated string server_secure_type_supported = 7;
  optional string client_secure_type_selected = 8;
  optional bytes server_authentication_challenge = 9;
  optional bytes client_authentication_response = 10;
  optional string authentication_result = 11;
  optional uint32 share_dsktp_flag = 12;
}

message xdmcp_connection_msg {
  optional string type = 1;
  optional string addr = 2;
}

message xdmcp_auth_msg {
  repeated string authentication_names = 1;
  optional bytes authentication_data = 2;
  repeated string authorization_names = 3;
  optional bytes authorization_data = 4;
}

message xdmcp_msg {
  optional Comm_msg comm_msg = 1;//公共消息内容
  optional uint32 version = 2;
  optional uint32 display_number = 3;
  required uint32 session_id = 4;
  optional string hostname = 5;
  repeated xdmcp_connection_msg connections = 6;
  optional xdmcp_auth_msg client_auth = 7;
  optional xdmcp_auth_msg server_auth = 8;
  optional string manufacture_disp_id = 9;
  optional string display_class = 10;
  optional string status = 11;
}

message ntp_client_msg {
  optional int32 stratum = 1;// 跳数（从客户端到服务器的服务器数量）
  optional int32 poll_interval_sec = 2;
  optional double clock_precision = 3;// 精度，表示本地时钟的精度
  optional double root_delay = 4;// 指向根延迟，即从客户端到 NTP 服务器的总延迟
  optional double root_dispersion = 5;// 根分散，表示时间同步的精度
  optional string reference_identifier = 6;// 引用ID，标识用于时间校准的参考时钟的ID
  optional uint32 refer_ts_sec = 7;// 参考时钟的时间戳
  optional uint32 refer_ts_nsec = 8;// 参考时钟的时间戳
  optional uint32 origin_ts_sec = 9;// 原始时间戳，即发送者发送消息时的时间
  optional uint32 origin_ts_nsec = 10;// 原始时间戳，即发送者发送消息时的时间
  optional uint32 recv_ts_sec = 11;// 接收时间戳，即接收者接收消息的时间
  optional uint32 recv_ts_nsec = 12;// 接收时间戳，即接收者接收消息的时间
  optional uint32 xmit_ts_sec = 13;// 传输时间戳，即发送者发送响应的时间
  optional uint32 xmit_ts_nsec = 14;// 传输时间戳，即发送者发送响应的时间
  //optional string private_data = 99; // 私有字段，用于特定实现的额外数据
}

message ntp_server_msg {
  optional int32 stratum = 1;// 跳数（从客户端到服务器的服务器数量）
  optional int32 poll_interval_sec = 2;
  optional double clock_precision = 3;// 精度，表示本地时钟的精度
  optional double root_delay = 4;// 指向根延迟，即从客户端到 NTP 服务器的总延迟
  optional double root_dispersion = 5;// 根分散，表示时间同步的精度
  optional string reference_identifier = 6;// 引用ID，标识用于时间校准的参考时钟的ID
  optional uint32 refer_ts_sec = 7;// 参考时钟的时间戳
  optional uint32 refer_ts_nsec = 8;// 参考时钟的时间戳
  optional uint32 origin_ts_sec = 9;// 原始时间戳，即发送者发送消息时的时间
  optional uint32 origin_ts_nsec = 10;// 原始时间戳，即发送者发送消息时的时间
  optional uint32 recv_ts_sec = 11;// 接收时间戳，即接收者接收消息的时间
  optional uint32 recv_ts_nsec = 12;// 接收时间戳，即接收者接收消息的时间
  optional uint32 xmit_ts_sec = 13;// 传输时间戳，即发送者发送响应的时间
  optional uint32 xmit_ts_nsec = 14;// 传输时间戳，即发送者发送响应的时间
  //optional string private_data = 99; // 私有字段，用于特定实现的额外数据
}


message ntp_msg {
  optional Comm_msg comm_msg = 1;//公共消息内容
  optional int32 version = 2;// NTP 协议版本号
  optional ntp_client_msg client_msg = 3;
  optional ntp_server_msg server_msg = 4;
}

message icmp_msg {
  optional Comm_msg comm_msg = 1;// 公共消息内容
  optional uint32 msg_type = 2;// 消息类型
  optional uint32 info_code = 3;// 信息代码
  optional uint32 echo_seq_num = 4;// 反射数据包序列号
  optional bytes data_con = 5;// 数据内容
  optional string unr_src_addr = 6;// 不可达源IP地址
  optional string unr_dst_addr = 7;// 不可达目的IP地址
  optional uint32 unr_prot = 8;// 不可达协议号
  optional uint32 unc_ttl = 9;// 不可达跳数
  optional uint32 ver = 10;// ICMP协议版本号
  optional uint64 orig_time_stamp = 11;// 发送时间戳（单位：毫秒）
  optional uint64 recv_time_stamp = 12;// 接收时间戳（单位：毫秒）
  optional uint64 trans_time_stamp = 13;// 传送时间戳（单位：毫秒）
  optional uint32 mask = 14;// 网络掩码
  optional uint32 sub_net_id = 15;// 子网络号
  optional uint32 rtr_time_out = 16;// 通告地址的有效时间（单位：秒）
  optional string exc_src_addr = 17;// 导致发生异常消息的报文的源ip地址，异常消息：目的不可达、超时时间
  optional string exc_dst_addr = 18;// 导致发生异常消息的报文的目的ip地址，异常消息：目的不可达、超时时间
  optional uint32 exc_prot = 19;// 导致发生异常消息的报文的协议号，异常消息：目的不可达、超时时间
  optional uint32 exc_src_port = 20;// 导致发生异常消息的报文的源ip端口，异常消息：目的不可达、超时时间
  optional uint32 exc_dst_port = 21;// 导致发生异常消息的报文的目的ip端口，异常消息：目的不可达、超时时间
  optional string gw_addr = 22;// 重路由网关gateway
  optional uint32 ttl = 23;// 生存时间
  optional uint32 rep_ttl = 24;// 响应生存时间
  optional uint32 qur_type = 25;// 查询类型
  optional string qur_ipv6_addr = 26;// 查询的IPV6地址
  optional string qur_ipv4_addr = 27;// 查询的IPV4地址
  optional string qur_dns = 28;// 查询的DNS
  optional uint32 ndp_life_time = 29;// 作为默认路由器时的生存期,从Router
  // lifetime 提取，如：1800 （单位：秒）
  optional string ndp_link_addr = 30;// NDP(邻居发现协议)重路由网关的链路地址
  optional uint32 ndp_pre_len = 31;// NDP(邻居发现协议)前缀长度,从Prefix length 获取，如：64
  optional string ndp_pre_fix = 32;// NDP(邻居发现协议)前缀，从 Prefix 获取，如：3ffe:507:0:1::
  optional uint32 ndp_val_life_time = 33;// NDP(邻居发现协议)前缀有效生存期,从Valid Lifetime 获取，如:3600000
  optional uint32 ndp_cur_mtu = 34;// NDP(邻居发现协议)当前链路的MTU大小，从MTU获取，如：1500
  optional string ndp_tar_addr = 35;// 邻居目的地址
  optional uint32 next_hop_mtu = 36;// 下一跳链路的MTU大小，从MTU获取，如：65000
  optional uint32 exc_pointer = 37;// 参数错误的位置（单位：字节）,标识出报文中出现错误地方的8位片偏移量。
  optional string mul_cast_addr = 38;// 组播组地址，从 Multicast Address 获取，如：ff02::1:ff0e:4c67
  optional uint32 check_sum = 39;// icmp层数据校验和
  optional uint32 check_sum_reply = 40;// icmp层响应数据校验和
  optional uint32 rtraddr = 41;// 路由器地址，多个值时以逗号分隔
  optional uint64 res_time = 42;// 响应时间
  optional uint32 exc_ttl = 43;// 不可达跳数
  optional uint64 response_time = 44;// ICMP协议的响应时间间隔，微秒级
  optional uint32 unreachable_source_port = 45;// ICMP的不可达源端口
  optional uint32 unreachable_destination_port = 46;// ICMP的不可达目的端口
}

