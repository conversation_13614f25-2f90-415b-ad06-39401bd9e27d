package com.geeksec.analysisFunction.infoSink.pbSink;

import static com.geeksec.nta.pipeline.ThreatDetectionPipeline.ALERT_LOG_PROPERTIES;
import static com.geeksec.nta.pipeline.ThreatDetectionPipeline.PARALLELISM_4;

import java.io.IOException;
import java.util.Collections;
import java.util.Properties;

import org.apache.commons.lang3.SerializationException;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.flinkTool.serializer.AlertLogKeySerializationSchema;
import com.geeksec.flinkTool.serializer.AlertLogValueSerializationSchema;
import com.geeksec.proto.AlertLog;
import com.twitter.chill.protobuf.ProtobufSerializer;

/**
 * <AUTHOR>
 * @Date 2024/11/25
 */

public class PbKafkaSink {

    private static final Logger logger = LoggerFactory.getLogger(PbKafkaSink.class);

    public static final String BOOTSTRAP_SERVERS = ALERT_LOG_PROPERTIES.getOrDefault("host","kafka").toString()
            +":"+ALERT_LOG_PROPERTIES.getOrDefault("port",9094).toString();
    public static final String ALERT_TOPIC = ALERT_LOG_PROPERTIES.getOrDefault("topic","alert_log").toString();
    public static final String DEVICE_IP = ALERT_LOG_PROPERTIES.getOrDefault("device.ip","***********").toString();


    public static void pbAlarmKafkaSink(DataStream<JSONObject> alarmJsonStream) {
        // 创建 Kafka Producer 配置
        Properties properties = new Properties();
        properties.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS);
        // 优化参数
        properties.put(ProducerConfig.BATCH_SIZE_CONFIG, 409600);
        properties.put(ProducerConfig.LINGER_MS_CONFIG, 300);
        properties.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 256 * 1024 * 1024);
        properties.put(ProducerConfig.MAX_REQUEST_SIZE_CONFIG, 10 * 1024 * 1024);
        logger.info("当前alert log 外发 kafka地址:{}", BOOTSTRAP_SERVERS);

        // 根据告警信息提取 ALERT_LOG
        SingleOutputStreamOperator<AlertLog.ALERT_LOG> protoAlertInfoStream = alarmJsonStream
                .flatMap(new alertInfoMapFunction()).name("告警转化为 ALERT_LOG").setParallelism(1);

        // 告警数据写入到Kafka
        KafkaSink<AlertLog.ALERT_LOG> kafkaJsonSink = KafkaSink.<AlertLog.ALERT_LOG>builder()
                .setBootstrapServers(properties.getProperty("bootstrap.servers"))
                .setRecordSerializer(KafkaRecordSerializationSchema.<AlertLog.ALERT_LOG>builder()
                        .setTopic(ALERT_TOPIC)
                        .setKeySerializationSchema(new AlertLogKeySerializationSchema())
                        .setValueSerializationSchema(new AlertLogValueSerializationSchema())
                        .build()
                )
                .build();

        protoAlertInfoStream.sinkTo(kafkaJsonSink).name("Alert Log Sink").setParallelism(PARALLELISM_4);
    }

    public static void main(String[] args) throws Exception {
        // 创建执行环境
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        // 创建 Kafka Consumer 配置
        Properties properties = new Properties();
        properties.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS);
        properties.put(ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG, "60000");
        properties.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, "5000");
        env.getConfig().registerTypeWithKryoSerializer(AlertLog.ALERT_LOG.class, ProtobufSerializer.class);

        // 创建 Kafka Source，指定主题名称为 ALERT_TOPIC
        KafkaSource<AlertLog.ALERT_LOG> kafkaSource = KafkaSource.<AlertLog.ALERT_LOG>builder()
                .setBootstrapServers(properties.getProperty(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG))
                .setTopics(Collections.singletonList(ALERT_TOPIC))
                // 从最新的偏移量开始消费
                .setStartingOffsets(OffsetsInitializer.latest())
                .setDeserializer(new KafkaRecordDeserializationSchema<AlertLog.ALERT_LOG>() {
                    @Override
                    public void deserialize(ConsumerRecord<byte[], byte[]> consumerRecord, Collector<AlertLog.ALERT_LOG> collector) throws IOException {
                        if (consumerRecord.value() == null) {
                            logger.info("值为空");
                        } else {
                            try {
                                AlertLog.ALERT_LOG alertLog = AlertLog.ALERT_LOG.parseFrom(consumerRecord.value());
                                collector.collect(alertLog);
                            } catch (Exception e) {
                                throw new SerializationException("Error when deserializing Protobuf message: " + e);
                            }
                        }
                    }

                    @Override
                    public TypeInformation<AlertLog.ALERT_LOG> getProducedType() {
                        return TypeInformation.of(new TypeHint<AlertLog.ALERT_LOG>(){});
                    }
                })
                .setProperties(properties)
                .build();

        // 创建数据流
        env.fromSource(kafkaSource, WatermarkStrategy.noWatermarks(), "Kafka-Source")
                .map(protobufMessage -> {
                    // 处理反序列化后的Protobuf消息
                    return protobufMessage;
                })
                .print();

        // 执行 Flink 作业
        env.execute("Kafka Protobuf Consumer");
    }
}
