package com.geeksec.analysisFunction.getFeature;

import com.geeksec.analysisFunction.getPbMapInfo.DnsInfoMapFlatMapFunction;
import java.math.BigDecimal;
import java.util.*;
import org.apache.commons.math3.stat.descriptive.DescriptiveStatistics;
import org.apache.flink.types.Row;

/**
 * <AUTHOR>
 * @Date 2022/12/15
 */

public class get_dns_tunnel_features {
    public static Map<String,Object> get_dns_tunnel_features(Row dns_info_row){

        Map<String,Object> dns_features = new HashMap<>();
        //key
        dns_features.put("key",dns_info_row.getField(1));
        //cnt：
        dns_features.put("cnt",dns_info_row.getField(10));
        //NX_percent：
        dns_features.put("NX_percent",get_NX_percent(dns_info_row));
        //distinct_que_type_cnt：que_A_percent：que_AAAA_percent：que_CNAME_percent：
        //que_MX_percent：que_NSEC_percent：que_PTR_percent：que_TXT_percent：que_NS_percent：
        dns_features.putAll(get_que_info(dns_info_row));
        //ttl_avg：
        dns_features.put("ttl_avg",get_ttl_avg(dns_info_row));
        //pre_entropy_mean：pre_cnt：pre_entropy：domain_session_std：
        dns_features.putAll(get_domain_info(dns_info_row));
        //ans_entropy_mean：ans_entropy：ans_session_std：
        dns_features.putAll(get_ans_value_info(dns_info_row));
        //start_time：end_time：
        Map<String,Integer> time_info = (Map<String, Integer>) dns_info_row.getField(11);
        dns_features.put("start_time",time_info.get("min_Start_time"));
        dns_features.put("end_time",time_info.get("Max_Start_time"));
        return dns_features;
    }

    private static Double get_NX_percent(Row dns_info_row){
        List<Integer> Domain_IP = dns_info_row.getFieldAs(9);
        int totalCount = Domain_IP.get(1);
        if (totalCount==0){
            return new BigDecimal (-1).doubleValue();
        }
        int Nx_cnt = Domain_IP.get(0);
        return new BigDecimal(Nx_cnt).divide(new BigDecimal(totalCount),6, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    private static Map<String,Double> get_que_info(Row dns_info_row){
        Map<String,Double> que_info_map = new HashMap<>();
        Map<Object,Integer> que_type = dns_info_row.getFieldAs(4);
        Map<Object,Integer> que_type_String = new HashMap<>();
        for (Object que_int:que_type.keySet()){
            que_type_String.put(que_int.toString(),que_type.get(que_int));
        }
        int totalCount = que_type_String.values().stream().mapToInt(Integer::intValue).sum();
        que_info_map.put("distinct_que_type_cnt",new BigDecimal(totalCount).doubleValue());
        for (String que_int: DnsInfoMapFlatMapFunction.QUERY_TYPE_MAP.keySet()){
            int cnt = 0;
            if (que_type_String.containsKey(que_int)){
                cnt = que_type_String.get(que_int);
            }
            String que_name = "que_"+ DnsInfoMapFlatMapFunction.QUERY_TYPE_MAP.get(que_int)+"_percent";
            BigDecimal que_percent = new BigDecimal(cnt).divide(new BigDecimal(totalCount),6, BigDecimal.ROUND_HALF_UP);
            que_info_map.put(que_name,que_percent.doubleValue());
        }
        return que_info_map;
    }

    private static Double get_ttl_avg(Row dns_info_row){
        List<Integer> ttl_info = dns_info_row.getFieldAs(8);
        BigDecimal ttl_avg = new BigDecimal(-1);
        if (ttl_info.get(1)>=1){
            int totalTtl = ttl_info.get(0);
            int totalNum = ttl_info.get(1);
            ttl_avg = new BigDecimal(totalTtl).divide(new BigDecimal(totalNum),6, BigDecimal.ROUND_HALF_UP);
        }
        return ttl_avg.doubleValue();
    }

    private static Map<String,Double> get_domain_info(Row dns_info_row){
        Map<String,Double> domain_info_map = new HashMap<>();
        Map<Object,Integer> domain_info = dns_info_row.getFieldAs(3);
//        Map<String,Map<String,Object>> domain_features = new HashMap<>();//域名完整特征
        Set<String> prefix_set = new HashSet<>();
        DescriptiveStatistics pre_entropy = new DescriptiveStatistics();
        List<String> domain_List = new ArrayList<>();
        for (Object domain:domain_info.keySet()){
            domain_List.add(domain.toString());
        }
        for (String domain:domain_List){
            Map<String,Object> domain_feature = get_domain_feature(domain);
            prefix_set.add((String) domain_feature.get("prefix"));
            pre_entropy.addValue((Double) domain_feature.get("pre_entropy"));
        }
        //domain_session_std
        Collection<Integer> cnt_list = domain_info.values();
        DescriptiveStatistics cnt_stats = new DescriptiveStatistics();
        for (Integer cnt:cnt_list){
            cnt_stats.addValue(cnt);
        }
        Double cnt_std = cnt_stats.getStandardDeviation();
        //pre_entropy_mean
        Double pre_entropy_mean = pre_entropy.getMean();
        //pre_cnt
        Double pre_cnt = Double.valueOf(prefix_set.size());
        //pre_entropy
        String all_prefix = String.join(".", prefix_set);
        Double pre_entropy_all = get_entropy_of_str(all_prefix);

        //一起传入
        domain_info_map.put("domain_session_std",cnt_std);
        domain_info_map.put("pre_entropy_mean",pre_entropy_mean);
        domain_info_map.put("pre_cnt",pre_cnt);
        domain_info_map.put("pre_entropy",pre_entropy_all);

        return domain_info_map;
    }

    private static Map<String,Object> get_domain_feature(String domain){
        Map<String,Object> domain_feature = new HashMap<>();
//        String tail = "";
//        if(!domain.equals("")){
//            tail = suffixList.getPublicSuffix(domain);//取的是最后一个顶级域名
//        }
//        if(tail==null){
//            tail="";
//        }
//        String head = domain.replace(tail,"");
        List<String> domain_char_list = Arrays.asList(domain.split("\\."));
        String prefix = String.join(".", domain_char_list.subList(0,domain_char_list.size()-1));
        Double prefix_entropy = get_entropy_of_str(prefix);
//        Double head_entropy = get_entropy_of_str(head);
//        Double domain_entropy = get_entropy_of_str(domain);

//        domain_feature.put("head",head);
        domain_feature.put("prefix",prefix);
//        domain_feature.put("prefix_len",prefix.length());
//        domain_feature.put("domain_entropy",domain_entropy);
        domain_feature.put("pre_entropy",prefix_entropy);
//        domain_feature.put("head_entropy",head_entropy);
        return domain_feature;
    }

    public static Double get_entropy_of_str(String str){
        Map<Character,Integer> c_map =new HashMap<>();
        char[] str_list=str.toCharArray();
        for (char c:str_list){
            if (!c_map.keySet().contains(c)){
                c_map.put(c,1);
            }else{
                c_map.put(c,c_map.get(c)+1);
            }
        }
        Collection<Integer> num_list = c_map.values();
        BigDecimal entropy = new BigDecimal(0);
        BigDecimal num = new BigDecimal(num_list.size());
        int sum_ = 0;
        for (Integer num_list_info:num_list){
            sum_+=num_list_info;
        }
        BigDecimal sum = new BigDecimal(sum_);
        for (Integer num_list_info:num_list){
            BigDecimal num_list_info_i = new BigDecimal(num_list_info);
            BigDecimal tmp = new BigDecimal(Math.log(num_list_info_i.divide(sum,10, BigDecimal.ROUND_HALF_UP).doubleValue()))
                    .divide(new BigDecimal(Math.log(2)),6,BigDecimal.ROUND_HALF_UP);
            entropy = entropy.add(new BigDecimal(-1).multiply((num_list_info_i.divide(sum,6, BigDecimal.ROUND_HALF_UP))).multiply(tmp));
        }
        return entropy.doubleValue();

    }

    private static Map<String,Double> get_ans_value_info(Row dns_info_row){
        Map<String,Double> domain_info_map = new HashMap<>();
        Map<Object,Integer> ans_value = dns_info_row.getFieldAs(7);
        List<String> ans_list = new ArrayList<>();
        for (Object ans:ans_value.keySet()){
            ans_list.add(ans.toString());
        }
        //ans_session_std
        Double cnt_std = 0d;
        Collection<Integer> cnt_list = ans_value.values();
        DescriptiveStatistics cnt_stats = new DescriptiveStatistics();
        if (cnt_list.size()>=1){
            for (Integer cnt:cnt_list){
                cnt_stats.addValue(cnt);
            }
            cnt_std = cnt_stats.getStandardDeviation();
        }
        //ans_entropy
        String ans_all = String.join(".", ans_list);
        Double ans_entropy_all = get_entropy_of_str(ans_all);
        //ans_entropy_mean
        DescriptiveStatistics ans_entropy_stats = new DescriptiveStatistics();
        Double ans_entropy_mean = 0d;
        if (ans_list.size()>0){
            for (String ans:ans_list){
                ans_entropy_stats.addValue(get_entropy_of_str(ans));
            }
            ans_entropy_mean = ans_entropy_stats.getMean();
        }

        //一起传入
        domain_info_map.put("ans_entropy_mean",ans_entropy_mean);
        domain_info_map.put("ans_entropy",ans_entropy_all);
        domain_info_map.put("ans_session_std",cnt_std);

        return domain_info_map;
    }

}
