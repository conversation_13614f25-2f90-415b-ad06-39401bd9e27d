package com.geeksec.analysisFunction.getPbMapInfo;

import com.geeksec.analysisFunction.analysisEntity.nebula.BaseEdge;
import com.geeksec.analysisFunction.analysisEntity.nebula.FingerInfo;
import com.geeksec.analysisFunction.analysisEntity.nebula.SSLFingerInfo;
import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import com.geeksec.common.utils.DomainUtils;
import com.geeksec.common.utils.Md5Util;
import com.geeksec.common.utils.ProNameForMysql;
import java.util.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;

public class SslInfoMapFlatMapFunction extends RichFlatMapFunction<Map<String, Object>, Map<String, Object>> {
    public HashMap<String,String> FINGER_JA3_MAP = null;
    private static final Logger logger = LoggerFactory.getLogger(SslInfoMapFlatMapFunction.class);

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        List<FingerInfo> FingerInfoList =  ProNameForMysql.getMysqlFingerInfo();
        if (FingerInfoList!=null){
            FINGER_JA3_MAP = ProNameForMysql.get_finger_ja3_map(FingerInfoList);
            logger.info("FINGER_JA3_MAP 信息加载成功");
        }else {
            FINGER_JA3_MAP = new HashMap<>();
            logger.info("FINGER_JA3_MAP 信息加载失败");
        }

    }

    @Override
    public void flatMap(Map<String, Object> pbMap, Collector<Map<String, Object>> collector) throws Exception {
        SSLFingerInfo sSSLFingerInfo = getSSLFinger(pbMap, "client");
        SSLFingerInfo dSSLFingerInfo = getSSLFinger(pbMap, "server");
        BaseEdge clientSslConnectDomainEdge = getSslConnectDomainEdge(pbMap, "client");
        BaseEdge serverSslConnectDomainEdge = getSslConnectDomainEdge(pbMap, "server");
        Row dipdSSLFingerSipRow = getDipdSSLFingerSipRow(pbMap);

        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("sSSLFingerInfoTag", sSSLFingerInfo);
        resultMap.put("dSSLFingerInfoTag", dSSLFingerInfo);
        if (dipdSSLFingerSipRow!=null){
            resultMap.put("SIP_DIP_FINGER_ROW",dipdSSLFingerSipRow);
        }
        resultMap.put("clientSslConnectDomainEdge", clientSslConnectDomainEdge);
        resultMap.put("serverSslConnectDomainEdge", serverSslConnectDomainEdge);

        collector.collect(resultMap);
    }

    private BaseEdge getSslConnectDomainEdge(Map<String, Object> pbMap, String type) {
        String clientIp = (String) pbMap.get("sIp");
        String serverIp = (String) pbMap.get("dIp");
        String sni = (String) pbMap.get("CH_ServerName");

        BaseEdge baseEdge = new BaseEdge();

        if (DomainUtils.isValidDomain(sni) && !sni.equals(serverIp)) {
            if (type.equals("client")) {
                baseEdge.setSrcId(clientIp);
            } else {
                baseEdge.setSrcId(serverIp);
            }
            if (sni.length() > 200) {
                baseEdge.setDstId(HashUtils.md5(sni));
            } else {
                baseEdge.setDstId(sni);
            }
            baseEdge.setFirstTime((Integer) pbMap.get("StartTime"));
            baseEdge.setLastTime((Integer) pbMap.get("StartTime"));
            baseEdge.setSessionCnt(0L);
        } else {
            return null;
        }

        NeededInfo neededInfo = new NeededInfo(pbMap);
        if(!"".equals(sni)){
            neededInfo.setSniDomain(sni);
        }
        baseEdge.setNeededInfo(neededInfo);

        return baseEdge;
    }

    private SSLFingerInfo getSSLFinger(Map<String, Object> pbMap, String type) {
        SSLFingerInfo sslFingerTagInfo = new SSLFingerInfo();
        String sSSLFinger = (String) pbMap.get("sSSLFinger");
        String dSSLFinger = (String) pbMap.get("dSSLFinger");
        String sni = (String) pbMap.get("CH_ServerName");
        String serverIp = (String) pbMap.get("dIp");
        if (type.equals("client")) {
            if (ObjectUtils.isEmpty(sSSLFinger)) {
                return null;
            }
            sslFingerTagInfo.setFingerId(sSSLFinger);
            sslFingerTagInfo.setJa3Hash(FINGER_JA3_MAP.getOrDefault(sSSLFinger, StringUtils.EMPTY));
            sslFingerTagInfo.setDesc(StringUtil.EMPTY_STRING);
            sslFingerTagInfo.setType("client");
        }

        if (type.equals("server")) {
            if (ObjectUtils.isEmpty(dSSLFinger)) {
                return null;
            }
            sslFingerTagInfo.setFingerId(dSSLFinger);
            sslFingerTagInfo.setJa3Hash(FINGER_JA3_MAP.getOrDefault(dSSLFinger, StringUtils.EMPTY));
            sslFingerTagInfo.setDesc(StringUtil.EMPTY_STRING);
            sslFingerTagInfo.setType("server");
        }
        if (DomainUtils.isValidDomain(sni) && !sni.equals(serverIp)){
                sslFingerTagInfo.setServer_domain(sni);
        }else{
            sslFingerTagInfo.setServer_domain(null);
        }
        sslFingerTagInfo.setDIp((String) pbMap.get("dIp"));
        sslFingerTagInfo.setSIp((String) pbMap.get("sIp"));
        sslFingerTagInfo.setSessionId((String) pbMap.get("SessionId"));
        sslFingerTagInfo.setEsKey((String) pbMap.get("es_key"));

        NeededInfo neededInfo = new NeededInfo(pbMap);
        if(!"".equals(sni)){
            neededInfo.setSniDomain(sni);
        }
        sslFingerTagInfo.setNeededInfo(neededInfo);

        return sslFingerTagInfo;
    }

    public Row getDipdSSLFingerSipRow(Map<String, Object> sslPbMap){
        List<String> dipSipList = Arrays.asList(sslPbMap.get("dIp").toString(),sslPbMap.get("sIp").toString());
        Row dipdSSLFingerSipRow = new Row(7);
        Collection<String> SessionId_list = new HashSet<>();
        SessionId_list.add((String) sslPbMap.get("SessionId"));
        Collection<String> fingerSet = new HashSet<>();
        String finger = (String) sslPbMap.get("sSSLFinger");
        if (finger.equals("0")){
            return null;
        }
        fingerSet.add(finger);

        String sni = (String) sslPbMap.get("CH_ServerName");
        NeededInfo neededInfo = new NeededInfo(sslPbMap);
        if(!"".equals(sni)){
            neededInfo.setSniDomain(sni);
        }

        dipdSSLFingerSipRow.setField(0,"SIP_DIP_FINGER_ROW");
        dipdSSLFingerSipRow.setField(1,dipSipList);
        dipdSSLFingerSipRow.setField(2,fingerSet);
        dipdSSLFingerSipRow.setField(3,sslPbMap.get("StartTime"));
        dipdSSLFingerSipRow.setField(4,SessionId_list);
        dipdSSLFingerSipRow.setField(5,sslPbMap.get("es_key"));
        dipdSSLFingerSipRow.setField(6, neededInfo);
        return dipdSSLFingerSipRow;
    }
}
