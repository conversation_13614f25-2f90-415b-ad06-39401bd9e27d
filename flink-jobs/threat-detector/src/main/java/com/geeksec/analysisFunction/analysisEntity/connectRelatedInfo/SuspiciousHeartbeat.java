package com.geeksec.analysisFunction.analysisEntity.connectRelatedInfo;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/7/27
 */

@Data
public class SuspiciousHeartbeat {
    private int size;
    private double interval;

    private int count;

    public SuspiciousHeartbeat(Integer size, double interval, int count) {
        this.size = size;
        this.interval = interval;
        this.count = count;
    }
}
