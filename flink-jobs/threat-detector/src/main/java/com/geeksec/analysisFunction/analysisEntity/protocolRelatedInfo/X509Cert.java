package com.geeksec.analysisFunction.analysisEntity.protocolRelatedInfo;

import com.alibaba.fastjson.annotation.JSONField;
import java.util.LinkedHashMap;
import lombok.Data;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @createTime: 2022/1/16 9:59
 **/
@Data
public class X509Cert {
    private static final Logger LOG = LogManager.getLogger(X509Cert.class);

    private byte[] cert;

    @JSONField(name = "CertID")
    private String CertID;

    @JSONField(name = "Subject")
    private LinkedHashMap<String, String> Subject = null;

    @JSONField(name = "Version")
    private int Version;

    @JSONField(name = "Issuer")
    private LinkedHashMap<String, String> Issuer = null;

    @J<PERSON>NField(name = "NotBefore")
    private String NotBefore = null;

    @J<PERSON>NField(name = "NotAfter")
    private String NotAfter = null;

    @JSONField(name = "Duration")
    private Long Duration = 0L;

    @JSONField(name = "SerialNumber")
    private String SerialNumber = null;

    @JSONField(name = "CN")
    private Object CN = null;

    @JSONField(name = "ASN1MD5")
    private String ASN1MD5 = null;

    @JSONField(name = "ASN1SHA1")
    private String ASN1SHA1 = null;

    public X509Cert(byte[] cert){
        this.cert = cert;
    }
}
