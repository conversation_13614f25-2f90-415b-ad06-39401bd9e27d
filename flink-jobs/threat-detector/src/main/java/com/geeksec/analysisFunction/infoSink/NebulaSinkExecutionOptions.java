package com.geeksec.analysisFunction.infoSink;

import com.geeksec.common.config.ConfigConstants;
import com.geeksec.common.config.ConfigurationManager;
import java.util.Arrays;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.nebula.connection.NebulaClientOptions;
import org.apache.flink.connector.nebula.connection.NebulaGraphConnectionProvider;
import org.apache.flink.connector.nebula.connection.NebulaMetaConnectionProvider;
import org.apache.flink.connector.nebula.sink.NebulaBatchOutputFormat;
import org.apache.flink.connector.nebula.sink.NebulaEdgeBatchOutputFormat;
import org.apache.flink.connector.nebula.statement.EdgeExecutionOptions;
import org.apache.flink.connector.nebula.statement.ExecutionOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Description：
 */
public class NebulaSinkExecutionOptions {

    private static final Logger logger = LoggerFactory.getLogger(NebulaSinkExecutionOptions.class);

    private static final ParameterTool CONFIG = ConfigurationManager.getConfig();
    public static final String NEBULA_GRAPH_ADDR = CONFIG.get(ConfigConstants.NEBULA_GRAPH_ADDR);
    public static final String NEBULA_META_ADDR = CONFIG.get(ConfigConstants.NEBULA_META_ADDR);
    public static final String NEBULA_GRAPH_SPACE = CONFIG.get(ConfigConstants.NEBULA_SPACE_NAME);

    private static Integer capacity = 20;

    public static NebulaGraphConnectionProvider graphConnectionProvider = null;
    public static NebulaMetaConnectionProvider metaConnectionProvider = null;

    static {
        // Nebula Conn通用配置
        NebulaClientOptions nebulaClientOptions = new NebulaClientOptions.NebulaClientOptionsBuilder()
                .setGraphAddress(NEBULA_GRAPH_ADDR)
                .setMetaAddress(NEBULA_META_ADDR)
                .build();
        graphConnectionProvider = new NebulaGraphConnectionProvider(nebulaClientOptions);
        metaConnectionProvider = new NebulaMetaConnectionProvider(nebulaClientOptions);
    }

    public static NebulaEdgeBatchOutputFormat handleEdgeFormat(String edgeType) {
        switch (edgeType) {
            case "finger_type_label":
            case "Http_Web_Login":
            case "random_finger_label":
            case "domain_ip_label":
            case "Port_Scan":
            case "dns_tunnel":
            case "dns_server":
            case "dns_le_server":
            case "app_scan":
            case "RCPAttack":
            case "x-ray":
            case "webshell":
            case "tunnel":
            case "srcp":
            case "encrypted_apt":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider,metaConnectionProvider,getLabelOptions());
            default:
                return null;
        }
    }

    /**
     * src--->标签 所有打标签操作
     */
    private static EdgeExecutionOptions getLabelOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("has_label")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Arrays.asList("analysis_by", "remark"))
                .setPositions(Arrays.asList(3,4))
                .setBatchSize(100)
                .setBatchIntervalMs(2000)
                .build();
    }
}
