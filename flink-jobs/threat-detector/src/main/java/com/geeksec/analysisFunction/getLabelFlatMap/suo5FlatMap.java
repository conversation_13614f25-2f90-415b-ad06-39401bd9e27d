package com.geeksec.analysisFunction.getLabelFlatMap;

import com.geeksec.SpecProtocolEnum;
import java.util.Collections;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2023/4/28
 */

public class suo5FlatMap extends RichFlatMapFunction<Row,Row> {
    private static final Logger logger = LoggerFactory.getLogger(suo5FlatMap.class);

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void flatMap(Row row, Collector<Row> collector) throws Exception {
        String SessionID = (String) row.getField(5);
        Row SessionLabelRow = new Row(4);
        SessionLabelRow.setField(0,"会话打标");
        SessionLabelRow.setField(1,SessionID);
        SessionLabelRow.setField(2, Collections.singleton(SpecProtocolEnum.TUNN_PROXY_SUO5.getCode()));//suo5代理隧道
        SessionLabelRow.setField(3,row.getFieldAs(6));//suo5代理隧道
        collector.collect(SessionLabelRow);
        logger.info("suo5代理隧道 ES插入{}",SessionID);
        Row alarm_row =new Row(8);
        String sIp = row.getFieldAs(1);
        String dIp = row.getFieldAs(2);
        String session_id = row.getFieldAs(5);
        alarm_row.setField(0,"加密隐蔽隧道通信");
        alarm_row.setField(1,"suo5代理隧道");
        alarm_row.setField(2,sIp);
        alarm_row.setField(3,dIp);
        alarm_row.setField(4,true);
        alarm_row.setField(5,SpecProtocolEnum.TUNN_PROXY_SUO5.getCode());
        alarm_row.setField(6,session_id);
        alarm_row.setField(7,row.getFieldAs(6));
        alarm_row.setField(8,row.getFieldAs(7));
        logger.info("suo5代理隧道告警{}",sIp);
        collector.collect(alarm_row);
    }
}
