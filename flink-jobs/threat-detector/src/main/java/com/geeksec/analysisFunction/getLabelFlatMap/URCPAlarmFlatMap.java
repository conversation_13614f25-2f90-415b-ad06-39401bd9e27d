package com.geeksec.analysisFunction.getLabelFlatMap;

import static com.geeksec.common.LabelUtils.AlarmUtils.getLabelEdgeRow;

import com.geeksec.SpecProtocolEnum;
import com.geeksec.analysisFunction.analysisEntity.connectRelatedInfo.ConnectBasicInfo;
import com.geeksec.analysisFunction.analysisEntity.protocolRelatedInfo.SRCPInfo;
import com.geeksec.common.LabelUtils.LabelRedisUtils;
import com.geeksec.common.LabelUtils.mysqlPool;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

public class URCPAlarmFlatMap extends RichFlatMapFunction<Row, Row> {
    private static final Logger logger = LoggerFactory.getLogger(URCPAlarmFlatMap.class);
    public static transient JedisPool jedisPool = null;
//    private static transient GenericObjectPool<Connection> HbasePool = null;

    public static final String HBASE_IP_APT_KNOWLEDGE_TABLE_NAME = "ip_knowledgebase";
    public static final String APT_COLUMN = "APT";
    public static final String APT_KEY = "name";
    public static final String APT_RESULT_KEY = "APT证书知识库碰撞结果";

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        jedisPool = LabelRedisUtils.initJedisPool();
        // Hbase 初始化
//        HbasePool = HbaseUtils.initHbasePool();
//        logger.info("生成 HbasePool 成功! {}", HbasePool.getNumIdle(), HbasePool.hashCode());
    }

    @Override
    public void close() throws Exception {
        super.close();
        if (!jedisPool.isClosed()) {
            jedisPool.close();
        }
//        if (HbasePool!=null){
//            HbasePool.close();
//        }
    }

    @Override
    public void flatMap(Row row, Collector<Row> collector) throws Exception {
        // 未知远程控制协议流量打标
        SRCPInfo urcpInfo = row.getFieldAs(1);
        ConnectBasicInfo connectBasicInfo = urcpInfo.getConnectBasicInfo();
        String sIp = connectBasicInfo.getSIp();
        String dIp = connectBasicInfo.getDIp();
        Jedis jedis = null;
        try {
            jedis = LabelRedisUtils.getJedis(jedisPool);
            jedis.select(7);
            jedis.sadd("controller", sIp);
            jedis.sadd("iscontrolled", dIp);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("获取redis连接失败");
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
        int dPort = Integer.parseInt(connectBasicInfo.getDPort());

        String SessionId = connectBasicInfo.getSessionId();
        String rcpType = urcpInfo.getRCPType();
        logger.info("未知远程控制协议下的行为告警检测{} {}", sIp, dIp + "_" + dPort);
        List<String> labelList = connectBasicInfo.getAnalysisLabelList();
        labelList.add(SpecProtocolEnum.URCP.getCode());
        connectBasicInfo.setAnalysisLabelList(labelList);
        urcpInfo.setConnectBasicInfo(connectBasicInfo);
        // 跳板判断与IP打标
        pivotCollet(collector, sIp, dIp);
        // 远程控制攻击
        Row resultRow = getLabelEdgeRow(dIp, SpecProtocolEnum.RAT.getCode());
        collector.collect(resultRow);
        Row aptKnowledgeCollision = APTKnowledgeCollision(dIp);
        if (aptKnowledgeCollision != null) {
            collector.collect(aptKnowledgeCollision);
        }
        collectInfo(collector, urcpInfo, SessionId, rcpType);
    }

    private void pivotCollet(Collector<Row> collector, String sIp, String dIp) {
        Jedis jedis = null;
        boolean pivot = false;
        try {
            jedis = LabelRedisUtils.getJedis(jedisPool);
            jedis.select(7);
            if (jedis.exists("iscontrolled")) {
                Set<String> iscontrolled = jedis.smembers("iscontrolled");
                if (iscontrolled.contains(sIp)) {
                    Row resultRow = getLabelEdgeRow(sIp, SpecProtocolEnum.PIVOT.getCode());//跳板节点
                    collector.collect(resultRow);
                    logger.info("跳板机告警 {} {}", sIp, dIp);
                }
            }
            if (jedis.exists("controller")) {
                Set<String> iscontrolled = jedis.smembers("controller");
                if (iscontrolled.contains(dIp)) {
                    Row resultRow = getLabelEdgeRow(dIp, SpecProtocolEnum.PIVOT.getCode());//跳板节点
                    collector.collect(resultRow);
                    logger.info("跳板机告警 {} {}", sIp, dIp);
                }
            }
        } catch (Exception e) {
            logger.error("获取redis连接失败");
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }

    private void collectInfo(Collector<Row> collector, SRCPInfo srcpInfo, String sessionId, String rcpType) {
        Row alarmRow = new Row(3);
        alarmRow.setField(0, "未知远程控制协议");
        // 标准远程控制协议下的C2行为基类
        alarmRow.setField(1, srcpInfo);
        collector.collect(alarmRow);

        Row SessionLabelRow = new Row(4);
        SessionLabelRow.setField(0, "会话打标");
        SessionLabelRow.setField(1, sessionId);
        Set<String> connectionLabels = new HashSet<>();
        connectionLabels.add(SpecProtocolEnum.URCP.getCode());
        if (srcpInfo.getConnectBasicInfo().isControl()) {
            connectionLabels.add(ConnectBasicInfo.controlTag);
        }
        SessionLabelRow.setField(2, connectionLabels);
        SessionLabelRow.setField(3, srcpInfo.getConnectBasicInfo().getEsKey());
        collector.collect(SessionLabelRow);

        // 给会话打心跳，控制，激活的标签
//        addSessionLabel(collector, srcpInfo, sessionId);
    }

    private static void addSessionLabel(Collector<Row> collector, SRCPInfo srcpInfo, String SessionId) {
        ConnectBasicInfo connectBasicInfo = srcpInfo.getConnectBasicInfo();

        if (connectBasicInfo.isControl()) {
            Row SessionLabelRow = new Row(3);
            SessionLabelRow.setField(0, "会话打标");
            SessionLabelRow.setField(1, SessionId);
            SessionLabelRow.setField(2, ConnectBasicInfo.controlTag);
            collector.collect(SessionLabelRow);
        }
    }

    private static Row APTKnowledgeCollision(String IP) throws Exception {
        java.sql.Connection connection = null;
        PreparedStatement select_preparedStatement = null;
        ResultSet selectResultSet = null;
        try {
            connection = mysqlPool.getConnection();
            String select_query = "SELECT tag_name FROM tb_threat_info WHERE target=? limit 1";
            select_preparedStatement = connection.prepareStatement(select_query);
            select_preparedStatement.setString(1, IP);
            selectResultSet = select_preparedStatement.executeQuery();
            logger.info("sql——{}", select_preparedStatement);
            // 执行查询
            try (ResultSet resultSet = select_preparedStatement.executeQuery()) {
                // 处理结果集
                while (resultSet.next()) {
                    // 获取tag_name列的值
                    String aptValue = resultSet.getString("tag_name");
                    return getLabelEdgeRow(IP, SpecProtocolEnum.fromName(aptValue).getCode());
                }
            }
//            Table ipAPTKnowledgetable = connection.getTable(TableName.valueOf(HBASE_IP_APT_KNOWLEDGE_TABLE_NAME));
//
//            // IP APT 关联 APT 碰撞
//            Get APTGet = new Get(Bytes.toBytes(IP));
//            APTGet.addColumn(Bytes.toBytes(APT_COLUMN), Bytes.toBytes(APT_KEY));
//            Result APTResult = ipAPTKnowledgetable.get(APTGet);
//            if (APTResult.getRow().length > 0) {
//                byte[] value = APTResult.getValue(Bytes.toBytes(APT_COLUMN), Bytes.toBytes(APT_KEY));
//                if (value != null) {
//                    String APTValueStr = Bytes.toString(value);
//                    logger.info("APT碰撞结果：{}, 标签名为：{}", APTValueStr, SpecProtocolEnum.fromName(APTValueStr).getCode());
//                    // IP APT 打标
//                    return getLabelEdgeRow(IP, SpecProtocolEnum.fromName(APTValueStr).getCode());
//                } else {
//                    System.out.println("APT列没有值");
//                }
//            }
        } catch (Exception e) {
            logger.error("apt组织与IP对应关系查询失败，报错：{}", e.toString());
        } finally {
            if (connection != null) {
                connection.close();
            }
        }
        return null;
    }
}
