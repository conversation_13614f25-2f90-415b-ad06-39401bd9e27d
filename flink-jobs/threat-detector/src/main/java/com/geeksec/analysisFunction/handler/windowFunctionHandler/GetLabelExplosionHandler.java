package com.geeksec.analysisFunction.handler.windowFunctionHandler;

import static com.geeksec.common.LabelUtils.AlarmUtils.getLabelEdgeRow;
import static com.geeksec.nta.pipeline.ThreatDetectionPipeline.PARALLELISM_4;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.flink.api.common.functions.ReduceFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingEventTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.geeksec.SpecProtocolEnum;
import com.geeksec.flinkTool.getWatermarkAndTimestamp.MyWatermarkStrategy;
import com.geeksec.flinkTool.labelKeySelector.SipDipFingerLabelKeySelector;
import com.geeksec.flinkTool.sideOutputTag.SinkOutPutTag.AlarmOutPutTag;
import com.geeksec.flinkTool.sideOutputTag.SinkOutPutTag.NebulaEdgeOutPutTag;

/**
 * <AUTHOR>
 * @Date 2023/4/25
 */

public class GetLabelExplosionHandler {
    private static final Logger logger = LoggerFactory.getLogger(GetLabelExplosionHandler.class);

    public static SingleOutputStreamOperator<Row> RDPLabelFunction(DataStream<Row> dipdSSLFingerSipRow){
        long RDP_delay = 30000;//30秒
        int RDP_timeStampPosition = 3;
        MyWatermarkStrategy myWatermarkStrategy = new MyWatermarkStrategy();
        myWatermarkStrategy.setMyEventTimeTimeStamp(RDP_timeStampPosition);
        myWatermarkStrategy.setMyDelay(RDP_delay);
        DataStream<Row> RDPLabelRowWithTimeStamp = dipdSSLFingerSipRow.assignTimestampsAndWatermarks(myWatermarkStrategy).name("生成事件时间戳和水印").setParallelism(2);//打上时间戳和水印都打
//        DataStream<Row> RDPLabelRowWithTimeStamp = dipdSSLFingerSipRow.assignTimestampsAndWatermarks(new BoundedOutOfOrdernessTimestampExtractor<Row>(Time.seconds(0)) {
//            @Override
//            public long extractTimestamp(Row row) {
//                Long timeStamp = Long.valueOf(row.getField(3).toString())*1000;
//                return timeStamp;
//            }
//        });//只打时间戳,没有watermark

        SingleOutputStreamOperator<Row> RDPLabelRow = RDPLabelRowWithTimeStamp.keyBy(new SipDipFingerLabelKeySelector())
                .window(TumblingEventTimeWindows.of(Time.seconds(2)))//5s为窗口大小
                .reduce(new ReduceFunction<Row>() {
                    @Override
                    public Row reduce(Row row, Row t1) throws Exception {
                        Collection<String> SessionId_listA = row.getFieldAs(4);
                        Collection<String> SessionId_listB = t1.getFieldAs(4);
                        if (SessionId_listA.size()<=10){
                            SessionId_listA.addAll(SessionId_listB);
                        }
                        Map<String,Integer> label_count_A = row.getFieldAs(2);
                        Map<String,Integer> label_count_B = t1.getFieldAs(2);
                        Map<String,Integer> label_count_tmp = add_label_map(label_count_A,label_count_B);
                        Row tmp = new Row(7);
                        tmp.setField(0,row.getField(0));
                        tmp.setField(1,row.getField(1));
                        tmp.setField(2,label_count_tmp);
                        tmp.setField(3,row.getField(3));
                        tmp.setField(4,SessionId_listA);
                        tmp.setField(5,row.getField(5));
                        tmp.setField(6,row.getField(6));
                        return tmp;
                    }
                }).name("RDP失败次数与返回次数进行窗口聚合").setParallelism(PARALLELISM_4)
                .process(new ProcessFunction<Row, Row>() {
                    @Override
                    public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                        Map<String,Integer> label_count = row.getFieldAs(2);
                        Integer fail_count = label_count.get(SpecProtocolEnum.RDP_LOGIN_REQ.getCode());
                        Integer return_count = label_count.get(SpecProtocolEnum.RDP_LOGIN_RETURN.getCode());
                        if (fail_count>15 || return_count>15){
                            List<String> IpList= (List<String>)row.getField(1);
                            String dIp = IpList.get(0);
                            String sIp = IpList.get(1);
                            Row alarm_row =new Row(8);
                            alarm_row.setField(0,"加密通道攻击行为");
                            alarm_row.setField(1,"RDP爆破行为");
                            alarm_row.setField(2,sIp);
                            alarm_row.setField(3,dIp);
                            alarm_row.setField(4,label_count);
                            alarm_row.setField(5,row.getField(4));
                            alarm_row.setField(6,row.getField(5));
                            alarm_row.setField(7,row.getField(6));
                            collector.collect(alarm_row);
                            logger.info("RDP爆破行为告警，源IP{}",sIp);
                        }
                    }
                })
                .name("窗口函数获取RDP爆破行为告警").setParallelism(PARALLELISM_4)
                .process(new ProcessFunction<Row, Row>() {
                    @Override
                    public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                        if (row.getField(0).equals("加密通道攻击行为")){
                            context.output(AlarmOutPutTag.Alarm_RDP_Row,row);
                        }
                    }
                }).name("ES告警分流").setParallelism(1);

        return RDPLabelRow;
    }

    public static SingleOutputStreamOperator<Row> OracleLabelFunction(DataStream<Row> dipdSSLFingerSipRow){
        long Oracle_delay = 30000;//30秒
        int Oracle_timeStampPosition = 3;
        MyWatermarkStrategy myWatermarkStrategy = new MyWatermarkStrategy();
        myWatermarkStrategy.setMyEventTimeTimeStamp(Oracle_timeStampPosition);
        myWatermarkStrategy.setMyDelay(Oracle_delay);
        DataStream<Row> OracleLabelRowWithTimeStamp = dipdSSLFingerSipRow.assignTimestampsAndWatermarks(myWatermarkStrategy).name("生成事件时间戳和水印").setParallelism(2);//打上时间戳和水印都打
//        DataStream<Row> OracleLabelRowWithTimeStamp = dipdSSLFingerSipRow.assignTimestampsAndWatermarks(new BoundedOutOfOrdernessTimestampExtractor<Row>(Time.seconds(0)) {
//            @Override
//            public long extractTimestamp(Row row) {
//                Long timeStamp = Long.valueOf(row.getField(3).toString())*1000;
//                return timeStamp;
//            }
//        });//只打时间戳,没有watermark

        SingleOutputStreamOperator<Row> OracleLabelRow = OracleLabelRowWithTimeStamp.keyBy(new SipDipFingerLabelKeySelector())
                .window(TumblingEventTimeWindows.of(Time.seconds(2)))//5s为窗口大小
                .reduce(new ReduceFunction<Row>() {
                    @Override
                    public Row reduce(Row row, Row t1) throws Exception {
                        Collection<String> SessionId_listA = row.getFieldAs(4);
                        Collection<String> SessionId_listB = t1.getFieldAs(4);
                        if (SessionId_listA.size()<=10){
                            SessionId_listA.addAll(SessionId_listB);
                        }
                        Map<String,Integer> label_count_A = row.getFieldAs(2);
                        Map<String,Integer> label_count_B = t1.getFieldAs(2);
                        Map<String,Integer> label_count_tmp = add_label_map(label_count_A,label_count_B);
                        Row tmp = new Row(7);
                        tmp.setField(0,row.getField(0));
                        tmp.setField(1,row.getField(1));
                        tmp.setField(2,label_count_tmp);
                        tmp.setField(3,row.getField(3));
                        tmp.setField(4,SessionId_listA);
                        tmp.setField(5,row.getFieldAs(5));
                        tmp.setField(6,row.getFieldAs(6));
                        return tmp;
                    }
                }).name("Oracle登录次数进行窗口聚合").setParallelism(PARALLELISM_4)
                .process(new ProcessFunction<Row, Row>() {
                    @Override
                    public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                        Map<String,Integer> label_count = row.getFieldAs(2);
                        //35004 oracle连接请求
                        //35014 oracle登陆失败
                        Integer fail_count = label_count.get(SpecProtocolEnum.ORACLE_LOGIN_FAIL.getCode());
                        Integer request_count = label_count.get(SpecProtocolEnum.ORACLE_CONNECT_REQ.getCode());
                        if (fail_count>20 || request_count>20){
                            List<String> IpList= (List<String>)row.getField(1);
                            String dIp = IpList.get(0);
                            String sIp = IpList.get(1);
                            Row alarm_row =new Row(8);
                            alarm_row.setField(0,"加密通道攻击行为");
                            alarm_row.setField(1,"Oracle爆破行为");
                            alarm_row.setField(2,sIp);
                            alarm_row.setField(3,dIp);
                            alarm_row.setField(4,label_count);
                            alarm_row.setField(5,row.getField(4));
                            alarm_row.setField(6,row.getField(5));
                            alarm_row.setField(7,row.getField(6));
                            collector.collect(alarm_row);
                            logger.info("Oracle爆破行为告警{}",dIp);
                        }
                    }
                })
                .name("窗口函数获取Oracle爆破行为告警").setParallelism(PARALLELISM_4)
                .process(new ProcessFunction<Row, Row>() {
                    @Override
                    public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                        if (row.getField(0).equals("加密通道攻击行为")){
                            context.output(AlarmOutPutTag.Alarm_Oracle_Row,row);
                        }
                    }
                }).name("ES告警分流").setParallelism(1);

        return OracleLabelRow;
    }

    public static SingleOutputStreamOperator<Row> MYSQLLabelFunction(DataStream<Row> dipdSSLFingerSipRow){
        long MYSQL_delay = 30000;//30秒
        int MYSQL_timeStampPosition = 3;
        MyWatermarkStrategy myWatermarkStrategy = new MyWatermarkStrategy();
        myWatermarkStrategy.setMyEventTimeTimeStamp(MYSQL_timeStampPosition);
        myWatermarkStrategy.setMyDelay(MYSQL_delay);
        DataStream<Row> MYSQLLabelRowWithTimeStamp = dipdSSLFingerSipRow.assignTimestampsAndWatermarks(myWatermarkStrategy).name("生成事件时间戳和水印").setParallelism(2);//打上时间戳和水印都打
//        DataStream<Row> MYSQLLabelRowWithTimeStamp = dipdSSLFingerSipRow.assignTimestampsAndWatermarks(new BoundedOutOfOrdernessTimestampExtractor<Row>(Time.seconds(0)) {
//            @Override
//            public long extractTimestamp(Row row) {
//                Long timeStamp = Long.valueOf(row.getField(3).toString())*1000;
//                return timeStamp;
//            }
//        });//只打时间戳,没有watermark

        SingleOutputStreamOperator<Row> MYSQLLabelRow = MYSQLLabelRowWithTimeStamp.keyBy(new SipDipFingerLabelKeySelector())
                .window(TumblingEventTimeWindows.of(Time.seconds(2)))//5s为窗口大小
                .reduce(new ReduceFunction<Row>() {
                    @Override
                    public Row reduce(Row row, Row t1) throws Exception {
                        Collection<String> SessionId_listA = row.getFieldAs(4);
                        Collection<String> SessionId_listB = t1.getFieldAs(4);
                        if (SessionId_listA.size()<=10){
                            SessionId_listA.addAll(SessionId_listB);
                        }
                        Map<String,Integer> label_count_A = row.getFieldAs(2);
                        Map<String,Integer> label_count_B = t1.getFieldAs(2);
                        Map<String,Integer> label_count_tmp = add_label_map(label_count_A,label_count_B);
                        Row tmp = new Row(7);
                        tmp.setField(0,row.getField(0));
                        tmp.setField(1,row.getField(1));
                        tmp.setField(2,label_count_tmp);
                        tmp.setField(3,row.getField(3));
                        tmp.setField(4,SessionId_listA);
                        tmp.setField(5,row.getField(5));
                        tmp.setField(6,row.getField(6));
                        return tmp;
                    }
                }).name("MYSQL登录次数进行窗口聚合").setParallelism(PARALLELISM_4)
                .process(new ProcessFunction<Row, Row>() {
                    @Override
                    public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                        Map<String,Integer> label_count = row.getFieldAs(2);
                        Integer fail_count = label_count.get(SpecProtocolEnum.MYSQL_LOGIN_FAIL.getCode());
                        if (fail_count>25){
                            List<String> IpList= (List<String>)row.getField(1);
                            String dIp = IpList.get(0);
                            String sIp = IpList.get(1);
                            Row alarm_row =new Row(8);
                            alarm_row.setField(0,"加密通道攻击行为");
                            alarm_row.setField(1,"MYSQL爆破行为");
                            alarm_row.setField(2,sIp);
                            alarm_row.setField(3,dIp);
                            alarm_row.setField(4,label_count);
                            alarm_row.setField(5,row.getField(4));
                            alarm_row.setField(6,row.getField(5));
                            alarm_row.setField(7,row.getField(6));
                            collector.collect(alarm_row);
                            logger.info("MYSQL爆破行为告警{}",dIp);
                        }
                    }
                })
                .name("窗口函数获取MYSQL爆破行为告警").setParallelism(PARALLELISM_4)
                .process(new ProcessFunction<Row, Row>() {
                    @Override
                    public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                        if (row.getField(0).equals("加密通道攻击行为")){
                            context.output(AlarmOutPutTag.Alarm_MYSQL_Row,row);
                        }
                    }
                }).name("ES告警分流").setParallelism(1);

        return MYSQLLabelRow;
    }

    public static SingleOutputStreamOperator<Row> SMBLabelFunction(DataStream<Row> dipdSSLFingerSipRow){
        long SMB_delay = 30000;//30秒
        int SMB_timeStampPosition = 3;
        MyWatermarkStrategy myWatermarkStrategy = new MyWatermarkStrategy();
        myWatermarkStrategy.setMyEventTimeTimeStamp(SMB_timeStampPosition);
        myWatermarkStrategy.setMyDelay(SMB_delay);
        DataStream<Row> SMBLabelRowWithTimeStamp = dipdSSLFingerSipRow.assignTimestampsAndWatermarks(myWatermarkStrategy).name("生成事件时间戳和水印").setParallelism(2);//打上时间戳和水印都打
//        DataStream<Row> SMBLabelRowWithTimeStamp = dipdSSLFingerSipRow.assignTimestampsAndWatermarks(new BoundedOutOfOrdernessTimestampExtractor<Row>(Time.seconds(0)) {
//            @Override
//            public long extractTimestamp(Row row) {
//                Long timeStamp = Long.valueOf(row.getField(3).toString())*1000;
//                return timeStamp;
//            }
//        });//只打时间戳,没有watermark

        SingleOutputStreamOperator<Row> SMBLabelRow = SMBLabelRowWithTimeStamp.keyBy(new SipDipFingerLabelKeySelector())
                .window(TumblingEventTimeWindows.of(Time.seconds(2)))//5s为窗口大小
                .reduce(new ReduceFunction<Row>() {
                    @Override
                    public Row reduce(Row row, Row t1) throws Exception {
                        Collection<String> SessionId_listA = row.getFieldAs(4);
                        Collection<String> SessionId_listB = t1.getFieldAs(4);
                        if (SessionId_listA.size()<=10){
                            SessionId_listA.addAll(SessionId_listB);
                        }
                        Map<String,Integer> label_count_A = row.getFieldAs(2);
                        Map<String,Integer> label_count_B = t1.getFieldAs(2);
                        Map<String,Integer> label_count_tmp = add_label_map(label_count_A,label_count_B);
                        Row tmp = new Row(7);
                        tmp.setField(0,row.getField(0));
                        tmp.setField(1,row.getField(1));
                        tmp.setField(2,label_count_tmp);
                        tmp.setField(3,row.getField(3));
                        tmp.setField(4,SessionId_listA);
                        tmp.setField(5,row.getField(5));
                        tmp.setField(6,row.getField(6));
                        return tmp;
                    }
                }).name("SMB失败次数与请求次数进行窗口聚合").setParallelism(PARALLELISM_4)
                .process(new ProcessFunction<Row, Row>() {
                    @Override
                    public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                        Map<String,Integer> label_count = row.getFieldAs(2);
                        Integer request_count = label_count.get(SpecProtocolEnum.SMB_LOGIN_REQ.getCode());
                        Integer fail_count = label_count.get(SpecProtocolEnum.SMB_LOGIN_FAIL.getCode());
                        if (fail_count>15 || request_count>15){
                            List<String> IpList= (List<String>)row.getField(1);
                            String dIp = IpList.get(0);
                            String sIp = IpList.get(1);
                            Row alarm_row =new Row(8);
                            alarm_row.setField(0,"加密通道攻击行为");
                            alarm_row.setField(1,"SMB爆破行为");
                            alarm_row.setField(2,sIp);
                            alarm_row.setField(3,dIp);
                            alarm_row.setField(4,label_count);
                            alarm_row.setField(5,row.getField(4));
                            alarm_row.setField(6,row.getField(5));
                            alarm_row.setField(7,row.getField(7));
                            collector.collect(alarm_row);
                            logger.info("SMB爆破行为告警，源IP{}",sIp);
                        }
                    }
                })
                .name("窗口函数获取SMB爆破行为告警").setParallelism(PARALLELISM_4)
                .process(new ProcessFunction<Row, Row>() {
                    @Override
                    public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                        if (row.getField(0).equals("加密通道攻击行为")){
                            context.output(AlarmOutPutTag.Alarm_SMB_Row,row);
                        }
                    }
                }).name("ES告警分流").setParallelism(1);

        return SMBLabelRow;
    }

    public static SingleOutputStreamOperator<Row> xRayLabelFunction(DataStream<Row> dipdSSLFingerSipRow){
        long xRay_delay = 30000;//30秒
        int xRay_timeStampPosition = 3;
        MyWatermarkStrategy myWatermarkStrategy = new MyWatermarkStrategy();
        myWatermarkStrategy.setMyEventTimeTimeStamp(xRay_timeStampPosition);
        myWatermarkStrategy.setMyDelay(xRay_delay);
        DataStream<Row> xRayLabelRowWithTimeStamp = dipdSSLFingerSipRow.assignTimestampsAndWatermarks(myWatermarkStrategy).name("生成事件时间戳和水印").setParallelism(2);//打上时间戳和水印都打
//        DataStream<Row> xRayLabelRowWithTimeStamp = dipdSSLFingerSipRow.assignTimestampsAndWatermarks(new BoundedOutOfOrdernessTimestampExtractor<Row>(Time.seconds(0)) {
//            @Override
//            public long extractTimestamp(Row row) {
//                Long timeStamp = Long.valueOf(row.getField(3).toString())*1000;
//                return timeStamp;
//            }
//        });//只打时间戳,没有watermark

        SingleOutputStreamOperator<Row> xRayLabelRow = xRayLabelRowWithTimeStamp.keyBy(new SipDipFingerLabelKeySelector())
                .window(TumblingEventTimeWindows.of(Time.minutes(1)))//1min为窗口大小
                .reduce(new ReduceFunction<Row>() {
                    @Override
                    public Row reduce(Row row, Row t1) throws Exception {
                        List<String> SessionId_listA = row.getFieldAs(4);
                        List<String> SessionId_listB = t1.getFieldAs(4);
                        SessionId_listA.addAll(SessionId_listB);
                        Row tmp = new Row(7);
                        tmp.setField(0,row.getField(0));
                        tmp.setField(1,row.getField(1));
                        tmp.setField(2,row.getField(2));
                        tmp.setField(3,row.getField(3));
                        tmp.setField(4,SessionId_listA);
                        tmp.setField(5,row.getField(5));
                        tmp.setField(6,row.getField(6));
                        return tmp;
                    }
                }).name("xRay会话次数进行窗口聚合").setParallelism(PARALLELISM_4)
                .process(new ProcessFunction<Row, Row>() {
                    @Override
                    public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                        List<String> SessionId_list = row.getFieldAs(4);
                        String finger = row.getFieldAs(2);
                        if (SessionId_list.size()>1000){
                            List<String> IpList= (List<String>)row.getField(1);
                            String dIp = IpList.get(0);
                            String sIp = IpList.get(1);
                            Row alarm_row =new Row(8);
                            alarm_row.setField(0,"加密通道攻击行为");
                            alarm_row.setField(1,"xRay扫描行为");
                            alarm_row.setField(2,sIp);
                            alarm_row.setField(3,dIp);
                            alarm_row.setField(4,finger);
                            alarm_row.setField(5,row.getField(4));
                            alarm_row.setField(6,row.getField(5));
                            alarm_row.setField(7,row.getField(6));
                            collector.collect(alarm_row);
                            logger.info("xRay漏扫告警，源IP{}",sIp);
                            Row resultRow = getLabelEdgeRow(sIp, SpecProtocolEnum.XRAY_SACN.getCode());//xRay漏扫工具IP标签
                            collector.collect(resultRow);
                            logger.info("xRay漏扫工具 边插入{},27063",sIp);
                        }
                    }
                })
                .name("窗口函数获取xRay爆破行为告警").setParallelism(PARALLELISM_4)
                .process(new ProcessFunction<Row, Row>() {
                    @Override
                    public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                        if (row.getField(0).equals("加密通道攻击行为")){
                            context.output(AlarmOutPutTag.Alarm_xRay_Row,row);
                        }else {
                            context.output(NebulaEdgeOutPutTag.Nebula_XRay_EdgeRow,row);
                        }
                    }
                }).name("ES告警和nebula打标分流").setParallelism(1);

        return xRayLabelRow;
    }

    //两个map的键值是一样的
    private static Map<String,Integer> add_label_map(Map<String, Integer> mapA,Map<String,Integer> mapB){//B大A小
        Map<String,Integer> map_result = new HashMap<>();
        for (String key: mapA.keySet()){
            map_result.put(key,mapA.get(key)+mapB.get(key));
        }
        return map_result;
    }

}
