package com.geeksec.analysisFunction.getLabelFlatMap;

import com.geeksec.SpecProtocolEnum;
import com.geeksec.common.LabelUtils.FileUtil;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2023/4/24
 */

public class NeoregeoFlatMap  extends RichFlatMapFunction<Row,Row> {
    public static List<String> english_LIST = new ArrayList<>();
    private static final Logger logger = LoggerFactory.getLogger(NeoregeoFlatMap.class);

    private static Double ENTROPY_THRESHOLD = Double.valueOf(4.9);
    @Override
    public void open(Configuration parameters) throws Exception {
        InputStream english_stream = this.getClass().getClassLoader().getResourceAsStream("english3.txt");
        BufferedReader english_buffer = new BufferedReader(new InputStreamReader(english_stream));
        //加载配置文件
        try {
            english_LIST = FileUtil.loadEnglishList(english_buffer);
        } catch (Exception e) {
            throw new IOException("file load fail ==>", e);
        }
        super.open(parameters);
    }
    @Override
    public void flatMap(Row row, Collector<Row> collector) throws Exception {
        Map<String,String> abnormal_head = row.getFieldAs(4);
        String SessionID = (String) row.getField(5);
        for (String key:abnormal_head.keySet()){
            //判断值的熵，目前弃用
            //String val = abnormal_head.get(key);
            //if (val.length()>54){
            //   val = val.substring(0,54);
            //}
            //Double val_Entropy = AlarmUtils.calculateEntropy(val);
            String field = key.split("-")[1];
            String val = abnormal_head.get(key);
            if (!english_LIST.contains(field)){
                List<String> alarm_info = Arrays.asList(field,val);
                Row SessionLabelRow = new Row(4);
                SessionLabelRow.setField(0,"会话打标");
                SessionLabelRow.setField(1,SessionID);
                SessionLabelRow.setField(2, Collections.singleton(SpecProtocolEnum.NEOREG.getCode()));//Neoreg代理隧道
                SessionLabelRow.setField(3,row.getFieldAs(6));
//                    AddTagToSession(SessionID,"21001");//web登录爆破
                collector.collect(SessionLabelRow);
                logger.info("Neoregeo代理隧道 ES插入{}",SessionID);
                Row alarm_row =new Row(8);
                String sIp = row.getFieldAs(1);
                String dIp = row.getFieldAs(2);
                String session_id = row.getFieldAs(5);
                alarm_row.setField(0,"加密隐蔽隧道通信");
                alarm_row.setField(1,"Neoregeo代理隧道");
                alarm_row.setField(2,sIp);
                alarm_row.setField(3,dIp);
                alarm_row.setField(4,alarm_info);
                alarm_row.setField(5,SpecProtocolEnum.NEOREG.getCode());
                alarm_row.setField(6,session_id);
                alarm_row.setField(7,row.getFieldAs(6));
                alarm_row.setField(8,row.getFieldAs(7));
                logger.info("Neoregeo代理隧道告警{}",sIp);
                collector.collect(alarm_row);
                break;
            }
        }
    }
}
