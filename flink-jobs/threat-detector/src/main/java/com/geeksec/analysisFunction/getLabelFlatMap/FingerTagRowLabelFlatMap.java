package com.geeksec.analysisFunction.getLabelFlatMap;

import static com.geeksec.analysisFunction.getLabelFlatMap.IpDomainEdgeRowLabelFlatMap.suffixList;

import com.geeksec.SpecProtocolEnum;
import com.geeksec.analysisFunction.analysisEntity.nebula.FingerInfo;
import com.geeksec.common.LabelUtils.AlarmUtils;
import com.geeksec.common.LabelUtils.FileUtil;
import com.geeksec.common.utils.ProNameForMysql;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2022/11/4
 */

public class FingerTagRowLabelFlatMap extends RichFlatMapFunction<Row,Row> {
    private static final Logger logger = LoggerFactory.getLogger(FingerTagRowLabelFlatMap.class);
    public static HashMap<String,String> FINGER_MAP = null;
    public HashMap<String, List<String>> FINGER_TYPE_MAP = new HashMap<>();

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        InputStream finger_type_map_stream = this.getClass().getClassLoader().getResourceAsStream("finger_type.csv");
        BufferedReader finger_type_map_buffer = new BufferedReader(new InputStreamReader(finger_type_map_stream));
        try {
            FINGER_TYPE_MAP = FileUtil.loadFingerTypeMap(finger_type_map_buffer);
        } catch (Exception e) {
            throw new IOException("file load fail ==>", e);
        }

        List<FingerInfo> FingerInfoList =  ProNameForMysql.getMysqlFingerInfo();
        if (FingerInfoList!=null){
            FINGER_MAP = ProNameForMysql.get_finger_type_map(FingerInfoList);
            logger.info("FINGER_MAP 信息加载成功");
        }else {
            FINGER_MAP = new HashMap<>();
            logger.info("FINGER_MAP 信息加载失败");
        }

    }

    @Override
    public void flatMap(Row fingerTagRow, Collector<Row> collector) throws Exception {
        String finger_id = fingerTagRow.getFieldAs(1).toString();
        String finger_Label = null;
        String ja3 = fingerTagRow.getFieldAs(2).toString();
        Set<String> ratType = new HashSet<>();
        // 指纹信息匹配
        if (FINGER_MAP.containsKey(finger_id)){
            String finger_type_this = FINGER_MAP.get(finger_id);
            for (String finger_type:FINGER_TYPE_MAP.keySet()){
                List<String> finger_type_map = FINGER_TYPE_MAP.get(finger_type);
                if (finger_type_map.contains(finger_type_this)){
                    finger_Label = finger_type;
                    if (!"unk".equals(finger_type_this)){
                        ratType.add(finger_type_this);
                    }
                    break;
                }
            }
            if (finger_Label==null){
                System.out.println("未查询到指纹");
                finger_Label = SpecProtocolEnum.UNK_FINGER.getCode();
            }
            //进行SSL挖矿告警
            if (finger_Label.equals(SpecProtocolEnum.FINGER_SSL_MINE.getCode())){
                Row Mine_alarm_row = new Row(10);
                Mine_alarm_row.setField(0,"挖矿病毒");
                Mine_alarm_row.setField(1,fingerTagRow.getField(5));
                Mine_alarm_row.setField(2,fingerTagRow.getField(6));
                String Server_domain = fingerTagRow.getFieldAs(7);
                if (Server_domain!=null){
                    Mine_alarm_row.setField(3,Server_domain);
                    Mine_alarm_row.setField(4,suffixList.getRegistrableDomain(Server_domain).split("\\.")[0]);
                }else{
                    Mine_alarm_row.setField(3,"");
                    Mine_alarm_row.setField(4,"");
                }
                Mine_alarm_row.setField(5,finger_id);//使用FingerID 告警
                Mine_alarm_row.setField(6,finger_Label);
                Mine_alarm_row.setField(7,fingerTagRow.getField(8));
                Mine_alarm_row.setField(8,fingerTagRow.getField(9));//es_key
                Mine_alarm_row.setField(9,fingerTagRow.getField(10));

                logger.info("挖矿病毒告警{}",ja3);
                collector.collect(Mine_alarm_row);
            }
            if (!ratType.isEmpty()){
                Set<String> sessionLabels = new HashSet<>();
                if (ratType.contains("Trickbot")){
                    sessionLabels.add(SpecProtocolEnum.TRICKBOT.getCode());
                }
                if (ratType.contains("Hancitor")){
                    sessionLabels.add(SpecProtocolEnum.HANCITOR.getCode());
                }
                if (ratType.contains("Qakbot")){
                    sessionLabels.add(SpecProtocolEnum.QAKBOT.getCode());
                }
                if (ratType.contains("Emotet")){
                    sessionLabels.add(SpecProtocolEnum.EMOTET.getCode());
                }
                if (ratType.contains("Dridex")){
                    sessionLabels.add(SpecProtocolEnum.DRIDEX.getCode());
                }
                if (!sessionLabels.isEmpty()){
                    Row SessionRow = new Row(4);
                    SessionRow.setField(0,"会话打标");
                    SessionRow.setField(1,fingerTagRow.getFieldAs(8));
                    SessionRow.setField(2,sessionLabels);
                    SessionRow.setField(3,fingerTagRow.getFieldAs(9));
                    collector.collect(SessionRow);
                }
            }
            //14004,14005,14006,14007,14008,14009,14010√,14011,14012,14013,14014
            SpecProtocolEnum fingerEnum = SpecProtocolEnum.fromCode(finger_Label);
            switch (fingerEnum){
                case TROJAN_FINGER://远控木马，木马指纹
                    Row alarm_row14005 = AlarmUtils.writeFingerAlarm("远控木马","木马指纹",fingerTagRow,finger_Label);
                    collector.collect(alarm_row14005);
                    break;
                case RAT_FINGER://远控木马，恶意软件
                    Row alarm_row14006 = AlarmUtils.writeFingerAlarm("远控木马","恶意软件指纹",fingerTagRow,finger_Label);
                    collector.collect(alarm_row14006);
                    break;
                case PT_FINGER://扫描行为，渗透工具
                    Row alarm_row14007 = AlarmUtils.writeFingerAlarm("扫描行为","渗透工具指纹",fingerTagRow,finger_Label);
                    collector.collect(alarm_row14007);
                    break;
                case TOR_FINGER://违规外联，匿名通讯
                    Row alarm_row14008 = AlarmUtils.writeFingerAlarm("违规外联","匿名通讯指纹",fingerTagRow,finger_Label);
                    collector.collect(alarm_row14008);
                    break;
                case CRAW_FINGER://远控木马，爬虫工具
                    Row alarm_row14009 = AlarmUtils.writeFingerAlarm("远控木马","爬虫工具指纹",fingerTagRow,finger_Label);
                    collector.collect(alarm_row14009);
                    break;
                case TUNN_PROXY_FINGER://加密隐蔽隧道通信,隧道代理
                    Row alarm_row14014 = AlarmUtils.writeFingerAlarm("加密隐蔽隧道通信","GoProxy代理隧道",fingerTagRow,finger_Label);
                    collector.collect(alarm_row14014);
                    break;
                case HACK_FINGER://加密通道攻击行为,awvs
                    String finger_type = fingerTagRow.getFieldAs(4);
                    if(finger_type.equals("client")){
                        Row alarm_row14015 = AlarmUtils.writeFingerAlarm("加密通道攻击行为","awvs",fingerTagRow,finger_Label);
                        collector.collect(alarm_row14015);
                        break;
                    }else{
                        logger.info("是awvs可能的服务端指纹，不予告警");
                        break;
                    }
                default:
//                    logger.info("指纹类型不需要告警");
                    break;
            }
        }

    }
}
