package com.geeksec.dbConnect.kafka;

import static com.geeksec.nta.pipeline.ThreatDetectionPipeline.PARALLELISM_4;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Map;
import java.util.Properties;

import org.apache.commons.lang3.SerializationException;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysisFunction.analysisEntity.protocolRelatedInfo.X509Cert;
import com.geeksec.analysisFunction.analysisEntity.webshell.KafkaConfig;
import com.geeksec.common.config.ConfigConstants;
import com.geeksec.common.config.ConfigurationManager;
import com.geeksec.flinkTool.deserializer.CertSerializationSchema;
import com.geeksec.flinkTool.deserializer.KafkaProtoMetricDeserializer;
import com.geeksec.flinkTool.deserializer.modelSwitchDeserializer;

/**
 * <AUTHOR>
 * @Date 2024/1/29
 */

public class KafkaStreamBuilder {
    private static final ParameterTool CONFIG = ConfigurationManager.getConfig();
    public static final String BOOTSTRAP_SERVERS = CONFIG.get(ConfigConstants.KAFKA_BOOTSTRAP_SERVERS);
    public static final String GROUP_ID = CONFIG.get(ConfigConstants.KAFKA_GROUP_ID);
    public static final String MODEL_GROUP_ID = CONFIG.get(ConfigConstants.KAFKA_MODEL_SWITCH_GROUP_ID);
    public static final String TOPIC = CONFIG.get(ConfigConstants.KAFKA_TOPIC);
    public static final String MODEL_TOPIC = CONFIG.get(ConfigConstants.KAFKA_MODEL_SWITCH_TOPIC);
    public static final String ORDER_TOPIC = CONFIG.get(ConfigConstants.KAFKA_ORDER_TOPIC);
    public static final String ORDER_GROUP_ID = CONFIG.get(ConfigConstants.KAFKA_ORDER_GROUP_ID);
    public static final String CERT_TOPIC = CONFIG.get(ConfigConstants.CERT_KAFKA_TOPIC_NAME);
    public static final String CERT_GROUP_ID = CONFIG.get(ConfigConstants.CERT_KAFKA_GROUP_ID);

    private static final Logger logger = LoggerFactory.getLogger(KafkaStreamBuilder.class);

    // 获取 Kafka Consumer 的基本配置参数
    public static Properties getConsumerProperties(){
        Properties properties = new Properties();
        properties.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS);
        properties.put(ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG, "60000");
        properties.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, "5000");
        return properties;
    }


    public static DataStream<Map<String,Object>> getMetaDataKafkaStream(StreamExecutionEnvironment env){
        Properties properties = getConsumerProperties();
        properties.setProperty("group.id", GROUP_ID);

        KafkaSource<Map<String,Object>> kafkaSource = KafkaSource.<Map<String,Object>>builder()
                .setBootstrapServers(properties.getProperty("bootstrap.servers"))
                .setTopics(Collections.singletonList(TOPIC))
                .setStartingOffsets(OffsetsInitializer.latest())//从最新的地方开始取
                .setDeserializer(new KafkaProtoMetricDeserializer())
                .setGroupId(properties.getProperty("group.id"))
                .setProperties(properties)
                .build();

        // 开始读取Kafka PB数据流至DataStream(进行transfer转换为JKNmsg对象实体)
        return env.fromSource(
                kafkaSource,
                WatermarkStrategy.noWatermarks(),
                "Kafka-Source",
                TypeInformation.of(new TypeHint<Map<String, Object>>(){})
        ).name("Kafka转义JKNmsg实体数据流").setParallelism(PARALLELISM_4);
    }

    public static DataStream<Map> getModelSwitchKafkaStream(StreamExecutionEnvironment env){
        Properties model_properties = getConsumerProperties();
        model_properties.put("model.group.id",MODEL_GROUP_ID);

        // 拉取kafka获得需要的模型开关数据
        KafkaSource<Map> modelSwitchKafkaSource = KafkaSource.<Map>builder()
                .setBootstrapServers(model_properties.getProperty("bootstrap.servers"))
                .setTopics(Collections.singletonList(MODEL_TOPIC))
                .setStartingOffsets(OffsetsInitializer.latest())//从最新的地方开始取
                .setDeserializer(new modelSwitchDeserializer())
                .setGroupId(model_properties.getProperty("model.group.id"))
                .setProperties(model_properties)
                .build();

        return env.fromSource(
                modelSwitchKafkaSource,
                WatermarkStrategy.noWatermarks(),
                "modelSwitch-Source",
                TypeInformation.of(Map.class)
        ).name("Kafka转义模型开关实时数据流").setParallelism(1);
    }

    /**
     * 获取外发告警Kafka地址与指定topicName消息流
     * @param env
     * @return DataStream<Map>
     */
    public static DataStream<KafkaConfig> getSubscribeKafkaStream(StreamExecutionEnvironment env){
        Properties subscribe_properties = getConsumerProperties();
        subscribe_properties.put("order.group.id",ORDER_GROUP_ID);

        // 拉取kafka获得需要的模型开关数据
        KafkaSource<KafkaConfig> subscirbeKafkaSource = KafkaSource.<KafkaConfig>builder()
                .setBootstrapServers(subscribe_properties.getProperty("bootstrap.servers"))
                .setTopics(Collections.singletonList(ORDER_TOPIC))
                .setStartingOffsets(OffsetsInitializer.latest())//从最新的地方开始取
                .setDeserializer(new KafkaRecordDeserializationSchema<KafkaConfig>(){
                    @Override
                    public void deserialize(ConsumerRecord<byte[], byte[]> consumerRecord, Collector<KafkaConfig> collector) throws IOException {
                        byte[] values = consumerRecord.value();
                        byte[] key = consumerRecord.key();
                        String keyString = new String(key, StandardCharsets.UTF_8);
                        if (values == null){
                            logger.info("SubscribeKafka值为空");
                        }else {
                            try {
                                JSONObject obj = JSONObject.parseObject(new String(values, StandardCharsets.UTF_8));
                                if(keyString.equals("order_change")){
                                    KafkaConfig config = new KafkaConfig((String) obj.get("ip"), (String) obj.get("ip"), (String) obj.get("topic"));
                                    collector.collect(config);
                                }else {
                                    logger.info("消费到了告警数据，topic：{}",keyString);
                                }
                            } catch (Exception e) {
                                throw new SerializationException("Error when serializing Customerto byte[] " + e);
                            }
                        }
                    }

                    @Override
                    public TypeInformation<KafkaConfig> getProducedType() {
                        return TypeInformation.of(KafkaConfig.class);
                    }
                })
                .setGroupId(subscribe_properties.getProperty("order.group.id"))
                .setProperties(subscribe_properties)
                .build();

        return env.fromSource(
                subscirbeKafkaSource,
                WatermarkStrategy.noWatermarks(),
                "subscribeKafka-Source",
                TypeInformation.of(KafkaConfig.class)
        ).name("外发Kafka更改实时数据流").setParallelism(1);
    }

    public static DataStream<X509Cert> getCertKafkaStream(StreamExecutionEnvironment env) {
        Properties certProperties = getConsumerProperties();
        certProperties.put("cert.kafka.group.id",CERT_GROUP_ID);

        // 拉取kafka获得需要的模型开关数据
        KafkaSource<X509Cert> modelSwitchKafkaSource = KafkaSource.<X509Cert>builder()
                .setBootstrapServers(certProperties.getProperty("bootstrap.servers"))
                .setTopics(Collections.singletonList(CERT_TOPIC))
                .setStartingOffsets(OffsetsInitializer.latest())//从最新的地方开始取
                .setDeserializer(new CertSerializationSchema())
                .setGroupId(certProperties.getProperty("cert.kafka.group.id"))
                .setProperties(certProperties)
                .build();

        return env.fromSource(
                modelSwitchKafkaSource,
                WatermarkStrategy.noWatermarks(),
                "cert-Source",
                TypeInformation.of(X509Cert.class)
        ).name("流量元数据中证书的实时数据流").setParallelism(1);
    }
}
