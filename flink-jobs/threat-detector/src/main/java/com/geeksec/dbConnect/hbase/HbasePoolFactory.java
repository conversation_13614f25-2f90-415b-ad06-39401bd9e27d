package com.geeksec.dbConnect.hbase;

import static com.geeksec.analysisFunction.getLabelFlatMap.URCPAlarmFlatMap.*;

import com.geeksec.common.config.ConfigConstants;
import com.geeksec.common.config.ConfigurationManager;
import java.io.IOException;
import java.util.*;
import org.apache.commons.pool.BasePoolableObjectFactory;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
* <AUTHOR>
* @Date  2024/10/9
*/

public class HbasePoolFactory extends BasePoolableObjectFactory<Connection> {

    private static final Logger logger = LoggerFactory.getLogger(HbasePoolFactory.class);
    private static final ParameterTool CONFIG = ConfigurationManager.getConfig();

    public static final String HBASE_QUORUM = CONFIG.get(ConfigConstants.HBASE_QUORUM);
    public static final String HBASE_CLIENT_PORT = CONFIG.get(ConfigConstants.HBASE_CLIENT_PORT);
    public static final String HBASE_MASTER = CONFIG.get(ConfigConstants.HBASE_MASTER);

    @Override
    public Connection makeObject() throws Exception {
        Connection connection = null;
        // Hadoop配置对象，用于创建HBase配置
        Configuration configuration = HBaseConfiguration.create();
        configuration.set("hbase.zookeeper.property.clientPort", HBASE_CLIENT_PORT);
        // 设置zookeeper的主机
        configuration.set("hbase.zookeeper.quorum", HBASE_QUORUM);
        configuration.set("hbase.master", HBASE_MASTER);
        try{
            connection = ConnectionFactory.createConnection(configuration);
        }catch (Exception e){
            logger.error("hbase客户端初始化失败");
        }
        return connection;
    }

    @Override
    public boolean validateObject(Connection connection) {
        return true;
    }

    @Override
    public void activateObject(Connection connection) throws Exception {
//        logger.info("对象被激活了"+restHighLevelClient);
    }

    @Override
    public void destroyObject(Connection connection) throws Exception {
//        logger.info("对象被销毁了"+restHighLevelClient);
    }

    @Override
    public void passivateObject(Connection connection) throws Exception {
//        logger.info("回收并进行钝化操作"+restHighLevelClient);
    }

    public static void main(String[] args) throws IOException {
//        Connection connection = null;
//        // 连接HBase
//        try {
//            Configuration configuration = HBaseConfiguration.create();
//            configuration.set("hbase.zookeeper.property.clientPort", HBASE_CLIENT_PORT);
//            configuration.set("hbase.zookeeper.quorum", HBASE_QUORUM);// 设置zookeeper的主机
//            configuration.set("hbase.master", HBASE_MASTER);
//            connection = ConnectionFactory.createConnection(configuration);
//            Admin admin = connection.getAdmin();
//            Table table = connection.getTable(TableName.valueOf("CERT"));
//            // 检查列族是否存在
//            if (admin.getDescriptor(TableName.valueOf("CERT")).getColumnFamily(Bytes.toBytes("Labels"))!=null) {
//                // 如果列族不存在，则创建列族
//                admin.addColumnFamily(TableName.valueOf("CERT"), ColumnFamilyDescriptorBuilder.newBuilder(Bytes.toBytes("Labels")).build());
//            }
//
////            // 准备Put操作
////            Put put = new Put(Bytes.toBytes("SHA1=a"));
////            put.addColumn(Bytes.toBytes("Labels"), Bytes.toBytes("your_column"), Bytes.toBytes("[1,2,3]"));
////
////            // 执行Put操作
////            table.put(put);
//
//            System.out.println("操作完成");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        Connection connection = null;
        List<Map<String, List<String>>> KnowledgeCollisionResult = new ArrayList<>();

        try {
            Configuration configuration = HBaseConfiguration.create();
            configuration.set("hbase.zookeeper.property.clientPort", HBASE_CLIENT_PORT);
            configuration.set("hbase.zookeeper.quorum", HBASE_QUORUM);// 设置zookeeper的主机
            configuration.set("hbase.master", HBASE_MASTER);
            connection = ConnectionFactory.createConnection(configuration);
            Table ipAPTKnowledgetable = connection.getTable(TableName.valueOf(HBASE_IP_APT_KNOWLEDGE_TABLE_NAME));

            // IP APT 关联 APT 碰撞
            Get APTGet = new Get(Bytes.toBytes("**************"));
            APTGet.addColumn(Bytes.toBytes(APT_COLUMN), Bytes.toBytes(APT_KEY));
            Result APTResult = ipAPTKnowledgetable.get(APTGet);
            if (APTResult.getRow().length > 0) {
                byte[] value = APTResult.getValue(Bytes.toBytes(APT_COLUMN), Bytes.toBytes(APT_KEY));
                if (value != null) {
                    String APTValueStr = Bytes.toString(value);
                    Map<String, List<String>> APTResultMap = new HashMap<>();
                    APTResultMap.put(APT_RESULT_KEY, Arrays.asList(APTValueStr));
                    KnowledgeCollisionResult.add(APTResultMap);
                    logger.info("成功碰撞APT证书知识库数据");
                    // IP APT 打标
//                    return getLabelEdgeRow(IP, SpecProtocolEnum.fromName("APT"));
                } else {
                    System.out.println("APT列没有值");
                }
            }
        } catch (Exception e) {
            logger.error("hbase查询失败，报错：{}", e.toString());
        } finally {
            if (connection != null) {
                connection.close();
            }
        }
    }
}
