package com.geeksec.dbConnect.kafka;

import com.geeksec.analysisFunction.analysisEntity.webshell.KafkaConfig;
import com.geeksec.common.config.ConfigConstants;
import com.geeksec.common.config.ConfigurationManager;
import java.util.HashMap;
import java.util.Map;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.jdbc.JdbcInputFormat;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

/**
 * <AUTHOR>
 * @Date 2024/1/29
 */

public class MySQLStreamBuilder {
    private static final ParameterTool CONFIG = ConfigurationManager.getConfig();

    public static String mysqlHost = CONFIG.get(ConfigConstants.MYSQL_DATABASE_HOST, "");

    public static String mysqlUser = CONFIG.get(ConfigConstants.MYSQL_DATABASE_USER, "");

    public static String mysqlPassword = CONFIG.get(ConfigConstants.MYSQL_DATABASE_PASSWORD, "");

    public static DataStream<Map> getModelSwitchStream(StreamExecutionEnvironment env) {

        final String modelSQl = "select model_id,state from tb_model_info";
        // 在此处使用批处理去mysql中获取原始的模型开关状况，并与后续的kafka流合并
        return env.createInput(JdbcInputFormat.buildJdbcInputFormat()
                .setDrivername("com.mysql.jdbc.Driver")
                .setDBUrl(mysqlHost)
                .setUsername(mysqlUser)
                .setPassword(mysqlPassword)
                .setQuery(modelSQl)
                .setRowTypeInfo(new RowTypeInfo(BasicTypeInfo.INT_TYPE_INFO,BasicTypeInfo.INT_TYPE_INFO))//此处的EqDeg是float类型
                .finish()).map(new MapFunction<Row, Map>() {
            @Override
            public Map map(Row row) throws Exception {
                Map<Object,Object> modelSwitchMap = new HashMap<>();
                modelSwitchMap.put( row.getField(0), row.getField(1));
                return modelSwitchMap;
            }
        }).name("mysql获取模型开关初始数据").setParallelism(1);
    }

    /**
     * 获取初始外发Kafka订阅信息流
     * @param env
     * @return
     */
    public static DataStream<KafkaConfig> getSubscribeStream(StreamExecutionEnvironment env) {

        final String modelSQl = "select ip,port,topic from tb_alarm_order order by id desc limit 1";
        // 在此处使用批处理去mysql中获取原始的模型开关状况，并与后续的kafka流合并
        return env.createInput(JdbcInputFormat.buildJdbcInputFormat()
                .setDrivername("com.mysql.jdbc.Driver")
                .setDBUrl(mysqlHost)
                .setUsername(mysqlUser)
                .setPassword(mysqlPassword)
                .setQuery(modelSQl)
                .setRowTypeInfo(new RowTypeInfo(BasicTypeInfo.STRING_TYPE_INFO,BasicTypeInfo.STRING_TYPE_INFO,BasicTypeInfo.STRING_TYPE_INFO))
                .finish()).flatMap(new RichFlatMapFunction<Row, KafkaConfig>() {
            @Override
            public void flatMap(Row row, Collector<KafkaConfig> collector) throws Exception {
                KafkaConfig config = new KafkaConfig(row.getFieldAs(0), row.getFieldAs(1),row.getFieldAs(2));
                collector.collect(config);
            }
        }).name("mysql获取kafka订阅初始数据").setParallelism(1);
    }
}
