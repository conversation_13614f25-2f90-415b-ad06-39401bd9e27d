package com.geeksec.flinkTool.deserializer;

import com.alibaba.fastjson.JSONObject;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import org.apache.commons.lang3.SerializationException;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2024/1/25
 */

public class modelSwitchDeserializer implements KafkaRecordDeserializationSchema<Map> {

    private static final Logger logger = LoggerFactory.getLogger(modelSwitchDeserializer.class);

    @Override
    public void deserialize(ConsumerRecord<byte[], byte[]> consumerRecord, Collector<Map> collector) throws IOException {
        byte[] values = consumerRecord.value();
        byte[] key = consumerRecord.key();
        String keyString = new String(key, StandardCharsets.UTF_8);
        if (values == null){
            logger.info("modelSwitch值为空");
        }else {
            try {
                JSONObject obj = JSONObject.parseObject(new String(values, StandardCharsets.UTF_8));
                if(keyString.equals("switch_change")){
                    Map<String, Object> infoMap = obj.getInnerMap();
                    collector.collect(infoMap);
                }else {
                    logger.info("消费到了告警数据，topic：{}",keyString);
                }
            } catch (Exception e) {
                throw new SerializationException("Error when serializing Customerto byte[] " + e);
            }
        }
    }

    @Override
    public TypeInformation<Map> getProducedType() {
        return TypeInformation.of(Map.class);
    }
}
