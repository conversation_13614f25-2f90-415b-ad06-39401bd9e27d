package com.geeksec.flinkTool.getWatermarkAndTimestamp;

import org.apache.flink.api.common.eventtime.Watermark;
import org.apache.flink.api.common.eventtime.WatermarkGenerator;
import org.apache.flink.api.common.eventtime.WatermarkOutput;
import org.apache.flink.types.Row;

/**
 * <AUTHOR>
 * @Date 2022/11/4
 */

public class MyEventTimeWaterMarks implements WatermarkGenerator<Row> {
    private long maxTimeStamp;
    private long MyDelay;
    @Override
    public void onEvent(Row row, long eventTimeStamp , WatermarkOutput watermarkOutput) {
        maxTimeStamp = Math.max(maxTimeStamp,eventTimeStamp);
//        System.err.println("maxTimeStamp:" + maxTimeStamp + "eventTimeStamp"+eventTimeStamp);
//        watermarkOutput.emitWatermark(new Watermark(maxTimeStamp-delay));
    }

    @Override
    public void onPeriodicEmit(WatermarkOutput watermarkOutput) {
        Watermark watermark = new Watermark(maxTimeStamp-MyDelay);
        watermarkOutput.emitWatermark(watermark);
//        System.err.println("watermark is: "+watermark);
    }

    public void setDelay(long MyDelay) {
        this.MyDelay = MyDelay;
    }
}
