package com.geeksec.flinkTool.customTrigger;

import java.util.Map;
import org.apache.flink.streaming.api.windowing.triggers.Trigger;
import org.apache.flink.streaming.api.windowing.triggers.TriggerResult;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;

/**
 * <AUTHOR>
 * @Date 2024/9/9
 */

public class ConnectTrigger extends Trigger<Map<String,Object>, TimeWindow> {

    public static final String CONNECTION_END_TYPE = "connect";
    public static final String SSL_TYPE = "ssl";
    public static final String HTTP_TYPE = "http";

    @Override
    public TriggerResult onElement(Map<String, Object> pbMap, long l, TimeWindow timeWindow, TriggerContext triggerContext) throws Exception {

        String type = (String) pbMap.get("type");
        if (CONNECTION_END_TYPE.equals(type)){
            return TriggerResult.FIRE_AND_PURGE;
        }else {
            return TriggerResult.CONTINUE;
        }
    }

    @Override
    public TriggerResult onProcessingTime(long l, TimeWindow timeWindow, TriggerContext triggerContext) throws Exception {
        return TriggerResult.CONTINUE;
    }

    @Override
    public TriggerResult onEventTime(long l, TimeWindow timeWindow, TriggerContext triggerContext) throws Exception {
        return TriggerResult.CONTINUE;
    }

    @Override
    public void clear(TimeWindow timeWindow, TriggerContext ctx) throws Exception {

    }
}
