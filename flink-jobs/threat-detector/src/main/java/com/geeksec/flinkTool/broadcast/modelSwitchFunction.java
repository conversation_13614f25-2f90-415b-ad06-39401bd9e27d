package com.geeksec.flinkTool.broadcast;
import static com.geeksec.analysisFunction.getRowInfo.SslInfoRowFlatMap.ROW_MODEL_MAP;
import static com.geeksec.flinkTool.descriptor.CustomDescriptor.modelSwitchStateDescriptor;

import com.geeksec.analysisFunction.handler.LabelOutPutTagConstant;
import java.util.Map;
import java.util.Objects;
import org.apache.flink.api.common.state.BroadcastState;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class modelSwitchFunction extends BroadcastProcessFunction<Row, Map, Row> {

    protected static final Logger LOG = LoggerFactory.getLogger(modelSwitchFunction.class);

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void close() throws Exception {
        super.close();
    }

    //TODO 跑测试
    @Override
    public void processElement(Row row, BroadcastProcessFunction<Row, Map, Row>.ReadOnlyContext ctx, Collector<Row> collector) throws Exception {

        if (row != null && row.getArity() != 0) {
            String type = row.getField(0).toString(); // 获取sink Row 类型

            BroadcastState<Integer, Integer> stateMap = (BroadcastState<Integer, Integer>) ctx.getBroadcastState(modelSwitchStateDescriptor);

            Integer model_switch = stateMap.get(ROW_MODEL_MAP.get(type));
            // 开关状态默认是1，开启
            if (Objects.isNull(model_switch)) {
                model_switch = 1;
            }
            if (model_switch==1){
                switch (type) {
                    case "SSL_FINGER_INFO":
                        ctx.output(LabelOutPutTagConstant.SSL_FINGER_INFO, row);
                        break;
                    case "SIP_DIP_FINGER_ROW":
                        ctx.output(LabelOutPutTagConstant.SIP_DIP_FINGER_ROW, row);
                        break;
                    case "CLIENT_HTTP_CONNECT_DOMAIN_EDGE":
                        ctx.output(LabelOutPutTagConstant.CLIENT_HTTP_CONNECT_DOMAIN_EDGE, row);
                        break;
                    case "DNS_PARSE_TO_EDGE":
                        ctx.output(LabelOutPutTagConstant.DNS_PARSE_TO_EDGE, row);
                        break;
                    case "CLIENT_QUERY_DOMAIN_EDGE":
                        ctx.output(LabelOutPutTagConstant.CLIENT_QUERY_DOMAIN_EDGE, row);
                        break;
                    case "SERVER_HTTP_CONNECT_DOMAIN_EDGE":
                        ctx.output(LabelOutPutTagConstant.SERVER_HTTP_CONNECT_DOMAIN_EDGE, row);
                        break;
                    case "CLIENT_SSL_CONNECT_DOMAIN_EDGE":
                        ctx.output(LabelOutPutTagConstant.CLIENT_SSL_CONNECT_DOMAIN_EDGE, row);
                        break;
                    case "SERVER_SSL_CONNECT_DOMAIN_EDGE":
                        ctx.output(LabelOutPutTagConstant.SERVER_SSL_CONNECT_DOMAIN_EDGE, row);
                        break;
                    case "http_web_login":
                        ctx.output(LabelOutPutTagConstant.http_webLogin_info, row);
                        break;
                    case "Web_Login_Info":
                        ctx.output(LabelOutPutTagConstant.Web_Login_Info, row);
                        break;
                    case "Port_Scan_Row":
                        ctx.output(LabelOutPutTagConstant.Port_Scan_Row, row);
                        break;
                    case "dns_tunnel_info":
                        ctx.output(LabelOutPutTagConstant.DNS_Tunnel_Row, row);
                        break;
                    case "ConnectInfo_Dns":
                        ctx.output(LabelOutPutTagConstant.ConnectInfo_Dns, row);
                        break;
                    case "Neoregeo_info":
                        ctx.output(LabelOutPutTagConstant.Neoregeo_info, row);
                        break;
                    case "RDP_row":
                        ctx.output(LabelOutPutTagConstant.RDP_info, row);
                        break;
                    case "Oracle_row":
                        ctx.output(LabelOutPutTagConstant.Oracle_info, row);
                        break;
                    case "MYSQL_row":
                        ctx.output(LabelOutPutTagConstant.MYSQL_info, row);
                        break;
                    case "SMB_row":
                        ctx.output(LabelOutPutTagConstant.SMB_info, row);
                        break;
                    case "xRay_Finger_Row":
                        ctx.output(LabelOutPutTagConstant.xRay_Finger_Row, row);
                        break;
                    case "suo5_info":
                        ctx.output(LabelOutPutTagConstant.suo5_info, row);
                        break;
                    case "BeHinder_info":
                        ctx.output(LabelOutPutTagConstant.BeHinder_info, row);
                        break;
                    case "antSword_info":
                        ctx.output(LabelOutPutTagConstant.antSword_info, row);
                        break;
                    case "antSword_php_info":
                        ctx.output(LabelOutPutTagConstant.antSword_php_info, row);
                        break;
                    case "http_tunnel_info":
                        ctx.output(LabelOutPutTagConstant.HTTP_Tunnel_Row, row);
                        break;
                    case "tcp_tunnel_info":
                        ctx.output(LabelOutPutTagConstant.TCP_Tunnel_Row, row);
                        break;
                    case "ntp_tunnel_info":
                        ctx.output(LabelOutPutTagConstant.NTP_Tunnel_Row, row);
                        break;
                    case "ssl_tunnel_info":
                        ctx.output(LabelOutPutTagConstant.SSL_Tunnel_Row, row);
                        break;
                    case "icmp_tunnel_info":
                        ctx.output(LabelOutPutTagConstant.ICMP_Tunnel_Row, row);
                        break;
                    case "srcpInfoRow":
                        ctx.output(LabelOutPutTagConstant.srcpInfoRow, row);
                        break;
                    case "urcpInfoRow":
                        ctx.output(LabelOutPutTagConstant.urcpInfoRow, row);
                        break;
                    case "webShell_info":
                        ctx.output(LabelOutPutTagConstant.webShell_info, row);
                        break;
                    case "encryptedTool_info":
                        ctx.output(LabelOutPutTagConstant.encryptedTool_info, row);
                        break;
                    case "ToDeskRow":
                        ctx.output(LabelOutPutTagConstant.ToDeskRow, row);
                        break;
                    default:
                        break;
                }
            }else{
                LOG.info("__________________——{}——模型未开启__________________",ROW_MODEL_MAP.get(type));
            }
        }
    }

    @Override
    public void processBroadcastElement(Map changeMap, BroadcastProcessFunction<Row, Map, Row>.Context ctx, Collector<Row> collector) throws Exception {
        for(Object modelID:changeMap.keySet()){
            Integer nowSwitchState = (Integer) changeMap.get(modelID);
            BroadcastState<Integer, Integer> stateMap = ctx.getBroadcastState(modelSwitchStateDescriptor);
            if(stateMap.contains((Integer) modelID)) {
                Integer oldState = stateMap.get((Integer) modelID);
                LOG.info(modelID+"存在且状态为: " + oldState);
                LOG.info(modelID+"即将修改状态为" + nowSwitchState);
            } else {
                LOG.info(modelID+"不存在");
                LOG.info(modelID+"即将修改状态为" + nowSwitchState);
            }
            // 更新模型状态
            stateMap.put((Integer) modelID, nowSwitchState);
        }
    }
}