package com.geeksec.flinkTool.customAggr;

import com.geeksec.analysisFunction.analysisEntity.connectRelatedInfo.ConnectBasicInfo;
import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import com.geeksec.analysisFunction.analysisEntity.protocolRelatedInfo.HttpSimpleInfo;
import com.geeksec.analysisFunction.analysisEntity.protocolRelatedInfo.SslSimpleInfo;
import com.geeksec.analysisFunction.analysisEntity.webshell.WebshellInfo;
import com.geeksec.common.utils.DomainUtils;
import com.geeksec.flinkTool.customTrigger.ConnectTrigger;
import java.util.List;
import java.util.Map;
import org.apache.flink.api.common.functions.AggregateFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2024/9/9
 */

public class WebshellAggr implements AggregateFunction<Map<String, Object>, WebshellInfo, WebshellInfo> {

    private static final Logger logger = LoggerFactory.getLogger(WebshellAggr.class);

    @Override
    public WebshellInfo createAccumulator() {
        return new WebshellInfo();
    }

    @Override
    public WebshellInfo add(Map<String, Object> pbMap, WebshellInfo webshellInfo) {
        String type = (String) pbMap.get("type");
        if (ConnectTrigger.CONNECTION_END_TYPE.equals(type)){
            ConnectBasicInfo connectBasicInfo = new ConnectBasicInfo(pbMap);
            webshellInfo.setConnectBasicInfo(connectBasicInfo);

            NeededInfo neededInfo = new NeededInfo(pbMap);
            if (webshellInfo.getNeededInfo() != null){
                neededInfo.setSniDomain(webshellInfo.getNeededInfo().getSniDomain());
                neededInfo.setHttpDomain(webshellInfo.getNeededInfo().getHttpDomain());
            }
            webshellInfo.setNeededInfo(neededInfo);

            return webshellInfo;
        }else if (ConnectTrigger.SSL_TYPE.equals(type)){
            SslSimpleInfo sslSimpleInfo = new SslSimpleInfo(pbMap);
            List<SslSimpleInfo> sslSimpleInfos = webshellInfo.getSslSimpleInfos();
            sslSimpleInfos.add(sslSimpleInfo);
            webshellInfo.setSslSimpleInfos(sslSimpleInfos);

            String sni = (String) pbMap.get("CH_ServerName");
            NeededInfo neededInfo = new NeededInfo(pbMap);
            if(!"".equals(sni)){
                neededInfo.setSniDomain(sni);
            }
            webshellInfo.setNeededInfo(neededInfo);

            return webshellInfo;
        } else if (ConnectTrigger.HTTP_TYPE.equals(type)) {
            HttpSimpleInfo httpSimpleInfo = new HttpSimpleInfo(pbMap);
            List<HttpSimpleInfo> httpSimpleInfos = webshellInfo.getHttpSimpleInfos();
            httpSimpleInfos.add(httpSimpleInfo);
            webshellInfo.setHttpSimpleInfos(httpSimpleInfos);

            String httpDomainAddr = (String) pbMap.get("Host");
            NeededInfo neededInfo = new NeededInfo(pbMap);
            if(DomainUtils.isValidDomain(httpDomainAddr)){
                neededInfo.setHttpDomain(httpDomainAddr);
            }

            return webshellInfo;
        }else {
            logger.error("WebshellAggr中混入了不属于 SSL, HTTP, CONNECT 的数据类型, 不做处理, 直接返回");
            return webshellInfo;
        }
    }

    @Override
    public WebshellInfo getResult(WebshellInfo webshellInfo) { return webshellInfo;}

    @Override
    public WebshellInfo merge(WebshellInfo webshellInfo1, WebshellInfo webshellInfo2) {

        WebshellInfo webshellInfo = new WebshellInfo();
        if (webshellInfo1.getConnectBasicInfo() != null){
            webshellInfo.setConnectBasicInfo(webshellInfo1.getConnectBasicInfo());
        }
        if (webshellInfo2.getConnectBasicInfo() != null){
            webshellInfo.setConnectBasicInfo(webshellInfo2.getConnectBasicInfo());
        }

        List<SslSimpleInfo> sslSimpleInfos1 = webshellInfo1.getSslSimpleInfos();
        List<SslSimpleInfo> sslSimpleInfos2 = webshellInfo2.getSslSimpleInfos();
        sslSimpleInfos1.addAll(sslSimpleInfos2);
        webshellInfo.setSslSimpleInfos(sslSimpleInfos1);

        List<HttpSimpleInfo> httpSimpleInfos1 = webshellInfo1.getHttpSimpleInfos();
        List<HttpSimpleInfo> httpSimpleInfos2 = webshellInfo2.getHttpSimpleInfos();
        httpSimpleInfos1.addAll(httpSimpleInfos2);
        webshellInfo.setHttpSimpleInfos(httpSimpleInfos1);

        NeededInfo neededInfo1 = webshellInfo1.getNeededInfo();
        NeededInfo neededInfo2 = webshellInfo2.getNeededInfo();
        String sni1 = neededInfo1.getSniDomain();
        String http1 = neededInfo1.getHttpDomain();
        if(!"".equals(sni1)){
            neededInfo2.setSniDomain(sni1);
        }
        if(DomainUtils.isValidDomain(http1)){
            neededInfo2.setHttpDomain(http1);
        }
        webshellInfo.setNeededInfo(neededInfo2);

        return webshellInfo;
    }
}
