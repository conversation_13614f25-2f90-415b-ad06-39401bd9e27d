package com.geeksec.common.LabelUtils;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.common.config.ConfigConstants;
import com.geeksec.common.config.ConfigurationManager;
import com.geeksec.dbConnect.poolFactory.ESPoolFactory;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.commons.pool.impl.GenericObjectPool;
import org.apache.flink.api.java.utils.ParameterTool;
import org.elasticsearch.ElasticsearchSecurityException;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.IndexNotFoundException;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2022/10/26
 */
@Slf4j
public class EsUtils {
    private static final ParameterTool CONFIG = ConfigurationManager.getConfig();
    public static final String TASK_ID = CONFIG.get(ConfigConstants.ES_TASK_ID);
    public static final String BATCH_ID = CONFIG.get(ConfigConstants.ES_BATCH_ID);
    public static final String WS_HOST = CONFIG.get(ConfigConstants.ES_WS_HOST);
    public static GenericObjectPool<RestHighLevelClient> initEsPool(){
        GenericObjectPool.Config EsPoolConfig = new GenericObjectPool.Config();
        EsPoolConfig.maxActive=10000;
        EsPoolConfig.maxIdle=10000;
        EsPoolConfig.maxWait=10000;
        EsPoolConfig.minIdle=100;
        EsPoolConfig.testOnBorrow=false;
        EsPoolConfig.testOnReturn=false;
        EsPoolConfig.whenExhaustedAction=1;
        ESPoolFactory esPoolFactory = new ESPoolFactory();
        return new GenericObjectPool<>(esPoolFactory,EsPoolConfig);
    }

    public static RestHighLevelClient getClient(GenericObjectPool<RestHighLevelClient> clientPool) throws Exception{
        RestHighLevelClient restHighLevelClient = clientPool.borrowObject();
//        logger.info("从池中取一个对象"+restHighLevelClient);
        return restHighLevelClient;
    }

    public static void returnClient(RestHighLevelClient client,GenericObjectPool<RestHighLevelClient> clientPool) throws Exception{
//        logger.info("使用完毕后，归还对象"+client);
        clientPool.returnObject(client);
    }

    //批量写入
    public static void createMapDocumentBulk(String index, List<JSONObject> certJsonList,RestHighLevelClient client) {
        BulkRequest request = new BulkRequest();
        try {
            for (JSONObject certJson : certJsonList) {
                IndexRequest indexRequest = new IndexRequest();
                request.add(indexRequest.index(index).source(certJson));
            }

            client.bulk(request, RequestOptions.DEFAULT);
        } catch (ElasticsearchSecurityException | IOException e) {
            e.printStackTrace();
        }
    }

    public static HashMap<String, Map> queryCACert(String index, String notAfter, String notBefore,
                                            String measures, String field, String queryString,
                                            String subjectMD5, String issuerMD5,RestHighLevelClient client) {

        HashMap<String, Map> result = new HashMap<>();
        Integer number = 0;
        SearchResponse response = null;

        //具有CA权限的
        RegexpQueryBuilder regexpQueryBuilder = QueryBuilders.regexpQuery("Extension" + "." + "basicConstraints" + ".keyword", ".*CA:TRUE.*");

        //大于等于父证书NotAfter的
        RangeQueryBuilder notAfterRange = QueryBuilders.rangeQuery("NotAfter");
        notAfterRange.gte(notAfter);
        //小于等父证书NotBefore
        RangeQueryBuilder notBeforeRange = QueryBuilders.rangeQuery("NotBefore");
        notBeforeRange.lte(notBefore);

        //父证书匹配规则
        TermQueryBuilder termQueryBuilder = QueryBuilders.termQuery(measures + "." + field + ".keyword", queryString);
        TermQueryBuilder termQueryBuilder2 = QueryBuilders.termQuery(subjectMD5, issuerMD5);

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                .must(regexpQueryBuilder)
                .must(notBeforeRange)
                .must(notAfterRange)
                .should(termQueryBuilder)
                .should(termQueryBuilder2)
                .minimumShouldMatch("1");//如果同时有should和must的话，should就代表有或者没有都可以，故使用MiniMumShouldMatch来至少满足一项should

        try {
            SearchRequest request = new SearchRequest();
            request.indices(index);

            SearchSourceBuilder builder = new SearchSourceBuilder();
            builder.query(boolQuery);
            request.source(builder);

            response = client.search(request, RequestOptions.DEFAULT);

        } catch (ElasticsearchSecurityException e) {
            log.error("ES is abnormal during the search, query content is: " + "");
            return result;
        } catch (IndexNotFoundException e1) {
            log.error("index not found exception: " + index);
            return result;
        } catch (IOException e) {
            e.printStackTrace();
        }

        SearchHits searchHits = response.getHits();
        log.debug("Match query: [" + measures + "." + field + "] [" + queryString + "].");
        log.debug("Total match found:" + searchHits.getTotalHits());
        SearchHit[] hits = searchHits.getHits();
        for (SearchHit searchHit : hits) {
            number += 1;
            result.put((String) searchHit.getSourceAsMap().get("ASN1SHA1"), searchHit.getSourceAsMap());
        }
        return result;
    }

    public static HashMap<String, Map> matchQuery(String indices, String field, String queryString,
                                           String notField, String notValue,RestHighLevelClient client) {

        SearchResponse response = null;
        HashMap<String, Map> result = new HashMap<>();

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery(field, queryString));

        if (notField != null) {
            boolQueryBuilder.mustNot(QueryBuilders.termQuery(notField, notValue));
        }

        try {
            SearchRequest request = new SearchRequest();
            request.indices(indices);

            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(boolQueryBuilder);
            request.source(sourceBuilder);
            response = client.search(request, RequestOptions.DEFAULT);

            SearchHits searchHits = response.getHits();
            log.debug("Match query: [" + field + "] [" + queryString + "].");
            log.debug("Total match found:" + searchHits.getTotalHits());
            SearchHit[] hits = searchHits.getHits();
            Integer number = 0;
            for (SearchHit searchHit : hits) {
                number += 1;
                result.put((String) searchHit.getSourceAsMap().get("ASN1SHA1"), searchHit.getSourceAsMap());
            }
        } catch (ElasticsearchSecurityException e) {
            log.error("ES is abnormal during the search, query content is: " + queryString);
            return result;
        } catch (IndexNotFoundException e1) {
            log.error("index not found exception: " + indices);
            return result;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    public static HashMap<String, Map> nestedQuery(String indices, String measures, String subjectMD5,
                                            String issuerMD5, String field, String queryString,
                                            String notField, String notValue,RestHighLevelClient client) {
        HashMap<String, Map> result = new HashMap<>();
        Integer number = 0;
        SearchResponse response = null;

        TermQueryBuilder termQueryBuilder = QueryBuilders.termQuery(measures + "." + field + ".keyword", queryString);
        TermQueryBuilder termQueryBuilder2 = QueryBuilders.termQuery(subjectMD5, issuerMD5);
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                .should(termQueryBuilder)
                .should(termQueryBuilder2);

        if (notField != null) {
            boolQuery.mustNot(QueryBuilders.termQuery(notField, notValue));
        }

        try {
            SearchRequest request = new SearchRequest();
            request.indices(indices);

            SearchSourceBuilder builder = new SearchSourceBuilder();
            builder.query(boolQuery);
            request.source(builder);

            response = client.search(request, RequestOptions.DEFAULT);

        } catch (ElasticsearchSecurityException e) {
            log.error("ES is abnormal during the search, query content is: " + queryString);
            return result;
        } catch (IndexNotFoundException e1) {
            log.error("index not found exception: " + indices);
            return result;
        } catch (IOException e) {
            e.printStackTrace();
        }

        SearchHits searchHits = response.getHits();
        log.debug("Match query: [" + measures + "." + field + "] [" + queryString + "].");
        log.debug("Total match found:" + searchHits.getTotalHits());
        SearchHit[] hits = searchHits.getHits();
        System.out.println(searchHits.getTotalHits());
        for (SearchHit searchHit : hits) {
            number += 1;
            result.put((String) searchHit.getSourceAsMap().get("ASN1SHA1"), searchHit.getSourceAsMap());
        }
        return result;
    }

    public static JSONObject AddTagToSession(String sessionId, Set<String> TagId, String esKey){
        HashMap<String,String> addTagMap = new HashMap<>();
        addTagMap.put("TargetName",sessionId);
        addTagMap.put("Time",String.valueOf(System.currentTimeMillis()/1000));
        addTagMap.put("es_key",esKey);
        JSONObject AddTagJsonInfo = new JSONObject();
        AddTagJsonInfo.putAll(addTagMap);
        AddTagJsonInfo.put("TagId",TagId);
        return AddTagJsonInfo;
    }

    public static Boolean match_URL_Query(String URI,RestHighLevelClient client) {

        SearchResponse response = null;

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("HTTP.Url", URI));
        long halfHourAgoMillis = Instant.now().minusSeconds(1800).getEpochSecond(); // 计算当前时间半小时前的时间戳
        boolQueryBuilder.must(QueryBuilders.rangeQuery("StartTime").gte(halfHourAgoMillis)); // 将范围查询添加为"must"查询条件
        LocalDate date = LocalDate.now(); // 获取当前日期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String formattedDate = date.format(formatter); // 将日期格式化为字符串
        String indices = "connectinfo_*_"+formattedDate+"_*";
        try {
            SearchRequest request = new SearchRequest();
            request.indices(indices);

            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(boolQueryBuilder);
            request.source(sourceBuilder);
            response = client.search(request, RequestOptions.DEFAULT);

            SearchHits searchHits = response.getHits();
            long totalHits = searchHits.getTotalHits().value; // 获取匹配文档数量
            log.debug("Match query: [" + "HTTP.Url" + "] [" + URI + "].");
            log.debug("Total match found:" + searchHits.getTotalHits());
            if (totalHits==0){
                return true;
            }else {
                return false;
            }

        } catch (ElasticsearchSecurityException e) {
            log.error("ES is abnormal during the search, query content is: " + URI);
            return false;
        } catch (IndexNotFoundException e1) {
            log.error("index not found exception: " + indices);
            return false;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    public static void write_tag(JSONObject send_json){
        HttpUtils.sendPost_only(WS_HOST,send_json);
    }

}
