package com.geeksec.common.LabelUtils;

import com.geeksec.common.config.ConfigConstants;
import com.geeksec.common.config.ConfigurationManager;
import com.mysql.cj.jdbc.MysqlDataSource;
import java.sql.Connection;
import java.sql.SQLException;
import javax.sql.DataSource;
import org.apache.flink.api.java.utils.ParameterTool;

/**
 * <AUTHOR>
 * @Date 2023/3/17
 */


public class mysqlPool {
    private static final ParameterTool CONFIG = ConfigurationManager.getConfig();

    public static String mysqlHost = CONFIG.get(ConfigConstants.MYSQL_DATABASE_HOST, "");

    public static String mysqlUser = CONFIG.get(ConfigConstants.MYSQL_DATABASE_USER, "");

    public static String mysqlPassword = CONFIG.get(ConfigConstants.MYSQL_DATABASE_PASSWORD, "");
    private static DataSource dataSource;

    static {
        MysqlDataSource mysqlDS = new MysqlDataSource();
        mysqlDS.setURL(mysqlHost);
        mysqlDS.setUser(mysqlUser);
        mysqlDS.setPassword(mysqlPassword);
        dataSource = mysqlDS;
    }

    public static Connection getConnection() throws SQLException {
        return dataSource.getConnection();
    }
}
