package com.geeksec.nta.graphbuilder.extractor.ssl;

import com.geeksec.common.model.dpianalysis.ssl.SslIndicator;
import com.geeksec.common.model.dpianalysis.ssl.SslSessionLog;
import com.geeksec.nta.graphbuilder.utils.net.DomainUtils;
import com.geeksec.nta.graphbuilder.model.EdgeDirection;
import com.geeksec.nta.graphbuilder.model.edge.EdgeType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.util.Collector;

import com.geeksec.nta.graphbuilder.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.graphbuilder.model.edge.BaseEdge;
import com.geeksec.nta.graphbuilder.model.edge.ClientReceivesCertEdge;
import com.geeksec.nta.graphbuilder.model.edge.DomainDerivesFromBaseDomainEdge;
import com.geeksec.nta.graphbuilder.model.edge.IpPresentsFingerprintEdge;
import com.geeksec.nta.graphbuilder.model.edge.FingerprintAppearsWithCertEdge;
import com.geeksec.nta.graphbuilder.model.edge.FingerprintAppearsWithDomainEdge;
import com.geeksec.nta.graphbuilder.model.edge.IpProvidesCertEdge;

import lombok.extern.slf4j.Slf4j;

/**
 * SSL边提取器
 * 从SSL数据中提取边关系数据，包括证书使用关系、指纹关联关系等
 *
 * <AUTHOR> Team
 */
@Slf4j
public class SslEdgeExtractor extends BaseEdgeExtractor {

    /**
     * 从SSL信息中生成各类边关系
     *
     * @param pbMap 数据映射
     * @param collector 收集器
     * @throws Exception 如果处理失败
     */
    @Override
    public void flatMap(Map<String, Object> pbMap, Collector<Map<String, Object>> collector) throws Exception {
        // 获取SSL信息列表
        List<HashMap<String, Object>> sslInfoList = (List<HashMap<String, Object>>) pbMap.get("SSL");
        if (CollectionUtils.isEmpty(sslInfoList)) {
            return;
        }

        String clientIp = (String) pbMap.get("sIp");
        String serverIp = (String) pbMap.get("dIp");

        // 存储所有边
        List<BaseEdge> clientTlsRequestsDomainEdges = new ArrayList<>();
        List<BaseEdge> serverTlsServesDomainEdges = new ArrayList<>();
        List<IpProvidesCertEdge> clientUsesCertEdges = new ArrayList<>();
        List<IpProvidesCertEdge> serverServesWithCertEdges = new ArrayList<>();
        List<ClientReceivesCertEdge> clientVerifiesCertEdges = new ArrayList<>();
        List<BaseEdge> clientUsesFingerEdges = new ArrayList<>();
        List<BaseEdge> serverServesWithFingerEdges = new ArrayList<>();
        List<BaseEdge> certServesSniEdges = new ArrayList<>();
        List<FingerprintAppearsWithCertEdge> fingerLinksCertEdges = new ArrayList<>();
        List<FingerprintAppearsWithDomainEdge> fingerLinksDomainEdges = new ArrayList<>();
        List<BaseEdge> sniBindsToBaseDomainEdges = new ArrayList<>();

        // 处理每个SSL信息
        for (HashMap<String, Object> sslMap : sslInfoList) {
            String sni = (String) sslMap.get("CH_ServerName");
            List<String> clientCertHashes = (List<String>) sslMap.get("sCertHash");
            List<String> serverCertHashes = (List<String>) sslMap.get("dCertHash");
            String clientFingerprint = (String) sslMap.get("sJa3");
            String serverFingerprint = (String) sslMap.get("dJa3");

            // 1. 客户端TLS请求域名边
            if (!StringUtil.isNullOrEmpty(sni) && DomainUtils.isValidDomain(sni) && !StringUtil.isNullOrEmpty(clientIp)) {
                // 处理域名中可能包含的端口
                if (sni.contains(":")) {
                    sni = sni.split(":")[0];
                }

                BaseEdge clientTlsRequestsDomainEdge = new BaseEdge() {
                    @Override
                        public EdgeType getEdgeType() {
                            return EdgeType.CLIENT_TLS_REQUESTS_DOMAIN;
                        }
                };
                clientTlsRequestsDomainEdge.setSrcId(clientIp);
                clientTlsRequestsDomainEdge.setDstId(sni);
                clientTlsRequestsDomainEdges.add(clientTlsRequestsDomainEdge);
            }

            // 2. 服务端TLS托管域名边
            if (!StringUtil.isNullOrEmpty(sni) && DomainUtils.isValidDomain(sni) && !StringUtil.isNullOrEmpty(serverIp)) {
                // 处理域名中可能包含的端口
                if (sni.contains(":")) {
                    sni = sni.split(":")[0];
                }

                BaseEdge serverTlsHostsDomainEdge = new BaseEdge() {
                    @Override
                        public EdgeType getEdgeType() {
                            return EdgeType.SERVER_TLS_HOSTS_DOMAIN;
                        }
                };
                serverTlsHostsDomainEdge.setSrcId(serverIp);
                serverTlsHostsDomainEdge.setDstId(sni);
                serverTlsServesDomainEdges.add(serverTlsHostsDomainEdge);
            }

            // 3. 客户端提供证书边
            if (!StringUtil.isNullOrEmpty(clientIp) && CollectionUtils.isNotEmpty(clientCertHashes)) {
                for (String certHash : clientCertHashes) {
                    IpProvidesCertEdge clientProvidesCertEdge = new IpProvidesCertEdge();
                    clientProvidesCertEdge.setSrcId(clientIp);
                    clientProvidesCertEdge.setDstId(certHash);
                    clientProvidesCertEdge.setRole("client");
                    clientProvidesCertEdge.setSni(sni);
                    clientUsesCertEdges.add(clientProvidesCertEdge);
                }
            }

            // 4. 服务端提供证书边
            if (!StringUtil.isNullOrEmpty(serverIp) && CollectionUtils.isNotEmpty(serverCertHashes)) {
                for (String certHash : serverCertHashes) {
                    IpProvidesCertEdge serverProvidesCertEdge = new IpProvidesCertEdge();
                    serverProvidesCertEdge.setSrcId(serverIp);
                    serverProvidesCertEdge.setDstId(certHash);
                    serverProvidesCertEdge.setRole("server");
                    serverProvidesCertEdge.setSni(sni);
                    serverServesWithCertEdges.add(serverProvidesCertEdge);
                }
            }

            // 5. 客户端接收证书边
            if (!StringUtil.isNullOrEmpty(clientIp) && CollectionUtils.isNotEmpty(serverCertHashes)) {
                for (String certHash : serverCertHashes) {
                    ClientReceivesCertEdge clientVerifiesCertEdge = new ClientReceivesCertEdge();
                    clientVerifiesCertEdge.setSrcId(clientIp);
                    clientVerifiesCertEdge.setDstId(certHash);
                    clientVerifiesCertEdge.setSni(sni);
                    clientVerifiesCertEdges.add(clientVerifiesCertEdge);
                }
            }

            // 6. 客户端呈现TLS指纹边
            if (!StringUtil.isNullOrEmpty(clientIp) && !StringUtil.isNullOrEmpty(clientFingerprint)) {
                IpPresentsFingerprintEdge clientExhibitsFingerEdge = new IpPresentsFingerprintEdge();
                clientExhibitsFingerEdge.setSrcId(clientIp);
                clientExhibitsFingerEdge.setDstId(clientFingerprint);
                // 1表示客户端
                clientExhibitsFingerEdge.setRole(1);
                clientUsesFingerEdges.add(clientExhibitsFingerEdge);
            }

            // 7. 服务端呈现TLS指纹边
            if (!StringUtil.isNullOrEmpty(serverIp) && !StringUtil.isNullOrEmpty(serverFingerprint)) {
                IpPresentsFingerprintEdge serverExhibitsFingerEdge = new IpPresentsFingerprintEdge();
                serverExhibitsFingerEdge.setSrcId(serverIp);
                serverExhibitsFingerEdge.setDstId(serverFingerprint);
                // 2表示服务端
                serverExhibitsFingerEdge.setRole(2);
                serverServesWithFingerEdges.add(serverExhibitsFingerEdge);
            }

            // 8. 证书服务于SNI域名边
            if (!StringUtil.isNullOrEmpty(sni) && DomainUtils.isValidDomain(sni) && CollectionUtils.isNotEmpty(serverCertHashes)) {
                // 处理域名中可能包含的端口
                if (sni.contains(":")) {
                    sni = sni.split(":")[0];
                }

                for (String certHash : serverCertHashes) {
                    BaseEdge certServesSniEdge = new BaseEdge() {
                        @Override
                        public EdgeType getEdgeType() {
                            return EdgeType.CERT_SERVES_SNI;
                        }
                    };
                    // 注意：关系方向从证书指向域名
                    certServesSniEdge.setSrcId(certHash);
                    certServesSniEdge.setDstId(sni);
                    certServesSniEdges.add(certServesSniEdge);
                }
            }

            // 9. TLS指纹关联证书边
            if (!StringUtil.isNullOrEmpty(clientFingerprint) && CollectionUtils.isNotEmpty(serverCertHashes)) {
                for (String certHash : serverCertHashes) {
                    FingerprintAppearsWithCertEdge fingerLinksCertEdge = new FingerprintAppearsWithCertEdge();
                    fingerLinksCertEdge.setSrcId(clientFingerprint);
                    fingerLinksCertEdge.setDstId(certHash);
                    fingerLinksCertEdge.setSni(sni);
                    fingerLinksCertEdges.add(fingerLinksCertEdge);
                }
            }

            // 10. TLS指纹关联域名边
            if (!StringUtil.isNullOrEmpty(clientFingerprint) && !StringUtil.isNullOrEmpty(sni) && DomainUtils.isValidDomain(sni)) {
                // 处理域名中可能包含的端口
                if (sni.contains(":")) {
                    sni = sni.split(":")[0];
                }

                FingerprintAppearsWithDomainEdge fingerprintAppearsWithDomainEdge = new FingerprintAppearsWithDomainEdge();
                fingerprintAppearsWithDomainEdge.setSrcId(clientFingerprint);
                fingerprintAppearsWithDomainEdge.setDstId(sni);
                fingerLinksDomainEdges.add(fingerprintAppearsWithDomainEdge);
            }

            // 11. 域名派生自基础域名边（SNI域名）
            if (!StringUtil.isNullOrEmpty(sni) && DomainUtils.isValidDomain(sni)) {
                // 处理域名中可能包含的端口
                if (sni.contains(":")) {
                    sni = sni.split(":")[0];
                }

                String baseDomainAddr = getBaseDomain(sni);
                if (!StringUtil.isNullOrEmpty(baseDomainAddr)) {
                    DomainDerivesFromBaseDomainEdge domainDerivesFromBaseDomainEdge = new DomainDerivesFromBaseDomainEdge();
                    domainDerivesFromBaseDomainEdge.setSrcId(sni);
                    domainDerivesFromBaseDomainEdge.setDstId(baseDomainAddr);
                    // 设置为SNI域名
                    domainDerivesFromBaseDomainEdge.setSni(true);
                    sniBindsToBaseDomainEdges.add(domainDerivesFromBaseDomainEdge);
                }
            }
        }

        // 合并由SSL元数据产出的边信息
        HashMap<String, Object> resultMap = new HashMap<>(12);
        resultMap.put("clientTlsRequestsDomainEdges", clientTlsRequestsDomainEdges);
        resultMap.put("serverTlsServesDomainEdges", serverTlsServesDomainEdges);
        resultMap.put("clientUsesCertEdges", clientUsesCertEdges);
        resultMap.put("serverServesWithCertEdges", serverServesWithCertEdges);
        resultMap.put("clientVerifiesCertEdges", clientVerifiesCertEdges);
        resultMap.put("clientUsesFingerEdges", clientUsesFingerEdges);
        resultMap.put("serverServesWithFingerEdges", serverServesWithFingerEdges);
        resultMap.put("certServesSniEdges", certServesSniEdges);
        resultMap.put("fingerLinksCertEdges", fingerLinksCertEdges);
        resultMap.put("fingerLinksDomainEdges", fingerLinksDomainEdges);
        resultMap.put("sniBindsToBaseDomainEdges", sniBindsToBaseDomainEdges);

        collector.collect(resultMap);
    }
}
