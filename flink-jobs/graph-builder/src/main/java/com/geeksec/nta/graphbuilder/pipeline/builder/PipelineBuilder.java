package com.geeksec.nta.graphbuilder.pipeline.builder;

import java.util.Map;

import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.types.Row;

import com.geeksec.nta.graphbuilder.config.GraphBuilderConfig;
import com.geeksec.nta.graphbuilder.converter.edge.CertificateEdgeConverter;
import com.geeksec.nta.graphbuilder.converter.edge.SessionEdgeConverter;
import com.geeksec.nta.graphbuilder.converter.edge.DomainEdgeConverter;
import com.geeksec.nta.graphbuilder.converter.edge.HttpEdgeConverter;
import com.geeksec.nta.graphbuilder.converter.vertex.CertificateVertexConverter;
import com.geeksec.nta.graphbuilder.converter.vertex.SessionVertexConverter;
import com.geeksec.nta.graphbuilder.converter.vertex.DomainVertexConverter;
import com.geeksec.nta.graphbuilder.converter.vertex.HttpVertexConverter;
import com.geeksec.nta.graphbuilder.extractor.dns.DnsEdgeExtractor;
import com.geeksec.nta.graphbuilder.extractor.dns.DnsVertexExtractor;
import com.geeksec.nta.graphbuilder.extractor.http.HttpEdgeExtractor;
import com.geeksec.nta.graphbuilder.extractor.http.HttpVertexExtractor;
import com.geeksec.nta.graphbuilder.extractor.session.SessionEdgeExtractor;
import com.geeksec.nta.graphbuilder.extractor.session.SessionVertexExtractor;
import com.geeksec.nta.graphbuilder.extractor.ssl.SslEdgeExtractor;
import com.geeksec.nta.graphbuilder.extractor.ssl.SslVertexExtractor;
import com.geeksec.nta.graphbuilder.io.sink.NebulaSinkWrapper;
import com.geeksec.nta.graphbuilder.io.sink.tag.NebulaGraphOutputTag;
import com.geeksec.nta.graphbuilder.io.source.KafkaSourceFactory;


import lombok.extern.slf4j.Slf4j;

/**
 * 流水线构建器
 * 负责构建和配置图数据处理流水线
 *
 * <AUTHOR> Team
 */
@Slf4j
public class PipelineBuilder {

    /** 流执行环境 */
    private final StreamExecutionEnvironment env;

    /**
     * 构造函数
     *
     * @param env 流执行环境
     */
    public PipelineBuilder(StreamExecutionEnvironment env) {
        this.env = env;
    }

    /**
     * 构建流水线
     * 配置数据源、处理逻辑和数据汇
     *
     * @return 流水线构建器
     * @throws Exception 如果构建失败
     */
    public PipelineBuilder build() throws Exception {
        // 配置数据源
        configureSources();

        return this;
    }

    /**
     * 配置数据源
     * 从Kafka读取各类数据
     *
     * @throws Exception 如果配置失败
     */
    private void configureSources() throws Exception {
        // 1. 会话信息流 - 包含IP、MAC、应用等会话元数据
        DataStream<Map<String, Object>> connectInfoStream = KafkaSourceFactory.getTypedKafkaStream(
                env,
                GraphBuilderConfig.getKafkaConnectTopic(),
                GraphBuilderConfig.getKafkaGroupId() + "-connect",
                "Connect Info");

        // 2. DNS信息流 - 包含域名解析信息
        DataStream<Map<String, Object>> dnsInfoStream = KafkaSourceFactory.getTypedKafkaStream(
                env,
                GraphBuilderConfig.getKafkaDnsTopic(),
                GraphBuilderConfig.getKafkaGroupId() + "-dns",
                "DNS Info");

        // 3. HTTP信息流 - 包含HTTP请求信息
        DataStream<Map<String, Object>> httpInfoStream = KafkaSourceFactory.getTypedKafkaStream(
                env,
                GraphBuilderConfig.getKafkaHttpTopic(),
                GraphBuilderConfig.getKafkaGroupId() + "-http",
                "HTTP Info");

        // 4. SSL信息流 - 包含SSL/TLS连接信息
        DataStream<Map<String, Object>> sslInfoStream = KafkaSourceFactory.getTypedKafkaStream(
                env,
                GraphBuilderConfig.getKafkaSslTopic(),
                GraphBuilderConfig.getKafkaGroupId() + "-ssl",
                "SSL Info");

        // 处理会话信息
        processSession(connectInfoStream);

        // 处理DNS信息
        processDnsInfo(dnsInfoStream);

        // 处理HTTP信息
        processHttpInfo(httpInfoStream);

        // 处理SSL信息
        processSslInfo(sslInfoStream);
    }

    /**
     * 处理会话信息
     * 提取顶点和边数据
     *
     * @param sessionStream 会话信息流
     */
    private void processSession(DataStream<Map<String, Object>> sessionStream) {
        // 提取顶点数据
        DataStream<Map<String, Object>> sessionVertexStream = sessionStream
                .flatMap(new SessionVertexExtractor())
                .name("会话顶点提取")
                .setParallelism(GraphBuilderConfig.getProcessParallelism());

        // 提取边数据
        DataStream<Map<String, Object>> sessionEdgeStream = sessionStream
                .flatMap(new SessionEdgeExtractor())
                .name("会话边提取")
                .setParallelism(GraphBuilderConfig.getProcessParallelism());

        // 转换顶点数据为Row格式
        SingleOutputStreamOperator<Row> sessionVertexRouted = sessionVertexStream
                .process(new SessionVertexConverter())
                .name("会话连接顶点转换和分流")
                .setParallelism(GraphBuilderConfig.getProcessParallelism());

        // 转换边数据为Row格式并直接分流
        SingleOutputStreamOperator<Row> sessionEdgeRouted = sessionEdgeStream
                .process(new SessionEdgeConverter())
                .name("会话连接边转换和分流")
                .setParallelism(GraphBuilderConfig.getProcessParallelism());

        // 配置图数据写入 - 会话顶点
        NebulaSinkWrapper.createIpVertexSink(sessionVertexRouted.getSideOutput(NebulaGraphOutputTag.Vertex.IP));
        NebulaSinkWrapper.createMacVertexSink(sessionVertexRouted.getSideOutput(NebulaGraphOutputTag.Vertex.DEVICE));
        NebulaSinkWrapper.createAppVertexSink(sessionVertexRouted.getSideOutput(NebulaGraphOutputTag.Vertex.APPLICATION));
        NebulaSinkWrapper.createDeviceVertexSink(sessionVertexRouted.getSideOutput(NebulaGraphOutputTag.Vertex.DEVICE));

        // 配置图数据写入 - 会话边
        NebulaSinkWrapper.createIpMapsToMacEdgeSink(sessionEdgeRouted.getSideOutput(NebulaGraphOutputTag.Edge.DEVICE_CONNECT));
        NebulaSinkWrapper.createIpConnectsToIpEdgeSink(sessionEdgeRouted.getSideOutput(NebulaGraphOutputTag.Edge.IP_CONNECT));
        NebulaSinkWrapper.createMacConnectsToMacEdgeSink(sessionEdgeRouted.getSideOutput(NebulaGraphOutputTag.Edge.DEVICE_CONNECT));
        NebulaSinkWrapper.createClientAccessesAppEdgeSink(sessionEdgeRouted.getSideOutput(NebulaGraphOutputTag.Edge.APPLICATION_CONNECT));
        NebulaSinkWrapper.createAppDeploysOnServerEdgeSink(sessionEdgeRouted.getSideOutput(NebulaGraphOutputTag.Edge.APPLICATION_CONNECT));
    }

    /**
     * 处理DNS信息
     * 提取顶点和边数据
     *
     * @param dnsInfoStream DNS信息流
     */
    private void processDnsInfo(DataStream<Map<String, Object>> dnsInfoStream) {
        // 提取顶点数据
        DataStream<Map<String, Object>> dnsVertexStream = dnsInfoStream
                .flatMap(new DnsVertexExtractor())
                .name("DNS顶点提取")
                .setParallelism(GraphBuilderConfig.getProcessParallelism());

        // 提取边数据
        DataStream<Map<String, Object>> dnsEdgeStream = dnsInfoStream
                .flatMap(new DnsEdgeExtractor())
                .name("DNS边提取")
                .setParallelism(GraphBuilderConfig.getProcessParallelism());

        // 转换顶点数据为Row格式并直接分流
        SingleOutputStreamOperator<Row> dnsVertexRouted = dnsVertexStream
                .process(new DomainVertexConverter())
                .name("DNS域名顶点转换和分流")
                .setParallelism(GraphBuilderConfig.getProcessParallelism());

        // 转换边数据为Row格式并直接分流
        SingleOutputStreamOperator<Row> dnsEdgeRouted = dnsEdgeStream
                .process(new DomainEdgeConverter())
                .name("DNS域名边转换和分流")
                .setParallelism(GraphBuilderConfig.getProcessParallelism());

        // 配置图数据写入 - DNS顶点
        NebulaSinkWrapper.createIpVertexSink(dnsVertexRouted.getSideOutput(NebulaGraphOutputTag.Vertex.IP));
        NebulaSinkWrapper.createDomainVertexSink(dnsVertexRouted.getSideOutput(NebulaGraphOutputTag.Vertex.DOMAIN));
        NebulaSinkWrapper.createBaseDomainVertexSink(dnsVertexRouted.getSideOutput(NebulaGraphOutputTag.Vertex.BASE_DOMAIN));

        // 配置图数据写入 - DNS边
        NebulaSinkWrapper.createClientQueriesDomainEdgeSink(dnsEdgeRouted.getSideOutput(NebulaGraphOutputTag.Edge.DOMAIN_ACCESS));
        NebulaSinkWrapper.createDomainResolvesToIpEdgeSink(dnsEdgeRouted.getSideOutput(NebulaGraphOutputTag.Edge.DOMAIN_RESOLVE));
    }

    /**
     * 处理HTTP信息
     * 提取顶点和边数据
     *
     * @param httpInfoStream HTTP信息流
     */
    private void processHttpInfo(DataStream<Map<String, Object>> httpInfoStream) {
        // 提取顶点数据
        DataStream<Map<String, Object>> httpVertexStream = httpInfoStream
                .flatMap(new HttpVertexExtractor())
                .name("HTTP顶点提取")
                .setParallelism(GraphBuilderConfig.getProcessParallelism());

        // 提取边数据
        DataStream<Map<String, Object>> httpEdgeStream = httpInfoStream
                .flatMap(new HttpEdgeExtractor())
                .name("HTTP边提取")
                .setParallelism(GraphBuilderConfig.getProcessParallelism());

        // 转换顶点数据为Row格式并直接分流
        SingleOutputStreamOperator<Row> httpVertexRouted = httpVertexStream
                .process(new HttpVertexConverter())
                .name("HTTP顶点转换和分流")
                .setParallelism(GraphBuilderConfig.getProcessParallelism());

        // 转换边数据为Row格式并直接分流
        SingleOutputStreamOperator<Row> httpEdgeRouted = httpEdgeStream
                .process(new HttpEdgeConverter())
                .name("HTTP边转换和分流")
                .setParallelism(GraphBuilderConfig.getProcessParallelism());

        // 配置图数据写入 - HTTP顶点
        NebulaSinkWrapper.createIpVertexSink(httpVertexRouted.getSideOutput(NebulaGraphOutputTag.Vertex.IP));
        NebulaSinkWrapper.createDomainVertexSink(httpVertexRouted.getSideOutput(NebulaGraphOutputTag.Vertex.DOMAIN));
        NebulaSinkWrapper.createUrlVertexSink(httpVertexRouted.getSideOutput(NebulaGraphOutputTag.Vertex.URL));
        NebulaSinkWrapper.createDeviceVertexSink(httpVertexRouted.getSideOutput(NebulaGraphOutputTag.Vertex.DEVICE));

        // 配置图数据写入 - HTTP边
        NebulaSinkWrapper.createClientHttpRequestsDomainEdgeSink(httpEdgeRouted.getSideOutput(NebulaGraphOutputTag.Edge.DOMAIN_ACCESS));
        NebulaSinkWrapper.createServerHttpServesDomainEdgeSink(httpEdgeRouted.getSideOutput(NebulaGraphOutputTag.Edge.DOMAIN_ACCESS));
        NebulaSinkWrapper.createUaRequestsDomainEdgeSink(httpEdgeRouted.getSideOutput(NebulaGraphOutputTag.Edge.URL_ACCESS));
    }

    /**
     * 处理SSL信息
     * 提取顶点和边数据
     *
     * @param sslInfoStream SSL信息流
     */
    private void processSslInfo(DataStream<Map<String, Object>> sslInfoStream) {
        // 提取顶点数据
        DataStream<Map<String, Object>> sslVertexStream = sslInfoStream
                .flatMap(new SslVertexExtractor())
                .name("SSL顶点提取")
                .setParallelism(GraphBuilderConfig.getProcessParallelism());

        // 提取边数据
        DataStream<Map<String, Object>> sslEdgeStream = sslInfoStream
                .flatMap(new SslEdgeExtractor())
                .name("SSL边提取")
                .setParallelism(GraphBuilderConfig.getProcessParallelism());

        // 转换顶点数据为Row格式并直接分流
        SingleOutputStreamOperator<Row> sslVertexRouted = sslVertexStream
                .process(new CertificateVertexConverter())
                .name("SSL证书顶点转换和分流")
                .setParallelism(GraphBuilderConfig.getProcessParallelism());

        // 转换边数据为Row格式并直接分流
        SingleOutputStreamOperator<Row> sslEdgeRouted = sslEdgeStream
                .process(new CertificateEdgeConverter())
                .name("SSL证书边转换和分流")
                .setParallelism(GraphBuilderConfig.getProcessParallelism());

        // 配置图数据写入 - SSL顶点
        NebulaSinkWrapper.createIpVertexSink(sslVertexRouted.getSideOutput(NebulaGraphOutputTag.Vertex.IP));
        NebulaSinkWrapper.createDomainVertexSink(sslVertexRouted.getSideOutput(NebulaGraphOutputTag.Vertex.DOMAIN));
        NebulaSinkWrapper.createCertificateVertexSink(sslVertexRouted.getSideOutput(NebulaGraphOutputTag.Vertex.CERTIFICATE));
        NebulaSinkWrapper.createIssuerVertexSink(sslVertexRouted.getSideOutput(NebulaGraphOutputTag.Vertex.ISSUER));
        NebulaSinkWrapper.createSubjectVertexSink(sslVertexRouted.getSideOutput(NebulaGraphOutputTag.Vertex.SUBJECT));
        NebulaSinkWrapper.createOrgVertexSink(sslVertexRouted.getSideOutput(NebulaGraphOutputTag.Vertex.ORGANIZATION));

        // 配置图数据写入 - SSL边
        NebulaSinkWrapper.createClientTlsRequestsDomainEdgeSink(sslEdgeRouted.getSideOutput(NebulaGraphOutputTag.Edge.DOMAIN_ACCESS));
        NebulaSinkWrapper.createServerTlsHostsDomainEdgeSink(sslEdgeRouted.getSideOutput(NebulaGraphOutputTag.Edge.DOMAIN_ACCESS));
        NebulaSinkWrapper.createCertHasSubjectEdgeSink(sslEdgeRouted.getSideOutput(NebulaGraphOutputTag.Edge.CERTIFICATE_SUBJECT));
    }

    /**
     * 执行流水线
     * 启动Flink作业
     *
     * @param jobName 作业名称
     * @throws Exception 如果执行失败
     */
    public void execute(String jobName) throws Exception {
        env.execute(jobName);
    }
}
