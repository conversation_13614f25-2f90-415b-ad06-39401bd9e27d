package com.geeksec.nta.graphbuilder.converter.edge;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;


import com.geeksec.nta.graphbuilder.io.sink.tag.NebulaGraphOutputTag;
import com.geeksec.nta.graphbuilder.model.edge.BaseEdge;
import com.geeksec.nta.graphbuilder.model.edge.EdgeType;

import lombok.extern.slf4j.Slf4j;

/**
 * HTTP边转换器
 * 处理HTTP相关的边数据，将其转换为Flink Row格式
 *
 * <AUTHOR>
 */
@Slf4j
public class HttpEdgeConverter extends ProcessFunction<Map<String, Object>, Row> {

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void processElement(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) throws Exception {
        // 处理客户端HTTP请求域名边
        processClientHttpRequestsDomainEdges(edgeMap, ctx, out);

        // 处理服务端HTTP服务域名边
        processServerHttpServesDomainEdges(edgeMap, ctx, out);

        // 处理客户端TLS请求域名边
        processClientTlsRequestsDomainEdges(edgeMap, ctx, out);

        // 处理服务端TLS服务域名边
        processServerTlsServesDomainEdges(edgeMap, ctx, out);

        // 处理UA请求域名边
        processUaRequestsDomainEdges(edgeMap, ctx, out);

        // 处理客户端使用UA边
        processClientUsesUaEdges(edgeMap, ctx, out);

        // 处理UA有设备边
        processUaHasDeviceEdges(edgeMap, ctx, out);

        // 处理UA有操作系统边
        processUaHasOsEdges(edgeMap, ctx, out);
    }

    /**
     * 处理客户端HTTP请求域名边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processClientHttpRequestsDomainEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理客户端HTTP请求域名边列表
        List<BaseEdge> clientHttpConnectDomainEdgeList = (List<BaseEdge>) edgeMap.get("clientHttpConnectDomainEdgeList");
        if (!CollectionUtils.isEmpty(clientHttpConnectDomainEdgeList)) {
            for (BaseEdge edge : clientHttpConnectDomainEdgeList) {
                Row clientHttpConnectDomainRow = new Row(4);
                clientHttpConnectDomainRow.setField(0, EdgeType.CLIENT_HTTP_REQUESTS_DOMAIN);
                clientHttpConnectDomainRow.setField(1, edge.getSrcId());
                clientHttpConnectDomainRow.setField(2, edge.getDstId());
                clientHttpConnectDomainRow.setField(3, 0);

                // 直接输出到域名访问边的侧输出流
                ctx.output(NebulaGraphOutputTag.Edge.DOMAIN_ACCESS, clientHttpConnectDomainRow);
            }
        }
    }

    /**
     * 处理服务端HTTP服务域名边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processServerHttpServesDomainEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理服务端HTTP服务域名边列表
        List<BaseEdge> serverHttpServesDomainEdgeList = (List<BaseEdge>) edgeMap.get("serverHttpServesDomainEdgeList");
        if (!CollectionUtils.isEmpty(serverHttpServesDomainEdgeList)) {
            for (BaseEdge edge : serverHttpServesDomainEdgeList) {
                Row serverHttpServesDomainRow = new Row(4);
                serverHttpServesDomainRow.setField(0, EdgeType.SERVER_HTTP_SERVES_DOMAIN);
                serverHttpServesDomainRow.setField(1, edge.getSrcId());
                serverHttpServesDomainRow.setField(2, edge.getDstId());
                serverHttpServesDomainRow.setField(3, 0);

                // 直接输出到域名访问边的侧输出流
                ctx.output(NebulaGraphOutputTag.Edge.DOMAIN_ACCESS, serverHttpServesDomainRow);
            }
        }
    }

    /**
     * 处理客户端TLS请求域名边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processClientTlsRequestsDomainEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理客户端TLS请求域名边列表
        List<BaseEdge> clientSslConnectDomainEdgeList = (List<BaseEdge>) edgeMap.get("clientSslConnectDomainEdgeList");
        if (!CollectionUtils.isEmpty(clientSslConnectDomainEdgeList)) {
            for (BaseEdge edge : clientSslConnectDomainEdgeList) {
                Row clientSslConnectDomainRow = new Row(4);
                clientSslConnectDomainRow.setField(0, EdgeType.CLIENT_TLS_REQUESTS_DOMAIN);
                clientSslConnectDomainRow.setField(1, edge.getSrcId());
                clientSslConnectDomainRow.setField(2, edge.getDstId());
                clientSslConnectDomainRow.setField(3, 0);

                // 直接输出到域名访问边的侧输出流
                ctx.output(NebulaGraphOutputTag.Edge.DOMAIN_ACCESS, clientSslConnectDomainRow);
            }
        }
    }

    /**
     * 处理服务端TLS托管域名边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processServerTlsServesDomainEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理服务端TLS托管域名边列表
        List<BaseEdge> serverSslServesDomainEdgeList = (List<BaseEdge>) edgeMap.get("serverSslServesDomainEdgeList");
        if (!CollectionUtils.isEmpty(serverSslServesDomainEdgeList)) {
            for (BaseEdge edge : serverSslServesDomainEdgeList) {
                Row serverSslServesDomainRow = new Row(4);
                serverSslServesDomainRow.setField(0, EdgeType.SERVER_TLS_HOSTS_DOMAIN);
                serverSslServesDomainRow.setField(1, edge.getSrcId());
                serverSslServesDomainRow.setField(2, edge.getDstId());
                serverSslServesDomainRow.setField(3, 0);

                // 直接输出到域名访问边的侧输出流
                ctx.output(NebulaGraphOutputTag.Edge.DOMAIN_ACCESS, serverSslServesDomainRow);
            }
        }
    }

    /**
     * 处理UA请求域名边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processUaRequestsDomainEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理UA请求域名边列表
        List<BaseEdge> uaRequestsDomainEdgeList = (List<BaseEdge>) edgeMap.get("uaRequestsDomainEdgeList");
        if (!CollectionUtils.isEmpty(uaRequestsDomainEdgeList)) {
            for (BaseEdge edge : uaRequestsDomainEdgeList) {
                Row uaRequestsDomainRow = new Row(4);
                uaRequestsDomainRow.setField(0, EdgeType.UA_REQUESTS_DOMAIN);
                uaRequestsDomainRow.setField(1, edge.getSrcId());
                uaRequestsDomainRow.setField(2, edge.getDstId());
                uaRequestsDomainRow.setField(3, 0);

                // 直接输出到URL访问边的侧输出流
                ctx.output(NebulaGraphOutputTag.Edge.URL_ACCESS, uaRequestsDomainRow);
            }
        }
    }

    /**
     * 处理客户端使用UA边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processClientUsesUaEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理客户端使用UA边列表
        List<BaseEdge> clientUsesUaEdgeList = (List<BaseEdge>) edgeMap.get("clientUsesUaEdgeList");
        if (!CollectionUtils.isEmpty(clientUsesUaEdgeList)) {
            for (BaseEdge edge : clientUsesUaEdgeList) {
                Row clientUsesUaRow = new Row(4);
                clientUsesUaRow.setField(0, EdgeType.CLIENT_USES_UA);
                clientUsesUaRow.setField(1, edge.getSrcId());
                clientUsesUaRow.setField(2, edge.getDstId());
                clientUsesUaRow.setField(3, 0);

                // 直接输出到URL访问边的侧输出流
                ctx.output(NebulaGraphOutputTag.Edge.URL_ACCESS, clientUsesUaRow);
            }
        }
    }

    /**
     * 处理UA有设备边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processUaHasDeviceEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理UA有设备边列表
        List<BaseEdge> uaHasDeviceEdgeList = (List<BaseEdge>) edgeMap.get("uaHasDeviceEdgeList");
        if (!CollectionUtils.isEmpty(uaHasDeviceEdgeList)) {
            for (BaseEdge edge : uaHasDeviceEdgeList) {
                Row uaHasDeviceRow = new Row(3);
                uaHasDeviceRow.setField(0, edge.getSrcId());
                uaHasDeviceRow.setField(1, edge.getDstId());
                uaHasDeviceRow.setField(2, 0);

                // 直接输出到设备连接边的侧输出流
                ctx.output(NebulaGraphOutputTag.Edge.DEVICE_CONNECT, uaHasDeviceRow);
            }
        }
    }

    /**
     * 处理UA有操作系统边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processUaHasOsEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理UA有操作系统边列表
        List<BaseEdge> uaHasOsEdgeList = (List<BaseEdge>) edgeMap.get("uaHasOsEdgeList");
        if (!CollectionUtils.isEmpty(uaHasOsEdgeList)) {
            for (BaseEdge edge : uaHasOsEdgeList) {
                Row uaHasOsRow = new Row(3);
                uaHasOsRow.setField(0, edge.getSrcId());
                uaHasOsRow.setField(1, edge.getDstId());
                uaHasOsRow.setField(2, 0);

                // 直接输出到设备连接边的侧输出流
                ctx.output(NebulaGraphOutputTag.Edge.DEVICE_CONNECT, uaHasOsRow);
            }
        }
    }
}
