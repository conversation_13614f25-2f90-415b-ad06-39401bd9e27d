package com.geeksec.nta.graphbuilder.extractor.http;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.util.Collector;

import com.geeksec.common.utils.net.IpUtils;
import com.geeksec.nta.graphbuilder.extractor.base.BaseVertexExtractor;
import com.geeksec.nta.graphbuilder.model.vertex.AppVertex;
import com.geeksec.nta.graphbuilder.model.vertex.DeviceVertex;
import com.geeksec.nta.graphbuilder.model.vertex.DomainVertex;
import com.geeksec.nta.graphbuilder.model.vertex.IpVertex;
import com.geeksec.nta.graphbuilder.model.vertex.OsVertex;
import com.geeksec.nta.graphbuilder.model.vertex.UaVertex;
import com.geeksec.nta.graphbuilder.utils.net.DomainUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * HTTP顶点提取器
 * 从HTTP数据中提取顶点数据，包括域名、IP、UA、设备、操作系统、应用等
 *
 * <AUTHOR> Team
 */
@Slf4j
public class HttpVertexExtractor extends BaseVertexExtractor {

    /**
     * 从HTTP信息中生成各类顶点
     *
     * @param pbMap 数据映射
     * @param collector 收集器
     * @throws Exception 如果处理失败
     */
    @Override
    public void flatMap(Map<String, Object> pbMap, Collector<Map<String, Object>> collector) throws Exception {
        // 获取HTTP信息列表
        List<HashMap<String, Object>> httpInfoList = (List<HashMap<String, Object>>) pbMap.get("HTTP");
        if (CollectionUtils.isEmpty(httpInfoList)) {
            return;
        }

        // 1. 客户端IP顶点
        String clientIp = (String) pbMap.get("sIp");
        IpVertex clientIpVertex = null;
        if (!StringUtils.isEmpty(clientIp) && IpUtils.isIpAddress(clientIp)) {
            clientIpVertex = new IpVertex();
            clientIpVertex.setIpAddr(clientIp);
            clientIpVertex.setThreatScore(0);
            clientIpVertex.setTrustScore(0);
            clientIpVertex.setRemark("");
        }

        // 2. 服务端IP顶点
        String serverIp = (String) pbMap.get("dIp");
        IpVertex serverIpVertex = null;
        if (!StringUtils.isEmpty(serverIp) && IpUtils.isIpAddress(serverIp)) {
            serverIpVertex = new IpVertex();
            serverIpVertex.setIpAddr(serverIp);
            serverIpVertex.setThreatScore(0);
            serverIpVertex.setTrustScore(0);
            serverIpVertex.setRemark("");
        }

        // 存储所有顶点
        List<DomainVertex> domainVertices = new ArrayList<>();
        List<DomainVertex> baseDomainVertices = new ArrayList<>();
        List<UaVertex> uaVertices = new ArrayList<>();
        List<DeviceVertex> deviceVertices = new ArrayList<>();
        List<OsVertex> osVertices = new ArrayList<>();
        List<AppVertex> appVertices = new ArrayList<>();

        // 处理每个HTTP信息
        for (HashMap<String, Object> httpMap : httpInfoList) {
            String host = (String) httpMap.get("Host");
            String userAgent = (String) httpMap.get("User-Agent");

            // 3. 域名顶点
            if (!StringUtils.isEmpty(host) && DomainUtils.isValidDomain(host)) {
                // 处理域名中可能包含的端口
                if (host.contains(":")) {
                    host = host.split(":")[0];
                }

                DomainVertex domainVertex = new DomainVertex();
                domainVertex.setDomainAddr(host);
                domainVertex.setThreatScore(0);
                domainVertex.setTrustScore(0);
                domainVertex.setRemark("");
                domainVertices.add(domainVertex);

                // 4. 基础域名顶点
                String baseDomainAddr = getBaseDomain(host);
                if (!StringUtils.isEmpty(baseDomainAddr)) {
                    DomainVertex baseDomainVertex = new DomainVertex();
                    baseDomainVertex.setDomainAddr(baseDomainAddr);
                    baseDomainVertex.setThreatScore(0);
                    baseDomainVertex.setTrustScore(0);
                    baseDomainVertex.setRemark("");
                    baseDomainVertices.add(baseDomainVertex);
                }
            }

            // 处理User-Agent相关顶点
            if (!StringUtils.isEmpty(userAgent)) {
                // 5. UA顶点
                String uaId = String.valueOf(userAgent.hashCode());
                UaVertex uaVertex = new UaVertex();
                uaVertex.setUaId(uaId);
                uaVertex.setUaStr(userAgent);
                uaVertex.setUaDesc("");
                uaVertices.add(uaVertex);

                // 解析UA信息（设备、操作系统、应用）
                Map<String, String> uaInfo = parseUserAgent(userAgent);

                // 6. 设备顶点
                if (!StringUtils.isEmpty(uaInfo.get("device"))) {
                    DeviceVertex deviceVertex = new DeviceVertex();
                    deviceVertex.setDeviceName(uaInfo.get("device"));
                    deviceVertices.add(deviceVertex);
                }

                // 7. 操作系统顶点
                if (!StringUtils.isEmpty(uaInfo.get("os"))) {
                    OsVertex osVertex = new OsVertex();
                    osVertex.setOsName(uaInfo.get("os"));
                    osVertices.add(osVertex);
                }

                // 8. 应用顶点
                if (!StringUtils.isEmpty(uaInfo.get("app"))) {
                    AppVertex appVertex = new AppVertex();
                    appVertex.setAppId(uaInfo.get("app"));
                    appVertex.setAppName(uaInfo.get("app"));
                    appVertex.setAppVersion("");
                    appVertex.setThreatScore(0);
                    appVertex.setTrustScore(0);
                    appVertices.add(appVertex);
                }
            }
        }

        // 合并由HTTP元数据产出的顶点信息
        HashMap<String, Object> resultMap = new HashMap<>(8);
        resultMap.put("clientIpVertex", clientIpVertex);
        resultMap.put("serverIpVertex", serverIpVertex);
        resultMap.put("domainVertices", domainVertices);
        resultMap.put("baseDomainVertices", baseDomainVertices);
        resultMap.put("uaVertices", uaVertices);
        resultMap.put("deviceVertices", deviceVertices);
        resultMap.put("osVertices", osVertices);
        resultMap.put("appVertices", appVertices);

        collector.collect(resultMap);
    }

    /**
     * 解析User-Agent字符串
     * 简化处理，实际应该使用专业的UA解析库
     *
     * @param userAgent User-Agent字符串
     * @return 解析结果，包含设备、操作系统、应用信息
     */
    private Map<String, String> parseUserAgent(String userAgent) {
        Map<String, String> result = new HashMap<>();

        // 简化处理，实际应该使用专业的UA解析库
        // 这里只是示例，不能准确解析所有UA

        // 设备信息
        if (userAgent.contains("iPhone")) {
            result.put("device", "iPhone");
        } else if (userAgent.contains("iPad")) {
            result.put("device", "iPad");
        } else if (userAgent.contains("Android")) {
            result.put("device", "Android");
        } else {
            result.put("device", "PC");
        }

        // 操作系统信息
        if (userAgent.contains("Windows")) {
            result.put("os", "Windows");
        } else if (userAgent.contains("Mac OS")) {
            result.put("os", "Mac OS");
        } else if (userAgent.contains("Linux")) {
            result.put("os", "Linux");
        } else if (userAgent.contains("iOS")) {
            result.put("os", "iOS");
        } else if (userAgent.contains("Android")) {
            result.put("os", "Android");
        }

        // 应用信息
        if (userAgent.contains("Chrome")) {
            result.put("app", "Chrome");
        } else if (userAgent.contains("Firefox")) {
            result.put("app", "Firefox");
        } else if (userAgent.contains("Safari")) {
            result.put("app", "Safari");
        } else if (userAgent.contains("Edge")) {
            result.put("app", "Edge");
        } else if (userAgent.contains("MSIE") || userAgent.contains("Trident")) {
            result.put("app", "Internet Explorer");
        }

        return result;
    }
}
