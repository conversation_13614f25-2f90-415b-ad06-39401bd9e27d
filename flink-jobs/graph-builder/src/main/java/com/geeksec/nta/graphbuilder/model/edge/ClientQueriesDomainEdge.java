package com.geeksec.nta.graphbuilder.model.edge;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * DNS查询边
 * 表示客户端与域名之间的查询关系
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ClientQueriesDomainEdge extends BaseEdge {
    /**
     * DNS查询类型
     */
    private Integer dnsType;

    /**
     * DNS应答类型
     */
    private Integer answerType;

    @Override
    public EdgeType getEdgeType() {
        return EdgeType.CLIENT_QUERIES_DOMAIN;
    }
}
