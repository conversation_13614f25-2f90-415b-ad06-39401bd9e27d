package com.geeksec.nta.graphbuilder.constant;

/**
 * 处理相关常量类
 * 定义了所有与数据处理相关的常量，包括并行度、窗口大小等
 *
 * <AUTHOR> Team
 */
public class ProcessingConstants {
    
    /**
     * 并行度常量
     * 定义了不同处理阶段的并行度
     */
    public static final class Parallelism {
        public static final int PA1 = 1;
        public static final int PA2 = 2;
        public static final int PA4 = 4;
        public static final int PA8 = 8;
        public static final int PA12 = 12;
        public static final int PA16 = 16;
        public static final int PA32 = 32;
        public static final int PA64 = 64;
        public static final int PA128 = 128;
        
        // 源并行度
        public static final int SOURCE = PA4;
        
        // 处理并行度
        public static final int PROCESS = PA8;
        
        // 路由并行度
        public static final int ROUTE = PA8;
        
        // 写入并行度
        public static final int SINK = PA8;
    }
    
    /**
     * 批处理常量
     * 定义了批处理相关的参数
     */
    public static final class Batch {
        // 批处理大小
        public static final int SIZE = 100;
        
        // 批处理间隔（毫秒）
        public static final int INTERVAL = 1000;
    }
}
