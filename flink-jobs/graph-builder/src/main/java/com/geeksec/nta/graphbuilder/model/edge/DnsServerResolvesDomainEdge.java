package com.geeksec.nta.graphbuilder.model.edge;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * DNS服务器解析域名边
 * 表示DNS服务器解析域名的关系
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DnsServerResolvesDomainEdge extends BaseEdge {
    
    /**
     * DNS查询类型
     */
    private String dnsType;
    
    /**
     * DNS应答类型
     */
    private Integer answerType;
    
    /**
     * 获取边类型
     *
     * @return 边类型常量
     */
    @Override
    public EdgeType getEdgeType() {
        return EdgeType.DNS_SERVER_RESOLVES_DOMAIN;
    }
}
