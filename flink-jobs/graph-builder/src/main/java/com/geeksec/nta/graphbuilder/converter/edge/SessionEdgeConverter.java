package com.geeksec.nta.graphbuilder.converter.edge;

import java.util.Map;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import com.geeksec.nta.graphbuilder.io.sink.tag.NebulaGraphOutputTag;

import com.geeksec.nta.graphbuilder.model.edge.IpConnectsToIpEdge;
import com.geeksec.nta.graphbuilder.model.edge.IpMapsToMacEdge;
import com.geeksec.nta.graphbuilder.model.edge.MacConnectsToMacEdge;

import lombok.extern.slf4j.Slf4j;

/**
 * 会话边转换器
 * 处理网络会话相关的边数据（IP连接、MAC连接、IP到MAC映射），将其转换为Flink Row格式
 *
 * <AUTHOR>
 */
@Slf4j
public class SessionEdgeConverter extends ProcessFunction<Map<String, Object>, Row> {

    private static final long serialVersionUID = 1L;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void processElement(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) throws Exception {
        // 处理IP到MAC映射边并直接分流
        processIpMapsToMacEdges(edgeMap, ctx, out);

        // 处理IP连接边并直接分流
        processIpConnectEdges(edgeMap, ctx, out);

        // 处理MAC连接边并直接分流
        processMacConnectEdges(edgeMap, ctx, out);
    }

    /**
     * 处理IP到MAC映射边并直接分流
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文
     * @param out 收集器
     */
    private void processIpMapsToMacEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理源IP到源MAC的映射
        IpMapsToMacEdge srcIpToMac = (IpMapsToMacEdge) edgeMap.get("srcBindEdge");
        if (!ObjectUtils.isEmpty(srcIpToMac)) {
            Row ipMapsToMacRow = new Row(4);
            ipMapsToMacRow.setField(0, srcIpToMac.getSrcId());
            ipMapsToMacRow.setField(1, srcIpToMac.getDstId());
            // rank值，用于区分相同源顶点和目标顶点的多条边
            ipMapsToMacRow.setField(2, 0);

            // 直接输出到设备连接边的侧输出流
            ctx.output(NebulaGraphOutputTag.Edge.DEVICE_CONNECT, ipMapsToMacRow);


        }

        // 处理目的IP到目的MAC的映射
        IpMapsToMacEdge dstIpToMac = (IpMapsToMacEdge) edgeMap.get("dstBindEdge");
        if (!ObjectUtils.isEmpty(dstIpToMac)) {
            Row ipMapsToMacRow = new Row(4);
            ipMapsToMacRow.setField(0, dstIpToMac.getSrcId());
            ipMapsToMacRow.setField(1, dstIpToMac.getDstId());
            // rank值，用于区分相同源顶点和目标顶点的多条边
            ipMapsToMacRow.setField(2, 0);

            // 直接输出到设备连接边的侧输出流
            ctx.output(NebulaGraphOutputTag.Edge.DEVICE_CONNECT, ipMapsToMacRow);


        }
    }

    /**
     * 处理IP连接边并直接分流
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文
     * @param out 收集器
     */
    private void processIpConnectEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理IP连接边
        IpConnectsToIpEdge ipConnectEdge = (IpConnectsToIpEdge) edgeMap.get("ipConnectEdge");
        if (!ObjectUtils.isEmpty(ipConnectEdge)) {
            Row ipConnectRow = new Row(4);
            ipConnectRow.setField(0, ipConnectEdge.getSrcId());
            ipConnectRow.setField(1, ipConnectEdge.getDstId());
            // rank值，用于区分相同源顶点和目标顶点的多条边
            ipConnectRow.setField(2, 0);
            ipConnectRow.setField(3, ipConnectEdge.getDport());

            // 直接输出到IP连接边的侧输出流
            ctx.output(NebulaGraphOutputTag.Edge.IP_CONNECT, ipConnectRow);


        }
    }

    /**
     * 处理MAC连接边并直接分流
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文
     * @param out 收集器
     */
    private void processMacConnectEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理MAC连接边
        MacConnectsToMacEdge macConnectEdge = (MacConnectsToMacEdge) edgeMap.get("macConnectEdge");
        if (!ObjectUtils.isEmpty(macConnectEdge)) {
            Row macConnectRow = new Row(3);
            macConnectRow.setField(0, macConnectEdge.getSrcId());
            macConnectRow.setField(1, macConnectEdge.getDstId());
            // rank值，用于区分相同源顶点和目标顶点的多条边
            macConnectRow.setField(2, 0);

            // 直接输出到设备连接边的侧输出流
            ctx.output(NebulaGraphOutputTag.Edge.DEVICE_CONNECT, macConnectRow);


        }
    }
}
