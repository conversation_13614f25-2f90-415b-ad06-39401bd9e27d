package com.geeksec.nta.graphbuilder.io.sink;

import java.util.Arrays;

import org.apache.flink.connector.nebula.connection.NebulaClientOptions;
import org.apache.flink.connector.nebula.connection.NebulaGraphConnectionProvider;
import org.apache.flink.connector.nebula.connection.NebulaMetaConnectionProvider;
import org.apache.flink.connector.nebula.sink.NebulaEdgeBatchOutputFormat;
import org.apache.flink.connector.nebula.sink.NebulaSinkFunction;
import org.apache.flink.connector.nebula.sink.NebulaVertexBatchOutputFormat;
import org.apache.flink.connector.nebula.statement.EdgeExecutionOptions;
import org.apache.flink.connector.nebula.statement.VertexExecutionOptions;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.types.Row;

import com.geeksec.nta.graphbuilder.config.GraphBuilderConfig;
import com.geeksec.nta.graphbuilder.model.edge.EdgeType;
import com.geeksec.nta.graphbuilder.model.vertex.VertexTag;

import lombok.extern.slf4j.Slf4j;

/**
 * Nebula Sink工厂类
 * 负责创建和配置Nebula图数据库的Sink函数
 *
 * <AUTHOR> Team
 */
@Slf4j
public class NebulaSinkFactory {

        /**
         * 获取Nebula图顶点Sink函数
         *
         * @param vertexTag 顶点标签枚举
         * @return Sink函数
         */
        @SuppressWarnings("deprecation")
        public static SinkFunction<Row> getVertexSink(VertexTag vertexTag) {
                // 创建Nebula连接提供者
                NebulaGraphConnectionProvider graphConnectionProvider = createGraphConnectionProvider();
                NebulaMetaConnectionProvider metaConnectionProvider = createMetaConnectionProvider();

                // 获取图空间和批处理配置
                String graphSpace = GraphBuilderConfig.getNebulaGraphSpace();
                int batchSize = GraphBuilderConfig.getBatchSize();
                int batchInterval = GraphBuilderConfig.getBatchInterval();

                // 创建顶点执行选项
                VertexExecutionOptions options = createVertexExecutionOptions(vertexTag, graphSpace, batchSize,
                                batchInterval);
                if (options == null) {
                        return null;
                }

                // 创建输出格式和Sink函数
                NebulaVertexBatchOutputFormat outputFormat = new NebulaVertexBatchOutputFormat(
                                graphConnectionProvider, metaConnectionProvider, options);
                return new NebulaSinkFunction<>(outputFormat);
        }

        /**
         * 获取Nebula图边Sink函数
         *
         * @param edgeType 边类型枚举
         * @return Sink函数
         */
        @SuppressWarnings("deprecation")
        public static SinkFunction<Row> getEdgeSink(EdgeType edgeType) {
                // 创建Nebula连接提供者
                NebulaGraphConnectionProvider graphConnectionProvider = createGraphConnectionProvider();
                NebulaMetaConnectionProvider metaConnectionProvider = createMetaConnectionProvider();

                // 获取图空间和批处理配置
                String graphSpace = GraphBuilderConfig.getNebulaGraphSpace();
                int batchSize = GraphBuilderConfig.getBatchSize();
                int batchInterval = GraphBuilderConfig.getBatchInterval();

                // 创建边执行选项
                EdgeExecutionOptions options = createEdgeExecutionOptions(edgeType, graphSpace, batchSize,
                                batchInterval);
                if (options == null) {
                        return null;
                }

                // 创建输出格式和Sink函数
                NebulaEdgeBatchOutputFormat outputFormat = new NebulaEdgeBatchOutputFormat(
                                graphConnectionProvider, metaConnectionProvider, options);
                return new NebulaSinkFunction<>(outputFormat);
        }

        /**
         * 创建顶点执行选项
         *
         * @param vertexTag     顶点标签枚举
         * @param graphSpace    图空间名称
         * @param batchSize     批处理大小
         * @param batchInterval 批处理间隔（毫秒）
         * @return 顶点执行选项
         */
        private static VertexExecutionOptions createVertexExecutionOptions(
                        VertexTag vertexTag, String graphSpace, int batchSize, int batchInterval) {

                return switch (vertexTag) {
                        case IP -> new VertexExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setTag(vertexTag.getValue())
                                        .setIdIndex(0)
                                        .setFields(Arrays.asList("version", "city", "country", "threat_score",
                                                        "trust_score",
                                                        "remark"))
                                        .setPositions(Arrays.asList(1, 2, 3, 4, 5, 6))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case MAC -> new VertexExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setTag(vertexTag.getValue())
                                        .setIdIndex(0)
                                        .setFields(Arrays.asList("vlan_info", "threat_score", "trust_score", "remark"))
                                        .setPositions(Arrays.asList(1, 2, 3, 4))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case APP -> new VertexExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setTag(vertexTag.getValue())
                                        .setIdIndex(0)
                                        .setFields(Arrays.asList("app_name", "app_version", "threat_score"))
                                        .setPositions(Arrays.asList(1, 2, 3))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case APPSERVICE -> new VertexExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setTag(vertexTag.getValue())
                                        .setIdIndex(0)
                                        .setFields(Arrays.asList("app_name", "ip_addr", "port", "ip_protocol",
                                                        "remark"))
                                        .setPositions(Arrays.asList(1, 2, 3, 4, 5))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case DOMAIN -> new VertexExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setTag(vertexTag.getValue())
                                        .setIdIndex(0)
                                        .setFields(Arrays.asList("domain_addr", "threat_score", "trust_score", "remark",
                                                        "alexa_rank",
                                                        "whois"))
                                        .setPositions(Arrays.asList(1, 2, 3, 4, 5, 6))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case BASE_DOMAIN -> new VertexExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setTag(vertexTag.getValue())
                                        .setIdIndex(0)
                                        .setFields(Arrays.asList("base_domain", "threat_score", "trust_score"))
                                        .setPositions(Arrays.asList(1, 2, 3))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case CERT -> new VertexExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setTag(vertexTag.getValue())
                                        .setIdIndex(0)
                                        .setFields(Arrays.asList("threat_score", "remark"))
                                        .setPositions(Arrays.asList(1, 2))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case UA -> new VertexExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setTag(vertexTag.getValue())
                                        .setIdIndex(0)
                                        .setFields(Arrays.asList("ua_str"))
                                        .setPositions(Arrays.asList(1))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case DEVICE -> new VertexExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setTag(vertexTag.getValue())
                                        .setIdIndex(0)
                                        .setFields(Arrays.asList())
                                        .setPositions(Arrays.asList())
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case OS -> new VertexExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setTag(vertexTag.getValue())
                                        .setIdIndex(0)
                                        .setFields(Arrays.asList("os_version"))
                                        .setPositions(Arrays.asList(1))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case SSL_FINGERPRINT -> new VertexExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setTag(vertexTag.getValue())
                                        .setIdIndex(0)
                                        .setFields(Arrays.asList("description", "type", "threat_score"))
                                        .setPositions(Arrays.asList(1, 2, 3))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case ISSUER -> new VertexExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setTag(vertexTag.getValue())
                                        .setIdIndex(0)
                                        .setFields(Arrays.asList("country", "common_name", "organization", "org_unit",
                                                        "threat_score",
                                                        "trust_score"))
                                        .setPositions(Arrays.asList(1, 2, 3, 4, 5, 6))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case SUBJECT -> new VertexExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setTag(vertexTag.getValue())
                                        .setIdIndex(0)
                                        .setFields(Arrays.asList("country", "common_name", "organization", "org_unit",
                                                        "threat_score",
                                                        "trust_score"))
                                        .setPositions(Arrays.asList(1, 2, 3, 4, 5, 6))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case ORG -> new VertexExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setTag(vertexTag.getValue())
                                        .setIdIndex(0)
                                        .setFields(Arrays.asList("org_name", "org_desc", "threat_score", "trust_score",
                                                        "remark"))
                                        .setPositions(Arrays.asList(1, 2, 3, 4, 5))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case URL -> new VertexExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setTag(vertexTag.getValue())
                                        .setIdIndex(0)
                                        .setFields(Arrays.asList("url", "trust_score", "threat_score"))
                                        .setPositions(Arrays.asList(1, 2, 3))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case ATTACKER, VICTIM -> new VertexExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setTag(vertexTag.getValue())
                                        .setIdIndex(0)
                                        .setFields(Arrays.asList("id", "name", "type"))
                                        .setPositions(Arrays.asList(1, 2, 3))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        default -> {
                                log.warn("Unknown vertex tag: {}", vertexTag);
                                yield null;
                        }
                };
        }

        /**
         * 创建边执行选项
         *
         * @param edgeType      边类型枚举
         * @param graphSpace    图空间名称
         * @param batchSize     批处理大小
         * @param batchInterval 批处理间隔（毫秒）
         * @return 边执行选项
         */
        private static EdgeExecutionOptions createEdgeExecutionOptions(
                        EdgeType edgeType, String graphSpace, int batchSize, int batchInterval) {
                return switch (edgeType) {
                        case IP_MAPS_TO_MAC -> new EdgeExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setEdge(edgeType.getValue())
                                        .setSrcIndex(0)
                                        .setDstIndex(1)
                                        .setRankIndex(2)
                                        .setFields(Arrays.asList("session_cnt"))
                                        .setPositions(Arrays.asList(3))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case IP_CONNECTS_TO_IP -> new EdgeExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setEdge(edgeType.getValue())
                                        .setSrcIndex(0)
                                        .setDstIndex(1)
                                        .setRankIndex(2)
                                        .setFields(Arrays.asList("protocol", "src_port", "dst_port", "session_cnt"))
                                        .setPositions(Arrays.asList(3, 4, 5, 6))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case MAC_CONNECTS_TO_MAC -> new EdgeExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setEdge(edgeType.getValue())
                                        .setSrcIndex(0)
                                        .setDstIndex(1)
                                        .setRankIndex(2)
                                        .setFields(Arrays.asList("session_cnt"))
                                        .setPositions(Arrays.asList(3))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case DOMAIN_DERIVES_FROM_BASE_DOMAIN -> new EdgeExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setEdge(edgeType.getValue())
                                        .setSrcIndex(0)
                                        .setDstIndex(1)
                                        .setRankIndex(2)
                                        .setFields(Arrays.asList())
                                        .setPositions(Arrays.asList())
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case CLIENT_ACCESSES_APP -> new EdgeExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setEdge(edgeType.getValue())
                                        .setSrcIndex(0)
                                        .setDstIndex(1)
                                        .setRankIndex(2)
                                        .setFields(Arrays.asList("session_cnt"))
                                        .setPositions(Arrays.asList(3))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case APP_DEPLOYS_ON_SERVER -> new EdgeExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setEdge(edgeType.getValue())
                                        .setSrcIndex(0)
                                        .setDstIndex(1)
                                        .setRankIndex(2)
                                        .setFields(Arrays.asList())
                                        .setPositions(Arrays.asList())
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case CLIENT_HTTP_REQUESTS_DOMAIN -> new EdgeExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setEdge(edgeType.getValue())
                                        .setSrcIndex(0)
                                        .setDstIndex(1)
                                        .setRankIndex(2)
                                        .setFields(Arrays.asList("method", "uri", "status_code", "session_cnt"))
                                        .setPositions(Arrays.asList(3, 4, 5, 6))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case SERVER_HTTP_SERVES_DOMAIN -> new EdgeExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setEdge(edgeType.getValue())
                                        .setSrcIndex(0)
                                        .setDstIndex(1)
                                        .setRankIndex(2)
                                        .setFields(Arrays.asList("method", "uri", "status_code", "session_cnt"))
                                        .setPositions(Arrays.asList(3, 4, 5, 6))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case CLIENT_TLS_REQUESTS_DOMAIN -> new EdgeExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setEdge(edgeType.getValue())
                                        .setSrcIndex(0)
                                        .setDstIndex(1)
                                        .setRankIndex(2)
                                        .setFields(Arrays.asList("session_cnt"))
                                        .setPositions(Arrays.asList(3))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case SERVER_TLS_HOSTS_DOMAIN -> new EdgeExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setEdge(edgeType.getValue())
                                        .setSrcIndex(0)
                                        .setDstIndex(1)
                                        .setRankIndex(2)
                                        .setFields(Arrays.asList("session_cnt"))
                                        .setPositions(Arrays.asList(3))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case CLIENT_QUERIES_DOMAIN -> new EdgeExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setEdge(edgeType.getValue())
                                        .setSrcIndex(0)
                                        .setDstIndex(1)
                                        .setRankIndex(2)
                                        .setFields(Arrays.asList("query_type", "session_cnt"))
                                        .setPositions(Arrays.asList(3, 4))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case CLIENT_QUERIES_DNS_SERVER -> new EdgeExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setEdge(edgeType.getValue())
                                        .setSrcIndex(0)
                                        .setDstIndex(1)
                                        .setRankIndex(2)
                                        .setFields(Arrays.asList("session_cnt"))
                                        .setPositions(Arrays.asList(3))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case UA_REQUESTS_DOMAIN -> new EdgeExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setEdge(edgeType.getValue())
                                        .setSrcIndex(0)
                                        .setDstIndex(1)
                                        .setRankIndex(2)
                                        .setFields(Arrays.asList("session_cnt"))
                                        .setPositions(Arrays.asList(3))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case CLIENT_USES_UA -> new EdgeExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setEdge(edgeType.getValue())
                                        .setSrcIndex(0)
                                        .setDstIndex(1)
                                        .setRankIndex(2)
                                        .setFields(Arrays.asList("session_cnt"))
                                        .setPositions(Arrays.asList(3))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case UA_HAS_DEVICE -> new EdgeExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setEdge(edgeType.getValue())
                                        .setSrcIndex(0)
                                        .setDstIndex(1)
                                        .setRankIndex(2)
                                        .setFields(Arrays.asList())
                                        .setPositions(Arrays.asList())
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case UA_HAS_OS -> new EdgeExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setEdge(edgeType.getValue())
                                        .setSrcIndex(0)
                                        .setDstIndex(1)
                                        .setRankIndex(2)
                                        .setFields(Arrays.asList())
                                        .setPositions(Arrays.asList())
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();

                        case IP_PRESENTS_FINGERPRINT -> new EdgeExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setEdge(edgeType.getValue())
                                        .setSrcIndex(0)
                                        .setDstIndex(1)
                                        .setRankIndex(2)
                                        .setFields(Arrays.asList("role"))
                                        .setPositions(Arrays.asList(3))
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case FINGERPRINT_APPEARS_WITH_DOMAIN -> new EdgeExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setEdge(edgeType.getValue())
                                        .setSrcIndex(0)
                                        .setDstIndex(1)
                                        .setRankIndex(2)
                                        .setFields(Arrays.asList())
                                        .setPositions(Arrays.asList())
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        case FINGERPRINT_IDENTIFIES_APP -> new EdgeExecutionOptions.ExecutionOptionBuilder()
                                        .setGraphSpace(graphSpace)
                                        .setEdge(edgeType.getValue())
                                        .setSrcIndex(0)
                                        .setDstIndex(1)
                                        .setRankIndex(2)
                                        .setFields(Arrays.asList())
                                        .setPositions(Arrays.asList())
                                        .setBatchIntervalMs(batchInterval)
                                        .setBatchSize(batchSize)
                                        .build();
                        default -> {
                                log.warn("Unknown edge type: {}", edgeType);
                                yield null;
                        }
                };
        }

        /**
         * 创建Nebula图连接提供者
         *
         * @return 图连接提供者
         */
        private static NebulaGraphConnectionProvider createGraphConnectionProvider() {
                // 使用NebulaClientOptionsBuilder创建NebulaClientOptions实例
                NebulaClientOptions options = new NebulaClientOptions.NebulaClientOptionsBuilder()
                                .setGraphAddress(GraphBuilderConfig.getNebulaGraphAddress())
                                .setMetaAddress(GraphBuilderConfig.getNebulaMetaAddress())
                                .build();

                return new NebulaGraphConnectionProvider(options);
        }

        /**
         * 创建Nebula元数据连接提供者
         *
         * @return 元数据连接提供者
         */
        private static NebulaMetaConnectionProvider createMetaConnectionProvider() {
                // 使用NebulaClientOptionsBuilder创建NebulaClientOptions实例
                NebulaClientOptions options = new NebulaClientOptions.NebulaClientOptionsBuilder()
                                .setMetaAddress(GraphBuilderConfig.getNebulaMetaAddress())
                                .build();

                return new NebulaMetaConnectionProvider(options);
        }
}
