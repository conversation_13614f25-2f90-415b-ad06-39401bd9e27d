package com.geeksec.nta.graphbuilder.model.edge;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TLS指纹与证书一同出现边
 * 表示TLS指纹与证书在同一会话中被观察到的关系
 * 根据nebula-init.yaml中的定义，fingerprint_appears_with_cert边应包含sni属性
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FingerprintAppearsWithCertEdge extends BaseEdge {
    /**
     * SNI信息
     */
    private String sni;

    /**
     * 获取边类型
     *
     * @return 边类型常量
     */
    @Override
    public EdgeType getEdgeType() {
        return EdgeType.FINGERPRINT_APPEARS_WITH_CERT;
    }
}
