package com.geeksec.nta.graphbuilder.model.edge;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * MAC连接边
 * 表示两个MAC地址之间的链路层连接关系
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MacConnectsToMacEdge extends BaseEdge {
    /**
     * 获取边类型
     *
     * @return 边类型常量
     */
    @Override
    public EdgeType getEdgeType() {
        return EdgeType.MAC_CONNECTS_TO_MAC;
    }
}
