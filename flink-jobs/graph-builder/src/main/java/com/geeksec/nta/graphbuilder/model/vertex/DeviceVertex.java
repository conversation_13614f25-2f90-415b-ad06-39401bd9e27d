package com.geeksec.nta.graphbuilder.model.vertex;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备顶点
 * 表示设备信息
 *
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DeviceVertex extends BaseVertex {
    /**
     * 设备名称
     */
    private String deviceName;

    @Override
    public String getVertexId() {
        return deviceName;
    }

    /**
     * 获取顶点标签
     *
     * @return 顶点标签常量
     */
    @Override
    public VertexTag getVertexTag() {
        return VertexTag.DEVICE;
    }
}
