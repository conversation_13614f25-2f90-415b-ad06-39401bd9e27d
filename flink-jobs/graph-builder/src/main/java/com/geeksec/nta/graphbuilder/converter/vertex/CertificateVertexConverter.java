package com.geeksec.nta.graphbuilder.converter.vertex;

import java.util.List;
import java.util.Map;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import com.geeksec.nta.graphbuilder.io.sink.tag.NebulaGraphOutputTag;
import com.geeksec.nta.graphbuilder.model.vertex.CertVertex;
import com.geeksec.nta.graphbuilder.model.vertex.SslFingerprintVertex;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书顶点转换器
 * 处理证书相关的顶点数据，将其转换为Flink Row格式
 *
 * <AUTHOR>
 */
@Slf4j
public class CertificateVertexConverter extends ProcessFunction<Map<String, Object>, Row> {

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void processElement(Map<String, Object> vertexMap, Context ctx, Collector<Row> out) throws Exception {
        // 处理证书顶点
        processCertVertices(vertexMap, ctx, out);

        // 处理SSL指纹顶点
        processSslFingerprintVertices(vertexMap, ctx, out);
    }

    /**
     * 处理证书顶点
     *
     * @param vertexMap 顶点数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processCertVertices(Map<String, Object> vertexMap, Context ctx, Collector<Row> out) {
        // 处理证书顶点列表
        List<CertVertex> certVertexList = (List<CertVertex>) vertexMap.get("certTagList");
        if (certVertexList != null && !certVertexList.isEmpty()) {
            for (CertVertex certVertex : certVertexList) {
                Row certVertexRow = new Row(3);
                certVertexRow.setField(0, certVertex.getCertId());
                certVertexRow.setField(1, certVertex.getThreatScore());
                certVertexRow.setField(2, certVertex.getRemark());

                // 直接输出到证书顶点的侧输出流
                ctx.output(NebulaGraphOutputTag.Vertex.CERTIFICATE, certVertexRow);
            }
        }
    }

    /**
     * 处理SSL指纹顶点
     *
     * @param vertexMap 顶点数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processSslFingerprintVertices(Map<String, Object> vertexMap, Context ctx, Collector<Row> out) {
        // 处理客户端SSL指纹顶点
        SslFingerprintVertex clientSslFingerprint = (SslFingerprintVertex) vertexMap.get("clientSslFingerprint");
        if (clientSslFingerprint != null) {
            Row clientSslFingerprintRow = new Row(4);
            clientSslFingerprintRow.setField(0, clientSslFingerprint.getJa3Hash());
            clientSslFingerprintRow.setField(1, clientSslFingerprint.getFingerDesc());
            clientSslFingerprintRow.setField(2, clientSslFingerprint.getType());
            clientSslFingerprintRow.setField(3, clientSslFingerprint.getThreatScore() != null ? clientSslFingerprint.getThreatScore() : 0);

            // 直接输出到SSL指纹顶点的侧输出流
            ctx.output(NebulaGraphOutputTag.Vertex.CERTIFICATE, clientSslFingerprintRow);
        }

        // 处理服务端SSL指纹顶点
        SslFingerprintVertex serverSslFingerprint = (SslFingerprintVertex) vertexMap.get("serverSslFingerprint");
        if (serverSslFingerprint != null) {
            Row serverSslFingerprintRow = new Row(4);
            serverSslFingerprintRow.setField(0, serverSslFingerprint.getJa3Hash());
            serverSslFingerprintRow.setField(1, serverSslFingerprint.getFingerDesc());
            serverSslFingerprintRow.setField(2, serverSslFingerprint.getType());
            serverSslFingerprintRow.setField(3, serverSslFingerprint.getThreatScore() != null ? serverSslFingerprint.getThreatScore() : 0);

            // 直接输出到SSL指纹顶点的侧输出流
            ctx.output(NebulaGraphOutputTag.Vertex.CERTIFICATE, serverSslFingerprintRow);
        }
    }
}
