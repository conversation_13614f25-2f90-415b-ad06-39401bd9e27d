package com.geeksec.nta.graphbuilder.extractor.dns;

import com.geeksec.nta.graphbuilder.utils.net.DomainUtils;
import com.geeksec.common.utils.net.IpUtils;
import com.geeksec.nta.graphbuilder.extractor.base.BaseVertexExtractor;
import com.geeksec.nta.graphbuilder.model.vertex.IpVertex;
import com.geeksec.nta.graphbuilder.model.vertex.DomainVertex;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.util.Collector;

import lombok.extern.slf4j.Slf4j;

/**
 * DNS顶点提取器
 * 从DNS数据中提取顶点数据，包括域名、IP等
 *
 * <AUTHOR> Team
 */
@Slf4j
public class DnsVertexExtractor extends BaseVertexExtractor {

    /**
     * 从DNS信息中生成各类顶点
     *
     * @param pbMap 数据映射
     * @param collector 收集器
     * @throws Exception 如果处理失败
     */
    @Override
    public void flatMap(Map<String, Object> pbMap, Collector<Map<String, Object>> collector) throws Exception {
        // 1. 客户端IP顶点
        String clientIp = (String) pbMap.get("sIp");
        IpVertex clientIpVertex = null;
        if (!StringUtil.isNullOrEmpty(clientIp) && IpUtils.isValidIp(clientIp)) {
            clientIpVertex = new IpVertex();
            clientIpVertex.setIpAddr(clientIp);
            clientIpVertex.setThreatScore(0);
            clientIpVertex.setTrustScore(0);
            clientIpVertex.setRemark("");
        }

        // 2. DNS服务器IP顶点
        String serverIp = (String) pbMap.get("dIp");
        IpVertex serverIpVertex = null;
        if (!StringUtil.isNullOrEmpty(serverIp) && IpUtils.isValidIp(serverIp)) {
            serverIpVertex = new IpVertex();
            serverIpVertex.setIpAddr(serverIp);
            serverIpVertex.setThreatScore(0);
            serverIpVertex.setTrustScore(0);
            serverIpVertex.setRemark("");
        }

        // 3. 域名顶点
        String domain = (String) pbMap.get("Domain");
        DomainVertex domainVertex = null;
        if (!StringUtil.isNullOrEmpty(domain) && DomainUtils.isValidDomain(domain)) {
            domainVertex = new DomainVertex();
            domainVertex.setDomainAddr(domain);
            domainVertex.setThreatScore(0);
            domainVertex.setTrustScore(0);
            domainVertex.setRemark("");
        }

        // 4. 基础域名顶点
        DomainVertex baseDomainVertex = null;
        if (domainVertex != null) {
            String baseDomainAddr = getBaseDomain(domain);
            if (!StringUtil.isNullOrEmpty(baseDomainAddr)) {
                baseDomainVertex = new DomainVertex();
                baseDomainVertex.setDomainAddr(baseDomainAddr);
                baseDomainVertex.setThreatScore(0);
                baseDomainVertex.setTrustScore(0);
                baseDomainVertex.setRemark("");
            }
        }

        // 5. CNAME域名顶点
        List<DomainVertex> cnameVertices = new ArrayList<>();
        List<String> cnameList = (List<String>) pbMap.get("CnameList");
        if (CollectionUtils.isNotEmpty(cnameList)) {
            for (String cname : cnameList) {
                if (!StringUtil.isNullOrEmpty(cname) && DomainUtils.isValidDomain(cname)) {
                    DomainVertex cnameVertex = new DomainVertex();
                    cnameVertex.setDomainAddr(cname);
                    cnameVertex.setThreatScore(0);
                    cnameVertex.setTrustScore(0);
                    cnameVertex.setRemark("");
                    cnameVertices.add(cnameVertex);
                }
            }
        }

        // 6. 解析IP顶点
        List<IpVertex> resolvedIpVertices = new ArrayList<>();
        List<String> resolvedIps = (List<String>) pbMap.get("ResolvedIps");
        if (CollectionUtils.isNotEmpty(resolvedIps)) {
            for (String ip : resolvedIps) {
                if (!StringUtil.isNullOrEmpty(ip) && IpUtils.isValidIp(ip)) {
                    IpVertex ipVertex = new IpVertex();
                    ipVertex.setIpAddr(ip);
                    ipVertex.setThreatScore(0);
                    ipVertex.setTrustScore(0);
                    ipVertex.setRemark("");
                    resolvedIpVertices.add(ipVertex);
                }
            }
        }

        // 合并由DNS元数据产出的顶点信息
        HashMap<String, Object> resultMap = new HashMap<>(8);
        resultMap.put("clientIpVertex", clientIpVertex);
        resultMap.put("serverIpVertex", serverIpVertex);
        resultMap.put("domainVertex", domainVertex);
        resultMap.put("baseDomainVertex", baseDomainVertex);
        resultMap.put("cnameVertices", cnameVertices);
        resultMap.put("resolvedIpVertices", resolvedIpVertices);

        collector.collect(resultMap);
    }
}
