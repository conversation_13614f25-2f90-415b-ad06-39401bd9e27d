package com.geeksec.nta.graphbuilder.model.vertex;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户代理顶点
 * 表示HTTP请求中的User-Agent信息
 *
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UaVertex extends BaseVertex {
    /**
     * UA ID
     */
    private String uaId;

    /**
     * UA字符串
     */
    private String uaStr;

    /**
     * UA描述
     */
    private String uaDesc;

    @Override
    public String getVertexId() {
        return uaId;
    }

    /**
     * 获取顶点标签
     *
     * @return 顶点标签常量
     */
    @Override
    public VertexTag getVertexTag() {
        return VertexTag.UA;
    }
}
