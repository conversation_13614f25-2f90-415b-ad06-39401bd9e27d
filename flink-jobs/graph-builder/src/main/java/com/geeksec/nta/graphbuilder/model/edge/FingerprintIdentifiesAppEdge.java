package com.geeksec.nta.graphbuilder.model.edge;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TLS指纹作为特定应用程序的识别特征边
 * 表示TLS指纹与应用程序之间的识别关系
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FingerprintIdentifiesAppEdge extends BaseEdge {
    
    /**
     * 获取边类型
     *
     * @return 边类型常量
     */
    @Override
    public EdgeType getEdgeType() {
        return EdgeType.FINGERPRINT_IDENTIFIES_APP;
    }
}
