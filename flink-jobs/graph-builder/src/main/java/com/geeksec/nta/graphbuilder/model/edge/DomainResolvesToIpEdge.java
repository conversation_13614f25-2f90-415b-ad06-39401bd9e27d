package com.geeksec.nta.graphbuilder.model.edge;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * DNS解析边
 * 表示域名解析到IP地址的关系
 * 根据nebula-init.yaml中的定义，domain_resolves_to_ip边应包含record_type, final_parse, max_ttl, min_ttl属性
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DomainResolvesToIpEdge extends BaseEdge {
    /**
     *  DNS解析服务器IP
     */
    private String dnsServer;

    /**
     * 记录类型，如"A"或"AAAA"
     */
    private String recordType = "A";

    /**
     * 是否为最终解析
     */
    private Boolean finalParse = true;

    /**
     * 最大有效期
     */
    private Integer maxTtl = 0;

    /**
     * 最小有效期
     */
    private Integer minTtl = 0;

    /**
     * 获取边类型
     *
     * @return 边类型常量
     */
    @Override
    public EdgeType getEdgeType() {
        return EdgeType.DOMAIN_RESOLVES_TO_IP;
    }
}
