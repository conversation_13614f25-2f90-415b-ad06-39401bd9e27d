package com.geeksec.nta.graphbuilder.model.edge;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * IP到MAC映射边
 * 表示IP地址和MAC地址之间的映射关系
 * 对应图数据库中的ip_maps_to_mac边类型
 *
 * 注意：IP和MAC之间的映射关系本质上是相同的，不需要区分源和目标
 *
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IpMapsToMacEdge extends BaseEdge {

    /**
     * 获取边类型
     *
     * @return 边类型枚举
     */
    @Override
    public EdgeType getEdgeType() {
        return EdgeType.IP_MAPS_TO_MAC;
    }
}
