package com.geeksec.nta.graphbuilder.model.vertex;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 操作系统顶点
 * 表示操作系统信息
 *
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OsVertex extends BaseVertex {
    /**
     * 操作系统名称
     */
    private String osName;

    /**
     * 操作系统版本
     */
    private String osVersion = "";

    @Override
    public String getVertexId() {
        return osName;
    }

    /**
     * 获取顶点标签
     *
     * @return 顶点标签常量
     */
    @Override
    public VertexTag getVertexTag() {
        return VertexTag.OS;
    }
}
