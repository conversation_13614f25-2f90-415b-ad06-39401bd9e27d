package com.geeksec.nta.graphbuilder.model.vertex;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * MAC顶点
 * 表示MAC地址信息
 *
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MacVertex extends BaseVertex {
    /**
     * MAC地址
     */
    private String mac;

    /**
     * VLAN信息
     */
    private String vlanInfo;

    /**
     * 威胁分数
     */
    private Integer threatScore;

    /**
     * 信任分数
     */
    private Integer trustScore;

    /**
     * 备注
     */
    private String remark;

    @Override
    public String getVertexId() {
        return mac;
    }

    /**
     * 获取顶点标签
     *
     * @return 顶点标签枚举
     */
    @Override
    public VertexTag getVertexTag() {
        return VertexTag.MAC;
    }
}
