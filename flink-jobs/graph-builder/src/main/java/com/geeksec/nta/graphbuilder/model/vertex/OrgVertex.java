package com.geeksec.nta.graphbuilder.model.vertex;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织顶点
 * 表示组织信息
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrgVertex extends BaseVertex {

    /**
     * 组织名称
     */
    private String orgName;

    /**
     * 组织描述
     */
    private String orgDesc;

    /**
     * 威胁分数
     */
    private Integer threatScore;

    /**
     * 信任分数
     */
    private Integer trustScore;

    /**
     * 备注
     */
    private String remark;

    /**
     * 获取顶点ID
     *
     * @return 顶点ID
     */
    @Override
    public String getVertexId() {
        return orgName;
    }

    /**
     * 获取顶点标签
     *
     * @return 顶点标签常量
     */
    @Override
    public VertexTag getVertexTag() {
        return VertexTag.ORG;
    }
}
