package com.geeksec.nta.graphbuilder.model.edge;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 证书作为CA签发其他证书边
 * 表示CA证书与其签发的子证书之间的签发关系
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CertSignsCertEdge extends BaseEdge {
    
    /**
     * 获取边类型
     *
     * @return 边类型常量
     */
    @Override
    public EdgeType getEdgeType() {
        return EdgeType.CERT_SIGNS_CERT;
    }
}
