package com.geeksec.nta.graphbuilder.model.vertex;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 基础域名顶点
 * 表示从完整域名中提取的基本部分，如从mail.example.com提取出example.com
 *
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseDomainVertex extends BaseVertex {
    /**
     * 基础域名地址
     */
    private String baseDomainAddr;

    /**
     * 威胁分数
     */
    private Integer threatScore;

    /**
     * 信任分数
     */
    private Integer trustScore;

    /**
     * 备注
     */
    private String remark;

    @Override
    public String getVertexId() {
        return baseDomainAddr;
    }

    /**
     * 获取顶点标签
     *
     * @return 顶点标签常量
     */
    @Override
    public VertexTag getVertexTag() {
        return VertexTag.BASE_DOMAIN;
    }
}
