package com.geeksec.nta.graphbuilder.model.vertex;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * URL顶点
 * 表示URL信息
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UrlVertex extends BaseVertex {

    /**
     * URL
     */
    private String url;

    /**
     * 威胁分数
     */
    private Integer threatScore;

    /**
     * 信任分数
     */
    private Integer trustScore;

    /**
     * 获取顶点ID
     *
     * @return 顶点ID
     */
    @Override
    public String getVertexId() {
        return url;
    }

    /**
     * 获取顶点标签
     *
     * @return 顶点标签常量
     */
    @Override
    public VertexTag getVertexTag() {
        return VertexTag.URL;
    }
}
