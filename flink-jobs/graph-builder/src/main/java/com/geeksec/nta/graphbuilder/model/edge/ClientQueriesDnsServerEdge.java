package com.geeksec.nta.graphbuilder.model.edge;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 向DNS服务器查询边
 * 表示客户端向DNS服务器发起查询的关系
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ClientQueriesDnsServerEdge extends BaseEdge {
    
    /**
     * DNS查询类型
     */
    private Integer dnsType;
    
    /**
     * DNS应答类型
     */
    private Integer answerType;
    
    /**
     * 获取边类型
     *
     * @return 边类型常量
     */
    @Override
    public EdgeType getEdgeType() {
        return EdgeType.CLIENT_QUERIES_DNS_SERVER;
    }
}
