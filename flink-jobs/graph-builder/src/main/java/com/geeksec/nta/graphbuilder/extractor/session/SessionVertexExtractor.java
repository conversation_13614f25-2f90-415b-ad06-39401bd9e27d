package com.geeksec.nta.graphbuilder.extractor.session;

import com.geeksec.common.utils.net.IpUtils;
import com.geeksec.nta.graphbuilder.extractor.base.BaseVertexExtractor;
import com.geeksec.nta.graphbuilder.model.vertex.AppServiceVertex;
import com.geeksec.nta.graphbuilder.model.vertex.CertVertex;
import com.geeksec.nta.graphbuilder.model.vertex.DomainVertex;
import com.geeksec.nta.graphbuilder.model.vertex.IpVertex;
import com.geeksec.nta.graphbuilder.model.vertex.MacVertex;
import com.geeksec.nta.graphbuilder.utils.net.DomainUtils;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.util.Collector;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 会话顶点提取器
 * 从网络会话中提取顶点数据，包括IP、MAC、域名、应用服务等
 *
 * <AUTHOR> Team
 */
@Slf4j
public class SessionVertexExtractor extends BaseVertexExtractor {

    private static final String BROADCAST_MAC = "ff:ff:ff:ff:ff:ff";

    /**
     * 需要过滤的无效应用服务名称列表
     */
    private static final List<String> ERR_DIRECT_SERVICE = Arrays.asList(
            "TCP_QueryOnly", "No_Payload", "TCP_NoPayload", "UDP_NoPayload",
            "TCP_PortClose", "APP_ICMP_v4", "UDP_Unknown", "APP_ICMP_v6", "APP_IPMessage");

    /**
     * 客户端类型
     */
    private static final String CLIENT_TYPE = "client";

    /**
     * 服务端类型
     */
    private static final String SERVER_TYPE = "server";

    /**
     * 域名端口分隔符
     */
    private static final String DOMAIN_PORT_SEPARATOR = ":";

    /**
     * 从会话信息中生成各类顶点
     *
     * @param pbMap     数据映射
     * @param collector 收集器
     * @throws Exception 如果处理失败
     */
    @Override
    public void flatMap(Map<String, Object> pbMap, Collector<Map<String, Object>> collector) throws Exception {
        // TAG 部分（IP、MAC、域名、应用服务、锚域名、证书信息）

        // 1.clientIp + serverIp Tag
        String clientIp = (String) pbMap.get("sIp");
        String serverIp = (String) pbMap.get("dIp");
        IpVertex clientIpTagInfo = getIpTagInfo(clientIp, "client", pbMap);
        IpVertex serverIpTagInfo = getIpTagInfo(serverIp, "server", pbMap);

        // 2.clientMac + serverMac Tag
        String clientMac = (String) pbMap.get("sMac");
        String serverMac = (String) pbMap.get("dMac");
        MacVertex clientMacTagInfo = getMacTagInfo(clientMac, pbMap);
        MacVertex serverMacTagInfo = getMacTagInfo(serverMac, pbMap);

        // 3.AppName 应用服务 Tag
        AppServiceVertex appTagInfo = getAppTagInfo(pbMap);

        // 4.会话中的域名信息（HTTP/DNS/SSL）+ 基础域名
        // DOMAIN 域名 TAG
        List<DomainVertex> domainTagInfoList = getDomainTagInfo(pbMap);
        // BASE_DOMAIN 基础域名 Tag
        List<String> baseDomainInfoTagList = new ArrayList<>();
        if (!domainTagInfoList.isEmpty()) {
            for (DomainVertex domainTagInfo : domainTagInfoList) {
                String baseDomainAddr = getBaseDomain(domainTagInfo.getDomainAddr());
                if (!StringUtil.isNullOrEmpty(baseDomainAddr)) {
                    baseDomainInfoTagList.add(baseDomainAddr);
                }
            }
        }

        // 5.证书 CertTagInfo
        List<CertVertex> certTagInfoList = getCertTagInfo(pbMap);

        // 合并由会话元数据产出的Tag和Edge的信息
        HashMap<String, Object> resultMap = new HashMap<>(8);
        resultMap.put("clientIpTag", clientIpTagInfo);
        resultMap.put("serverIpTag", serverIpTagInfo);
        resultMap.put("clientMacTag", clientMacTagInfo);
        resultMap.put("serverMacTag", serverMacTagInfo);
        resultMap.put("appTag", appTagInfo);
        resultMap.put("domainTagList", domainTagInfoList);
        resultMap.put("baseDomainTagList", baseDomainInfoTagList);
        resultMap.put("certTagList", certTagInfoList);

        collector.collect(resultMap);
    }

    /**
     * 生成IP顶点信息
     *
     * @param ipAddr IP地址
     * @param type   类型（client或server）
     * @param pbMap  数据映射
     * @return IP顶点
     */
    private IpVertex getIpTagInfo(String ipAddr, String type, Map<String, Object> pbMap) {
        if (StringUtil.isNullOrEmpty(ipAddr)) {
            return null;
        }

        IpVertex ipVertex = new IpVertex();
        ipVertex.setIpAddr(ipAddr);
        ipVertex.setRemark("");

        // 获取地理位置信息
        String ipCountry = "";
        String ipCity = "";

        if (CLIENT_TYPE.equals(type)) {
            ipCountry = (String) pbMap.get("sIpCountry");
            ipCity = (String) pbMap.get("sIpCity");
        } else if (SERVER_TYPE.equals(type)) {
            ipCountry = (String) pbMap.get("dIpCountry");
            ipCity = (String) pbMap.get("dIpCity");
        }

        ipVertex.setCountry(ipCountry != null ? ipCountry : "");
        ipVertex.setCity(ipCity != null ? ipCity : "");

        return ipVertex;
    }

    /**
     * 生成MAC顶点信息
     *
     * @param macAddr MAC地址
     * @param pbMap   数据映射
     * @return MAC顶点
     */
    private MacVertex getMacTagInfo(String macAddr, Map<String, Object> pbMap) {
        if (StringUtil.isNullOrEmpty(macAddr) || BROADCAST_MAC.equals(macAddr)) {
            return null;
        }

        MacVertex macVertex = new MacVertex();
        macVertex.setMac(macAddr);
        macVertex.setRemark("");

        return macVertex;
    }

    /**
     * 生成应用服务顶点信息
     *
     * @param pbMap 数据映射
     * @return 应用服务顶点
     */
    private AppServiceVertex getAppTagInfo(Map<String, Object> pbMap) {
        String appName = (String) pbMap.get("AppName");

        // 过滤无效服务
        if (ERR_DIRECT_SERVICE.contains(appName)) {
            return null;
        }

        AppServiceVertex appServiceVertex = new AppServiceVertex();
        String hkey = (String) pbMap.get("Hkey");
        String dIp = (String) pbMap.get("dIp");
        Integer dPort = (Integer) pbMap.get("dPort");
        String ipKey;

        // 处理IP键
        if (IpUtils.isValidIpv4(dIp)) {
            if (IpUtils.isInternalIp(dIp)) {
                ipKey = hkey.split("_")[1] + "-" + dIp;
            } else {
                ipKey = dIp;
            }
        } else {
            ipKey = dIp;
        }

        // 设置应用信息
        String serviceKey = ipKey + "_" + dPort + "_" + appName;
        appServiceVertex.setServiceId(serviceKey);
        appServiceVertex.setAppName(appName);
        // 设置IP地址和端口
        appServiceVertex.setIpAddress(dIp);
        appServiceVertex.setPort(dPort);
        appServiceVertex.setIpProtocol("TCP"); // 默认协议
        // 设置默认值
        appServiceVertex.setThreatScore(0);
        appServiceVertex.setTrustScore(0);
        appServiceVertex.setRemark("");

        return appServiceVertex;
    }

    /**
     * 获取域名顶点列表
     *
     * @param pbMap 数据映射
     * @return 域名顶点列表
     */
    private List<DomainVertex> getDomainTagInfo(Map<String, Object> pbMap) {
        List<DomainVertex> domainVertexList = new ArrayList<>();

        // 获取HTTP信息列表
        List<HashMap<String, Object>> httpInfoList = (List<HashMap<String, Object>>) pbMap.get("HTTP");

        // 获取DNS信息列表
        List<HashMap<String, Object>> dnsInfoList = (List<HashMap<String, Object>>) pbMap.get("DNS");

        // 获取SSL信息列表
        List<HashMap<String, Object>> sslInfoList = (List<HashMap<String, Object>>) pbMap.get("SSL");

        // 处理HTTP中的域名
        processHttpDomains(pbMap, httpInfoList, domainVertexList);

        // 处理DNS中的域名
        processDnsDomains(pbMap, dnsInfoList, domainVertexList);

        // 处理SSL中的域名
        processSslDomains(pbMap, sslInfoList, domainVertexList);

        return domainVertexList;
    }

    /**
     * 处理HTTP中的域名信息
     *
     * @param pbMap            数据映射
     * @param httpInfoList     HTTP信息列表
     * @param domainVertexList 域名顶点列表
     */
    private void processHttpDomains(Map<String, Object> pbMap, List<HashMap<String, Object>> httpInfoList,
            List<DomainVertex> domainVertexList) {
        if (CollectionUtils.isNotEmpty(httpInfoList)) {
            for (HashMap<String, Object> httpMap : httpInfoList) {
                String response = (String) httpMap.get("Response");
                try {
                    if (!StringUtil.isNullOrEmpty(response)) {
                        String httpDomainAddr = (String) httpMap.get("Host");
                        if (DomainUtils.isValidDomain(httpDomainAddr)) {
                            DomainVertex httpDomainVertex = new DomainVertex();
                            if (httpDomainAddr.contains(DOMAIN_PORT_SEPARATOR)) {
                                httpDomainVertex.setDomainAddr(httpDomainAddr.split(DOMAIN_PORT_SEPARATOR)[0]);
                            } else {
                                httpDomainVertex.setDomainAddr(httpDomainAddr);
                            }
                            httpDomainVertex.setThreatScore(0);
                            httpDomainVertex.setTrustScore(0);
                            httpDomainVertex.setRemark("");
                            domainVertexList.add(httpDomainVertex);
                        }
                    }
                } catch (Exception e) {
                    log.warn("处理HTTP域名信息失败: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 处理DNS中的域名信息
     *
     * @param pbMap            数据映射
     * @param dnsInfoList      DNS信息列表
     * @param domainVertexList 域名顶点列表
     */
    private void processDnsDomains(Map<String, Object> pbMap, List<HashMap<String, Object>> dnsInfoList,
            List<DomainVertex> domainVertexList) {
        if (CollectionUtils.isNotEmpty(dnsInfoList)) {
            for (HashMap<String, Object> dnsMap : dnsInfoList) {
                String dnsDomainAddr = (String) dnsMap.get("Domain");
                if (DomainUtils.isValidDomain(dnsDomainAddr)) {
                    DomainVertex dnsDomainVertex = new DomainVertex();
                    dnsDomainVertex.setDomainAddr(dnsDomainAddr);
                    dnsDomainVertex.setThreatScore(0);
                    dnsDomainVertex.setTrustScore(0);
                    dnsDomainVertex.setRemark("");
                    domainVertexList.add(dnsDomainVertex);
                }
            }
        }
    }

    /**
     * 处理SSL中的域名信息
     *
     * @param pbMap            数据映射
     * @param sslInfoList      SSL信息列表
     * @param domainVertexList 域名顶点列表
     */
    private void processSslDomains(Map<String, Object> pbMap, List<HashMap<String, Object>> sslInfoList,
            List<DomainVertex> domainVertexList) {
        if (CollectionUtils.isNotEmpty(sslInfoList)) {
            HashMap<String, Object> sslMap = sslInfoList.get(0);
            String sslDomainAddr = (String) sslMap.get("CH_ServerName");
            if (DomainUtils.isValidDomain(sslDomainAddr)) {
                DomainVertex sslDomainVertex = new DomainVertex();
                if (sslDomainAddr.contains(DOMAIN_PORT_SEPARATOR)) {
                    sslDomainVertex.setDomainAddr(sslDomainAddr.split(DOMAIN_PORT_SEPARATOR)[0]);
                } else {
                    sslDomainVertex.setDomainAddr(sslDomainAddr);
                }
                sslDomainVertex.setThreatScore(0);
                sslDomainVertex.setTrustScore(0);
                sslDomainVertex.setRemark("");
                domainVertexList.add(sslDomainVertex);
            }
        }
    }

    /**
     * 获取证书顶点列表
     *
     * @param pbMap 数据映射
     * @return 证书顶点列表
     */
    private List<CertVertex> getCertTagInfo(Map<String, Object> pbMap) {
        List<CertVertex> certVertexList = new ArrayList<>();

        List<HashMap<String, Object>> sslInfoList = (List<HashMap<String, Object>>) pbMap.get("SSL");

        if (CollectionUtils.isEmpty(sslInfoList)) {
            return certVertexList;
        }

        HashMap<String, Object> sslMap = sslInfoList.get(0);
        List<String> certIds = Stream.of(
                (List<String>) sslMap.get("sCertHash"),
                (List<String>) sslMap.get("dCertHash"))
                .filter(list -> list != null)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(certIds)) {
            for (String certId : certIds) {
                CertVertex certVertex = new CertVertex();
                certVertex.setCertId(certId);
                certVertex.setThreatScore(0);
                certVertex.setTrustScore(0);
                certVertex.setRemark("");
                certVertexList.add(certVertex);
            }
        }

        return certVertexList;
    }
}
