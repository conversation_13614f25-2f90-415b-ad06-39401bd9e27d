package com.geeksec.nta.graphbuilder.model.vertex;

import java.io.Serializable;

import lombok.Data;

/**
 * 基础顶点类
 * 所有顶点实体的基类
 *
 * <AUTHOR>
 */
@Data
public abstract class BaseVertex implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 获取顶点ID
     *
     * @return 顶点ID
     */
    public abstract String getVertexId();

    /**
     * 获取顶点标签
     *
     * @return 顶点标签枚举
     */
    public abstract VertexTag getVertexTag();
}
