package com.geeksec.nta.graphbuilder.model.edge;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TLS指纹与域名一同出现边
 * 表示TLS指纹与域名在同一会话中被观察到的关系
 * 根据nebula-init.yaml中的定义，fingerprint_appears_with_domain边不包含任何属性
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FingerprintAppearsWithDomainEdge extends BaseEdge {

    /**
     * 获取边类型
     *
     * @return 边类型常量
     */
    @Override
    public EdgeType getEdgeType() {
        return EdgeType.FINGERPRINT_APPEARS_WITH_DOMAIN;
    }
}
