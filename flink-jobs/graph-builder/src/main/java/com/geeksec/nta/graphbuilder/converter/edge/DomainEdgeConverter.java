package com.geeksec.nta.graphbuilder.converter.edge;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import org.apache.flink.streaming.api.functions.ProcessFunction;
import com.geeksec.nta.graphbuilder.io.sink.tag.NebulaGraphOutputTag;
import com.geeksec.nta.graphbuilder.model.edge.BaseEdge;
import com.geeksec.nta.graphbuilder.model.edge.ClientQueriesDomainEdge;
import com.geeksec.nta.graphbuilder.model.edge.DomainResolvesToIpEdge;


import lombok.extern.slf4j.Slf4j;

/**
 * 域名边转换器
 * 处理域名相关的边数据（域名解析、域名关联），将其转换为Flink Row格式并直接输出到侧输出流
 *
 * <AUTHOR>
 */
@Slf4j
public class DomainEdgeConverter extends ProcessFunction<Map<String, Object>, Row> {

    private static final long serialVersionUID = 1L;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void processElement(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) throws Exception {
        // 处理域名到基础域名边
        processDomainToBaseDomainEdges(edgeMap, ctx, out);

        // 处理DNS查询边
        processDnsQueryEdges(edgeMap, ctx, out);

        // 处理DNS解析边
        processDnsResolveEdges(edgeMap, ctx, out);

        // 处理CNAME别名边
        processCnameAliasEdges(edgeMap, ctx, out);
    }

    /**
     * 处理域名到基础域名边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processDomainToBaseDomainEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理域名到基础域名边列表
        List<BaseEdge> domainToBaseDomainEdgeList = (List<BaseEdge>) edgeMap.get("domainToBaseDomainEdgeList");
        if (!CollectionUtils.isEmpty(domainToBaseDomainEdgeList)) {
            for (BaseEdge edge : domainToBaseDomainEdgeList) {
                Row domainToBaseDomainRow = new Row(2);
                domainToBaseDomainRow.setField(0, edge.getSrcId());
                domainToBaseDomainRow.setField(1, edge.getDstId());

                // 直接输出到域名解析边的侧输出流
                ctx.output(NebulaGraphOutputTag.Edge.DOMAIN_RESOLVE, domainToBaseDomainRow);
            }
        }
    }

    /**
     * 处理DNS查询边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processDnsQueryEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理客户端查询域名边
        ClientQueriesDomainEdge clientQueryDomainEdge = (ClientQueriesDomainEdge) edgeMap.get("clientQueryDomainEdge");
        if (!ObjectUtils.isEmpty(clientQueryDomainEdge)) {
            Row clientQueryDomainRow = new Row(4);
            clientQueryDomainRow.setField(0, clientQueryDomainEdge.getSrcId());
            clientQueryDomainRow.setField(1, clientQueryDomainEdge.getDstId());
            clientQueryDomainRow.setField(2, clientQueryDomainEdge.getDnsType());
            clientQueryDomainRow.setField(3, clientQueryDomainEdge.getAnswerType());

            // 直接输出到域名访问边的侧输出流
            ctx.output(NebulaGraphOutputTag.Edge.DOMAIN_ACCESS, clientQueryDomainRow);
        }

        // 处理客户端查询DNS服务器边
        BaseEdge clientQueryDnsServerEdge = (BaseEdge) edgeMap.get("clientQueryDnsServerEdge");
        if (!ObjectUtils.isEmpty(clientQueryDnsServerEdge)) {
            Row clientQueryDnsServerRow = new Row(2);
            clientQueryDnsServerRow.setField(0, clientQueryDnsServerEdge.getSrcId());
            clientQueryDnsServerRow.setField(1, clientQueryDnsServerEdge.getDstId());

            // 直接输出到IP连接边的侧输出流
            ctx.output(NebulaGraphOutputTag.Edge.IP_CONNECT, clientQueryDnsServerRow);
        }

        // 处理DNS服务器解析域名边
        BaseEdge dnsServerResolveDomainEdge = (BaseEdge) edgeMap.get("dnsServerResolveDomainEdge");
        if (!ObjectUtils.isEmpty(dnsServerResolveDomainEdge)) {
            Row dnsServerResolveDomainRow = new Row(2);
            dnsServerResolveDomainRow.setField(0, dnsServerResolveDomainEdge.getSrcId());
            dnsServerResolveDomainRow.setField(1, dnsServerResolveDomainEdge.getDstId());

            // 直接输出到域名解析边的侧输出流
            ctx.output(NebulaGraphOutputTag.Edge.DOMAIN_RESOLVE, dnsServerResolveDomainRow);
        }
    }

    /**
     * 处理DNS解析边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processDnsResolveEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理域名解析到IP边列表
        List<DomainResolvesToIpEdge> dnsParseToEdgeList = (List<DomainResolvesToIpEdge>) edgeMap.get("dnsParseToEdgeList");
        if (!CollectionUtils.isEmpty(dnsParseToEdgeList)) {
            for (DomainResolvesToIpEdge edge : dnsParseToEdgeList) {
                Row dnsParseToEdgeRow = new Row(6);
                dnsParseToEdgeRow.setField(0, edge.getSrcId());
                dnsParseToEdgeRow.setField(1, edge.getDstId());
                dnsParseToEdgeRow.setField(2, edge.getDnsServer());
                dnsParseToEdgeRow.setField(3, edge.getFinalParse());
                dnsParseToEdgeRow.setField(4, edge.getMaxTtl());
                dnsParseToEdgeRow.setField(5, edge.getMinTtl());

                // 直接输出到域名解析边的侧输出流
                ctx.output(NebulaGraphOutputTag.Edge.DOMAIN_RESOLVE, dnsParseToEdgeRow);
            }
        }
    }

    /**
     * 处理CNAME别名边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processCnameAliasEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理CNAME别名域名边列表
        List<BaseEdge> cnameAliasDomainEdgeList = (List<BaseEdge>) edgeMap.get("cnameAliasDomainEdgeList");
        if (!CollectionUtils.isEmpty(cnameAliasDomainEdgeList)) {
            for (BaseEdge edge : cnameAliasDomainEdgeList) {
                Row cnameAliasDomainRow = new Row(2);
                cnameAliasDomainRow.setField(0, edge.getSrcId());
                cnameAliasDomainRow.setField(1, edge.getDstId());

                // 直接输出到域名解析边的侧输出流
                ctx.output(NebulaGraphOutputTag.Edge.DOMAIN_RESOLVE, cnameAliasDomainRow);
            }
        }
    }
}
