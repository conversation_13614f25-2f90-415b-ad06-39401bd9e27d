package com.geeksec.nta.graphbuilder.model.edge;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * IP呈现指纹边
 * 表示IP与指纹之间的呈现关系
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IpPresentsFingerprintEdge extends BaseEdge {

    /**
     * 角色（1-客户端，2-服务端）
     */
    private Integer role;

    /**
     * 获取边类型
     *
     * @return 边类型常量
     */
    @Override
    public EdgeType getEdgeType() {
        return EdgeType.IP_PRESENTS_FINGERPRINT;
    }
}
