package com.geeksec.nta.graphbuilder.model.edge;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * IP提供证书边
 * 表示IP与证书之间的关系，通过role属性区分客户端和服务端
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IpProvidesCertEdge extends BaseEdge {
    
    /**
     * 角色，可为client或server
     */
    private String role;
    
    /**
     * SNI信息
     */
    private String sni;
    
    /**
     * 获取边类型
     *
     * @return 边类型常量
     */
    @Override
    public EdgeType getEdgeType() {
        return EdgeType.IP_PROVIDES_CERT;
    }
}
