package com.geeksec.nta.graphbuilder.model.edge;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户端访问应用边
 * 表示客户端IP与应用之间的访问关系
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ClientAccessesAppEdge extends BaseEdge {
    
    /**
     * 客户端IP地址
     */
    private String clientIp;
    
    /**
     * 应用名称
     */
    private String appName;
    
    /**
     * 目的端口
     */
    private Integer dPort;
    
    /**
     * 获取边类型
     *
     * @return 边类型常量
     */
    @Override
    public EdgeType getEdgeType() {
        return EdgeType.CLIENT_ACCESSES_APP;
    }
}
