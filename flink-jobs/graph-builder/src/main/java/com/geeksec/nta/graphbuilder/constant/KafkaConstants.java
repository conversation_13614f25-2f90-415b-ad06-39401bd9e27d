package com.geeksec.nta.graphbuilder.constant;

/**
 * Kafka相关常量类
 * 定义了所有与Kafka相关的常量，包括主题名称、消费者组ID等
 *
 * <AUTHOR> Team
 */
public class KafkaConstants {

    /**
     * Kafka主题常量
     */
    public static final class Topic {
        // 会话信息主题
        public static final String CONNECT = "enriched-connect-info";

        // DNS信息主题
        public static final String DNS = "enriched-dns-info";

        // HTTP信息主题
        public static final String HTTP = "enriched-http-info";

        // SSL信息主题
        public static final String SSL = "enriched-ssl-info";

        // 证书信息主题
        public static final String CERT = "cert-info";
    }

    /**
     * Kafka消费者组ID常量
     */
    public static final class GroupId {
        // 图构建消费者组前缀
        public static final String PREFIX = "graph-builder";

        // 会话信息消费者组
        public static final String CONNECT = PREFIX + "-connect";

        // DNS信息消费者组
        public static final String DNS = PREFIX + "-dns";

        // HTTP信息消费者组
        public static final String HTTP = PREFIX + "-http";

        // SSL信息消费者组
        public static final String SSL = PREFIX + "-ssl";

        // 证书信息消费者组
        public static final String CERT = PREFIX + "-cert";
    }
}
