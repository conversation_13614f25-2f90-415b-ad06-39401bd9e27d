package com.geeksec.nta.graphbuilder.model.edge;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * IP连接边
 * 表示两个IP地址之间的网络连接关系
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IpConnectsToIpEdge extends BaseEdge {

    /**
     * 目的端口
     */
    private Integer dport;

    /**
     * 获取边类型
     *
     * @return 边类型枚举
     */
    @Override
    public EdgeType getEdgeType() {
        return EdgeType.IP_CONNECTS_TO_IP;
    }
}
