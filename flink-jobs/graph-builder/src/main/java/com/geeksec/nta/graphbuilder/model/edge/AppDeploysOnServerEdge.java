package com.geeksec.nta.graphbuilder.model.edge;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 应用部署在服务器边
 * 表示应用与服务器IP之间的部署关系
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AppDeploysOnServerEdge extends BaseEdge {
    
    /**
     * 服务器IP地址
     */
    private String serverIp;
    
    /**
     * 应用名称
     */
    private String appName;
    
    /**
     * 目的端口
     */
    private Integer dPort;
    
    /**
     * 获取边类型
     *
     * @return 边类型常量
     */
    @Override
    public EdgeType getEdgeType() {
        return EdgeType.APP_DEPLOYS_ON_SERVER;
    }
}
