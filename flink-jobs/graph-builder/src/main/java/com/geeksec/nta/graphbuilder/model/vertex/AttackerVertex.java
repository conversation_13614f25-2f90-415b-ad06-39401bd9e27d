package com.geeksec.nta.graphbuilder.model.vertex;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 攻击者标签
 * 表示攻击者角色标记，应用于IP节点
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AttackerVertex extends BaseVertex {

    /**
     * 攻击者ID（通常是IP地址）
     */
    private String attackerId;

    /**
     * 获取顶点ID
     *
     * @return 顶点ID
     */
    @Override
    public String getVertexId() {
        return attackerId;
    }

    /**
     * 获取顶点标签
     * 注意：攻击者是IP节点的一个标签，而不是独立的顶点类型
     *
     * @return 顶点标签常量
     */
    @Override
    public VertexTag getVertexTag() {
        /* 攻击者标签应用于IP节点 */
        return VertexTag.IP;
    }

    /**
     * 获取角色标签
     *
     * @return 角色标签常量
     */
    public VertexTag getRoleTag() {
        return VertexTag.ATTACKER;
    }
}
