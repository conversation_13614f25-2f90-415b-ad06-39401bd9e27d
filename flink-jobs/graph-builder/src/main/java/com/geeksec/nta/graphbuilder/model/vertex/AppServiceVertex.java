package com.geeksec.nta.graphbuilder.model.vertex;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 应用服务顶点类
 * 存储服务端应用程序的详细信息
 *
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AppServiceVertex extends BaseVertex {

    /**
     * 应用服务ID
     * 格式：服务键（ip + dPort + AppName）
     */
    private String serviceId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 服务端口
     */
    private Integer port;

    /**
     * IP协议
     */
    private String ipProtocol;

    /**
     * 威胁分数
     */
    private Integer threatScore;

    /**
     * 信任分数
     */
    private Integer trustScore;

    /**
     * 备注
     */
    private String remark;

    @Override
    public String getVertexId() {
        return serviceId;
    }

    /**
     * 获取顶点标签
     *
     * @return 顶点标签常量
     */
    @Override
    public VertexTag getVertexTag() {
        return VertexTag.APPSERVICE;
    }
}
