package com.geeksec.nta.graphbuilder.extractor.session;

import com.geeksec.nta.graphbuilder.utils.net.DomainUtils;
import com.geeksec.nta.graphbuilder.model.edge.EdgeType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.util.Collector;

import com.geeksec.common.utils.net.IpUtils;
import com.geeksec.nta.graphbuilder.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.graphbuilder.model.edge.BaseEdge;
import com.geeksec.nta.graphbuilder.model.edge.ClientAccessesAppEdge;
import com.geeksec.nta.graphbuilder.model.edge.AppDeploysOnServerEdge;
import com.geeksec.nta.graphbuilder.model.edge.IpConnectsToIpEdge;
import com.geeksec.nta.graphbuilder.model.edge.IpMapsToMacEdge;
import com.geeksec.nta.graphbuilder.model.edge.MacConnectsToMacEdge;

import lombok.extern.slf4j.Slf4j;

/**
 * 会话边提取器
 * 从网络会话中提取边关系数据，包括IP绑定关系、连接关系和应用关系
 *
 * <AUTHOR> Team
 */
@Slf4j
public class SessionEdgeExtractor extends BaseEdgeExtractor {

    /**
     * 从会话信息中生成各类边关系
     *
     * @param pbMap 数据映射
     * @param collector 收集器
     * @throws Exception 如果处理失败
     */
    @Override
    public void flatMap(Map<String, Object> pbMap, Collector<Map<String, Object>> collector) throws Exception {
        // 获取PKT发送字节/包信息
        Map<String, Object> pktMap = (Map<String, Object>) pbMap.get("pktInfo");

        // Edge部分
        // 会话部分
        // 1. 源IP--->源MAC 发方关联 & 目的MAC---->目的IP 收方关联
        IpMapsToMacEdge srcBindEdge = getBindEdge(pbMap, CLIENT_TYPE);
        IpMapsToMacEdge dstBindEdge = getBindEdge(pbMap, SERVER_TYPE);

        // 2.connect 链接信息 源IP--->目的IP & 源MAC--->目的MAC
        IpConnectsToIpEdge ipConnectEdge = (IpConnectsToIpEdge) getConnectEdge(pbMap, IP_TYPE);
        MacConnectsToMacEdge macConnectEdge = (MacConnectsToMacEdge) getConnectEdge(pbMap, MAC_TYPE);

        // 3.域名--->基础域名 归属域名
        List<BaseEdge> domainToBaseDomainEdgeList = getDomainToBaseDomainEdge(pbMap);

        // 4.Client --->应用服务 客户端IP访问服务
        ClientAccessesAppEdge clientAppEdge = getClientAccessesAppEdge(pbMap);
        AppDeploysOnServerEdge serverAppEdge = getAppDeploysOnServerEdge(pbMap);

        // 合并由会话元数据产出的Tag和Edge的信息
        HashMap<String, Object> resultMap = new HashMap<>(8);
        resultMap.put("srcBindEdge", srcBindEdge);
        resultMap.put("dstBindEdge", dstBindEdge);
        resultMap.put("ipConnectEdge", ipConnectEdge);
        resultMap.put("macConnectEdge", macConnectEdge);
        resultMap.put("domainToBaseDomainEdgeList", domainToBaseDomainEdgeList);
        resultMap.put("clientAppEdge", clientAppEdge);
        resultMap.put("serverAppEdge", serverAppEdge);

        collector.collect(resultMap);
    }

    /**
     * IP到MAC映射关系
     *
     * @param pbMap 数据映射
     * @param type 类型（client或server）
     * @return IP到MAC映射边
     */
    private IpMapsToMacEdge getBindEdge(Map<String, Object> pbMap, String type) {
        if (pbMap == null) {
            return null;
        }

        IpMapsToMacEdge bindEdge = new IpMapsToMacEdge();
        if (CLIENT_TYPE.equals(type)) {
            // 源IP到源MAC的映射
            bindEdge.setSrcId((String) pbMap.get("sIp"));
            bindEdge.setDstId((String) pbMap.get("sMac"));
        } else if (SERVER_TYPE.equals(type)) {
            // 目标IP到目标MAC的映射
            bindEdge.setSrcId((String) pbMap.get("dIp"));
            bindEdge.setDstId((String) pbMap.get("dMac"));
        }

        return bindEdge;
    }

    /**
     * 连接边
     *
     * @param pbMap 数据映射
     * @param type 类型（ip或mac）
     * @return 连接边
     */
    private BaseEdge getConnectEdge(Map<String, Object> pbMap, String type) {
        Map<String, Object> pktMap = (Map<String, Object>) pbMap.get("pktInfo");
        if (pktMap == null) {
            return null;
        }

        if (IP_TYPE.equals(type)) {
            IpConnectsToIpEdge ipConnectEdge = new IpConnectsToIpEdge();
            ipConnectEdge.setSrcId((String) pbMap.get("sIp"));
            ipConnectEdge.setDstId((String) pbMap.get("dIp"));

            // 根据nebula-init.yaml中的定义，ip_connects_to_ip边只应包含dport属性
            ipConnectEdge.setDport((Integer) pbMap.get("dPort"));
            return ipConnectEdge;
        } else if (MAC_TYPE.equals(type)) {
            MacConnectsToMacEdge macConnectEdge = new MacConnectsToMacEdge();
            macConnectEdge.setSrcId((String) pbMap.get("sMac"));
            macConnectEdge.setDstId((String) pbMap.get("dMac"));
            return macConnectEdge;
        }

        return null;
    }

    /**
     * 域名到基础域名边
     *
     * @param pbMap 数据映射
     * @return 域名到基础域名边列表
     */
    private List<BaseEdge> getDomainToBaseDomainEdge(Map<String, Object> pbMap) {
        List<BaseEdge> domainToBaseDomainEdgeList = new ArrayList<>();

        // 获取HTTP信息列表
        List<HashMap<String, Object>> httpInfoList = (List<HashMap<String, Object>>) pbMap.get("HTTP");

        // 获取DNS信息列表
        List<HashMap<String, Object>> dnsInfoList = (List<HashMap<String, Object>>) pbMap.get("DNS");

        // 获取SSL信息列表
        List<HashMap<String, Object>> sslInfoList = (List<HashMap<String, Object>>) pbMap.get("SSL");

        // 处理HTTP中的域名
        processDomainToBaseDomainEdges(pbMap, httpInfoList, domainToBaseDomainEdgeList, "HTTP");

        // 处理DNS中的域名
        processDomainToBaseDomainEdges(pbMap, dnsInfoList, domainToBaseDomainEdgeList, "DNS");

        // 处理SSL中的域名
        processDomainToBaseDomainEdges(pbMap, sslInfoList, domainToBaseDomainEdgeList, "SSL");

        return domainToBaseDomainEdgeList;
    }

    /**
     * 处理域名到基础域名边
     *
     * @param pbMap 数据映射
     * @param infoList 信息列表
     * @param edgeList 边列表
     * @param type 类型（HTTP、DNS或SSL）
     */
    private void processDomainToBaseDomainEdges(Map<String, Object> pbMap, List<HashMap<String, Object>> infoList,
            List<BaseEdge> edgeList, String type) {
        if (CollectionUtils.isEmpty(infoList)) {
            return;
        }

        for (HashMap<String, Object> infoMap : infoList) {
            String domainAddr = null;

            if ("HTTP".equals(type)) {
                domainAddr = (String) infoMap.get("Host");
            } else if ("DNS".equals(type)) {
                domainAddr = (String) infoMap.get("Domain");
            } else if ("SSL".equals(type)) {
                domainAddr = (String) infoMap.get("CH_ServerName");
            }

            if (StringUtil.isNullOrEmpty(domainAddr) || !DomainUtils.isValidDomain(domainAddr)) {
                continue;
            }

            // 处理域名中可能包含的端口
            if (domainAddr != null && domainAddr.contains(":")) {
                domainAddr = domainAddr.split(":")[0];
            }

            // 获取基础域名
            String baseDomainAddr = getBaseDomain(domainAddr);
            if (StringUtil.isNullOrEmpty(baseDomainAddr)) {
                continue;
            }

            // 创建域名派生自基础域名边
            BaseEdge edge = new BaseEdge() {
                @Override
                        public EdgeType getEdgeType() {
                            return EdgeType.DOMAIN_DERIVES_FROM_BASE_DOMAIN;
                        }
            };
            edge.setSrcId(domainAddr);
            edge.setDstId(baseDomainAddr);
            edgeList.add(edge);
        }
    }

    /**
     * 客户端访问应用边
     *
     * @param pbMap 数据映射
     * @return 客户端访问应用边
     */
    private ClientAccessesAppEdge getClientAccessesAppEdge(Map<String, Object> pbMap) {
        String appName = (String) pbMap.get("AppName");
        String dIp = (String) pbMap.get("dIp");
        Integer dPort = (Integer) pbMap.get("dPort");

        // 构建应用ID
        String ipKey;
        if (IpUtils.isValidIpv4(dIp)) {
            if (IpUtils.isInternalIp(dIp)) {
                String hkey = (String) pbMap.get("Hkey");
                ipKey = hkey.split("_")[1] + "-" + dIp;
            } else {
                ipKey = dIp;
            }
        } else {
            ipKey = dIp;
        }
        String appId = ipKey + "_" + dPort + "_" + appName;

        // 创建客户端访问应用边
        ClientAccessesAppEdge edge = new ClientAccessesAppEdge();
        edge.setSrcId((String) pbMap.get("sIp"));
        edge.setDstId(appId);
        edge.setClientIp((String) pbMap.get("sIp"));
        edge.setAppName(appName);
        edge.setDPort(dPort);

        return edge;
    }

    /**
     * 应用部署在服务器边
     *
     * @param pbMap 数据映射
     * @return 应用部署在服务器边
     */
    private AppDeploysOnServerEdge getAppDeploysOnServerEdge(Map<String, Object> pbMap) {
        String appName = (String) pbMap.get("AppName");
        String dIp = (String) pbMap.get("dIp");
        Integer dPort = (Integer) pbMap.get("dPort");

        // 构建应用ID
        String ipKey;
        if (IpUtils.isValidIpv4(dIp)) {
            if (IpUtils.isInternalIp(dIp)) {
                String hkey = (String) pbMap.get("Hkey");
                ipKey = hkey.split("_")[1] + "-" + dIp;
            } else {
                ipKey = dIp;
            }
        } else {
            ipKey = dIp;
        }
        String appId = ipKey + "_" + dPort + "_" + appName;

        // 创建应用部署在服务器边
        AppDeploysOnServerEdge edge = new AppDeploysOnServerEdge();
        edge.setSrcId(dIp);
        edge.setDstId(appId);
        edge.setServerIp(dIp);
        edge.setAppName(appName);
        edge.setDPort(dPort);

        return edge;
    }
}
