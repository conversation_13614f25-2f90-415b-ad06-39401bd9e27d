package com.geeksec.nta.graphbuilder.model.edge;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 应用使用IP边
 * 表示应用程序与IP地址之间的使用关系
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AppUsesIpEdge extends BaseEdge {
    
    /**
     * 获取边类型
     *
     * @return 边类型常量
     */
    @Override
    public EdgeType getEdgeType() {
        return EdgeType.APP_USES_IP;
    }
}
