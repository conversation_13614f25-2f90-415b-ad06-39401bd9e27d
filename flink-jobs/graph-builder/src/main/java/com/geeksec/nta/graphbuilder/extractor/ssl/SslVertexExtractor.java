package com.geeksec.nta.graphbuilder.extractor.ssl;

import com.geeksec.common.model.dpianalysis.ssl.SslSessionLog;
import com.geeksec.common.model.nta.NetProtocol;
import com.geeksec.nta.graphbuilder.utils.net.DomainUtils;
import com.geeksec.nta.graphbuilder.model.vertex.BaseDomainVertex;
import com.geeksec.common.utils.net.IpUtils;
import com.geeksec.nta.graphbuilder.extractor.base.BaseVertexExtractor;
import com.geeksec.nta.graphbuilder.model.vertex.IpVertex;
import com.geeksec.nta.graphbuilder.model.vertex.DomainVertex;
import com.geeksec.nta.graphbuilder.model.vertex.CertVertex;
import com.geeksec.nta.graphbuilder.model.vertex.SslFingerprintVertex;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.util.Collector;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SSL顶点提取器
 * 从SSL数据中提取顶点数据，包括域名、IP、证书、指纹等
 *
 * <AUTHOR> Team
 */
@Slf4j
public class SslVertexExtractor extends BaseVertexExtractor {

    /**
     * 从SSL信息中生成各类顶点
     *
     * @param pbMap 数据映射
     * @param collector 收集器
     * @throws Exception 如果处理失败
     */
    @Override
    public void flatMap(Map<String, Object> pbMap, Collector<Map<String, Object>> collector) throws Exception {
        // 获取SSL信息列表
        List<HashMap<String, Object>> sslInfoList = (List<HashMap<String, Object>>) pbMap.get("SSL");
        if (CollectionUtils.isEmpty(sslInfoList)) {
            return;
        }

        // 1. 客户端IP顶点
        String clientIp = (String) pbMap.get("sIp");
        IpVertex clientIpVertex = null;
        if (!StringUtil.isNullOrEmpty(clientIp) && IpUtils.isValidIp(clientIp)) {
            clientIpVertex = new IpVertex();
            clientIpVertex.setIpAddr(clientIp);
            clientIpVertex.setThreatScore(0);
            clientIpVertex.setTrustScore(0);
            clientIpVertex.setRemark("");
        }

        // 2. 服务端IP顶点
        String serverIp = (String) pbMap.get("dIp");
        IpVertex serverIpVertex = null;
        if (!StringUtil.isNullOrEmpty(serverIp) && IpUtils.isValidIp(serverIp)) {
            serverIpVertex = new IpVertex();
            serverIpVertex.setIpAddr(serverIp);
            serverIpVertex.setThreatScore(0);
            serverIpVertex.setTrustScore(0);
            serverIpVertex.setRemark("");
        }

        // 存储所有顶点
        List<DomainVertex> domainVertices = new ArrayList<>();
        List<DomainVertex> baseDomainVertices = new ArrayList<>();
        List<CertVertex> certVertices = new ArrayList<>();
        List<SslFingerprintVertex> fingerprintVertices = new ArrayList<>();

        // 处理每个SSL信息
        for (HashMap<String, Object> sslMap : sslInfoList) {
            String sni = (String) sslMap.get("CH_ServerName");
            List<String> clientCertHashes = (List<String>) sslMap.get("sCertHash");
            List<String> serverCertHashes = (List<String>) sslMap.get("dCertHash");
            String clientFingerprint = (String) sslMap.get("sJa3");
            String serverFingerprint = (String) sslMap.get("dJa3");

            // 3. 域名顶点
            if (!StringUtil.isNullOrEmpty(sni) && DomainUtils.isValidDomain(sni)) {
                // 处理域名中可能包含的端口
                if (sni.contains(":")) {
                    sni = sni.split(":")[0];
                }

                DomainVertex domainVertex = new DomainVertex();
                domainVertex.setDomainAddr(sni);
                domainVertex.setThreatScore(0);
                domainVertex.setTrustScore(0);
                domainVertex.setRemark("");
                domainVertices.add(domainVertex);

                // 4. 基础域名顶点
                String baseDomainAddr = getBaseDomain(sni);
                if (!StringUtil.isNullOrEmpty(baseDomainAddr)) {
                    DomainVertex baseDomainVertex = new DomainVertex();
                    baseDomainVertex.setDomainAddr(baseDomainAddr);
                    baseDomainVertex.setThreatScore(0);
                    baseDomainVertex.setTrustScore(0);
                    baseDomainVertex.setRemark("");
                    baseDomainVertices.add(baseDomainVertex);
                }
            }

            // 5. 证书顶点
            // 处理客户端证书
            if (CollectionUtils.isNotEmpty(clientCertHashes)) {
                for (String certHash : clientCertHashes) {
                    CertVertex certVertex = new CertVertex();
                    certVertex.setCertId(certHash);
                    certVertex.setThreatScore(0);
                    certVertex.setTrustScore(0);
                    certVertex.setRemark("");
                    certVertices.add(certVertex);
                }
            }

            // 处理服务端证书
            if (CollectionUtils.isNotEmpty(serverCertHashes)) {
                for (String certHash : serverCertHashes) {
                    CertVertex certVertex = new CertVertex();
                    certVertex.setCertId(certHash);
                    certVertex.setThreatScore(0);
                    certVertex.setTrustScore(0);
                    certVertex.setRemark("");
                    certVertices.add(certVertex);
                }
            }

            // 6. 指纹顶点
            // 处理客户端指纹
            if (!StringUtil.isNullOrEmpty(clientFingerprint)) {
                SslFingerprintVertex fingerprintVertex = new SslFingerprintVertex();
                fingerprintVertex.setJa3Hash(clientFingerprint);
                fingerprintVertex.setFingerDesc("Client JA3 Fingerprint");
                fingerprintVertex.setType(1); // 1表示客户端
                fingerprintVertices.add(fingerprintVertex);
            }

            // 处理服务端指纹
            if (!StringUtil.isNullOrEmpty(serverFingerprint)) {
                SslFingerprintVertex fingerprintVertex = new SslFingerprintVertex();
                fingerprintVertex.setJa3Hash(serverFingerprint);
                fingerprintVertex.setFingerDesc("Server JA3S Fingerprint");
                fingerprintVertex.setType(2); // 2表示服务端
                fingerprintVertices.add(fingerprintVertex);
            }
        }

        // 合并由SSL元数据产出的顶点信息
        HashMap<String, Object> resultMap = new HashMap<>(8);
        resultMap.put("clientIpVertex", clientIpVertex);
        resultMap.put("serverIpVertex", serverIpVertex);
        resultMap.put("domainVertices", domainVertices);
        resultMap.put("baseDomainVertices", baseDomainVertices);
        resultMap.put("certVertices", certVertices);
        resultMap.put("fingerprintVertices", fingerprintVertices);

        collector.collect(resultMap);
    }
}
