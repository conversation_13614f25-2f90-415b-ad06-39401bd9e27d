package com.geeksec.nta.graphbuilder.extractor.http;

import com.geeksec.nta.graphbuilder.utils.net.DomainUtils;
import com.geeksec.nta.graphbuilder.model.edge.EdgeType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.util.Collector;

import com.geeksec.nta.graphbuilder.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.graphbuilder.model.edge.BaseEdge;

import lombok.extern.slf4j.Slf4j;

/**
 * HTTP边提取器
 * 从HTTP数据中提取边关系数据，包括客户端请求域名、服务端服务域名、UA相关边等
 *
 * <AUTHOR> Team
 */
@Slf4j
public class HttpEdgeExtractor extends BaseEdgeExtractor {

    /**
     * 从HTTP信息中生成各类边关系
     *
     * @param pbMap 数据映射
     * @param collector 收集器
     * @throws Exception 如果处理失败
     */
    @Override
    public void flatMap(Map<String, Object> pbMap, Collector<Map<String, Object>> collector) throws Exception {
        // 获取HTTP信息列表
        List<HashMap<String, Object>> httpInfoList = (List<HashMap<String, Object>>) pbMap.get("HTTP");
        if (CollectionUtils.isEmpty(httpInfoList)) {
            return;
        }

        String clientIp = (String) pbMap.get("sIp");
        String serverIp = (String) pbMap.get("dIp");

        // 存储所有边
        List<BaseEdge> clientHttpRequestsDomainEdges = new ArrayList<>();
        List<BaseEdge> serverHttpServesDomainEdges = new ArrayList<>();
        List<BaseEdge> uaRequestsDomainEdges = new ArrayList<>();
        List<BaseEdge> clientUsesUaEdges = new ArrayList<>();
        List<BaseEdge> uaHasDeviceEdges = new ArrayList<>();
        List<BaseEdge> uaHasOsEdges = new ArrayList<>();
        List<BaseEdge> uaHasAppEdges = new ArrayList<>();

        // 处理每个HTTP信息
        for (HashMap<String, Object> httpMap : httpInfoList) {
            String host = (String) httpMap.get("Host");
            String userAgent = (String) httpMap.get("User-Agent");

            // 跳过无效数据
            if (StringUtil.isNullOrEmpty(host) || !DomainUtils.isValidDomain(host)) {
                continue;
            }

            // 处理域名中可能包含的端口
            if (host.contains(":")) {
                host = host.split(":")[0];
            }

            // 1. 客户端HTTP请求域名边
            if (!StringUtil.isNullOrEmpty(clientIp)) {
                BaseEdge clientHttpRequestsDomainEdge = new BaseEdge() {
                    @Override
                        public EdgeType getEdgeType() {
                            return EdgeType.CLIENT_HTTP_REQUESTS_DOMAIN;
                        }
                };
                clientHttpRequestsDomainEdge.setSrcId(clientIp);
                clientHttpRequestsDomainEdge.setDstId(host);
                clientHttpRequestsDomainEdges.add(clientHttpRequestsDomainEdge);
            }

            // 2. 服务端HTTP服务域名边
            if (!StringUtil.isNullOrEmpty(serverIp)) {
                BaseEdge serverHttpServesDomainEdge = new BaseEdge() {
                    @Override
                        public EdgeType getEdgeType() {
                            return EdgeType.SERVER_HTTP_SERVES_DOMAIN;
                        }
                    };
                serverHttpServesDomainEdge.setSrcId(serverIp);
                serverHttpServesDomainEdge.setDstId(host);
                serverHttpServesDomainEdges.add(serverHttpServesDomainEdge);
            }

            // 处理User-Agent相关边
            if (!StringUtil.isNullOrEmpty(userAgent)) {
                String uaId = String.valueOf(userAgent.hashCode());

                // 3. UA请求域名边
                BaseEdge uaRequestsDomainEdge = new BaseEdge() {
                    @Override
                        public EdgeType getEdgeType() {
                            return EdgeType.UA_REQUESTS_DOMAIN;
                        }
                };
                uaRequestsDomainEdge.setSrcId(uaId);
                uaRequestsDomainEdge.setDstId(host);
                uaRequestsDomainEdges.add(uaRequestsDomainEdge);

                // 4. 客户端使用UA边
                if (!StringUtil.isNullOrEmpty(clientIp)) {
                    BaseEdge clientUsesUaEdge = new BaseEdge() {
                        @Override
                        public EdgeType getEdgeType() {
                            return EdgeType.CLIENT_USES_UA;
                        }
                    };
                    clientUsesUaEdge.setSrcId(clientIp);
                    clientUsesUaEdge.setDstId(uaId);
                    clientUsesUaEdges.add(clientUsesUaEdge);
                }

                // 解析UA信息（设备、操作系统、应用）
                // 这里简化处理，实际应该使用UA解析库
                Map<String, String> uaInfo = parseUserAgent(userAgent);

                // 5. UA包含设备信息边
                if (!StringUtil.isNullOrEmpty(uaInfo.get("device"))) {
                    BaseEdge uaHasDeviceEdge = new BaseEdge() {
                        @Override
                        public EdgeType getEdgeType() {
                            return EdgeType.UA_HAS_DEVICE;
                        }
                    };
                    uaHasDeviceEdge.setSrcId(uaId);
                    uaHasDeviceEdge.setDstId(uaInfo.get("device"));
                    uaHasDeviceEdges.add(uaHasDeviceEdge);
                }

                // 6. UA包含系统信息边
                if (!StringUtil.isNullOrEmpty(uaInfo.get("os"))) {
                    BaseEdge uaHasOsEdge = new BaseEdge() {
                        @Override
                        public EdgeType getEdgeType() {
                            return EdgeType.UA_HAS_OS;
                        }
                    };
                    uaHasOsEdge.setSrcId(uaId);
                    uaHasOsEdge.setDstId(uaInfo.get("os"));
                    uaHasOsEdges.add(uaHasOsEdge);
                }

                // 7. UA包含应用信息边
                if (!StringUtil.isNullOrEmpty(uaInfo.get("app"))) {
                    BaseEdge uaHasAppEdge = new BaseEdge() {
                        @Override
                        public EdgeType getEdgeType() {
                            return EdgeType.UA_HAS_APP;
                        }
                    };
                    uaHasAppEdge.setSrcId(uaId);
                    uaHasAppEdge.setDstId(uaInfo.get("app"));
                    uaHasAppEdges.add(uaHasAppEdge);
                }
            }
        }

        // 合并由HTTP元数据产出的边信息
        HashMap<String, Object> resultMap = new HashMap<>(8);
        resultMap.put("clientHttpRequestsDomainEdges", clientHttpRequestsDomainEdges);
        resultMap.put("serverHttpServesDomainEdges", serverHttpServesDomainEdges);
        resultMap.put("uaRequestsDomainEdges", uaRequestsDomainEdges);
        resultMap.put("clientUsesUaEdges", clientUsesUaEdges);
        resultMap.put("uaHasDeviceEdges", uaHasDeviceEdges);
        resultMap.put("uaHasOsEdges", uaHasOsEdges);
        resultMap.put("uaHasAppEdges", uaHasAppEdges);

        collector.collect(resultMap);
    }

    /**
     * 解析User-Agent字符串
     * 简化处理，实际应该使用专业的UA解析库
     *
     * @param userAgent User-Agent字符串
     * @return 解析结果，包含设备、操作系统、应用信息
     */
    private Map<String, String> parseUserAgent(String userAgent) {
        Map<String, String> result = new HashMap<>();

        // 简化处理，实际应该使用专业的UA解析库
        // 这里只是示例，不能准确解析所有UA

        // 设备信息
        if (userAgent.contains("iPhone")) {
            result.put("device", "iPhone");
        } else if (userAgent.contains("iPad")) {
            result.put("device", "iPad");
        } else if (userAgent.contains("Android")) {
            result.put("device", "Android");
        } else {
            result.put("device", "PC");
        }

        // 操作系统信息
        if (userAgent.contains("Windows")) {
            result.put("os", "Windows");
        } else if (userAgent.contains("Mac OS")) {
            result.put("os", "Mac OS");
        } else if (userAgent.contains("Linux")) {
            result.put("os", "Linux");
        } else if (userAgent.contains("iOS")) {
            result.put("os", "iOS");
        } else if (userAgent.contains("Android")) {
            result.put("os", "Android");
        }

        // 应用信息
        if (userAgent.contains("Chrome")) {
            result.put("app", "Chrome");
        } else if (userAgent.contains("Firefox")) {
            result.put("app", "Firefox");
        } else if (userAgent.contains("Safari")) {
            result.put("app", "Safari");
        } else if (userAgent.contains("Edge")) {
            result.put("app", "Edge");
        } else if (userAgent.contains("MSIE") || userAgent.contains("Trident")) {
            result.put("app", "Internet Explorer");
        }

        return result;
    }
}
