package com.geeksec.nta.graphbuilder.io.sink.tag;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * Nebula Graph输出标签
 * 定义了所有用于Nebula Graph的输出标签
 *
 * <AUTHOR> Team
 */
public class NebulaGraphOutputTag {

    /**
     * 顶点输出标签
     * 用于路由顶点数据到Nebula Graph
     */
    public static final class Vertex {
        /** IP顶点 */
        public static final OutputTag<Row> IP = new OutputTag<>("nebula_vertex_ip", TypeInformation.of(Row.class));

        /** 设备顶点 */
        public static final OutputTag<Row> DEVICE = new OutputTag<>("nebula_vertex_device", TypeInformation.of(Row.class));

        /** 应用顶点 */
        public static final OutputTag<Row> APPLICATION = new OutputTag<>("nebula_vertex_application", TypeInformation.of(Row.class));

        /** 域名顶点 */
        public static final OutputTag<Row> DOMAIN = new OutputTag<>("nebula_vertex_domain", TypeInformation.of(Row.class));

        /** URL顶点 */
        public static final OutputTag<Row> URL = new OutputTag<>("nebula_vertex_url", TypeInformation.of(Row.class));

        /** 证书顶点 */
        public static final OutputTag<Row> CERTIFICATE = new OutputTag<>("nebula_vertex_certificate", TypeInformation.of(Row.class));

        /** 证书颁发者顶点 */
        public static final OutputTag<Row> ISSUER = new OutputTag<>("nebula_vertex_issuer", TypeInformation.of(Row.class));

        /** 证书主体顶点 */
        public static final OutputTag<Row> SUBJECT = new OutputTag<>("nebula_vertex_subject", TypeInformation.of(Row.class));

        /** 组织顶点 */
        public static final OutputTag<Row> ORGANIZATION = new OutputTag<>("nebula_vertex_organization", TypeInformation.of(Row.class));

        /** 基础域名顶点 */
        public static final OutputTag<Row> BASE_DOMAIN = new OutputTag<>("nebula_vertex_base_domain", TypeInformation.of(Row.class));
    }

    /**
     * 边输出标签
     * 用于路由边数据到Nebula Graph
     */
    public static final class Edge {
        /** 设备连接边 */
        public static final OutputTag<Row> DEVICE_CONNECT = new OutputTag<>("nebula_edge_device_connect", TypeInformation.of(Row.class));

        /** 应用连接边 */
        public static final OutputTag<Row> APPLICATION_CONNECT = new OutputTag<>("nebula_edge_application_connect", TypeInformation.of(Row.class));

        /** IP连接边 */
        public static final OutputTag<Row> IP_CONNECT = new OutputTag<>("nebula_edge_ip_connect", TypeInformation.of(Row.class));

        /** 域名解析边 */
        public static final OutputTag<Row> DOMAIN_RESOLVE = new OutputTag<>("nebula_edge_domain_resolve", TypeInformation.of(Row.class));

        /** 域名访问边 */
        public static final OutputTag<Row> DOMAIN_ACCESS = new OutputTag<>("nebula_edge_domain_access", TypeInformation.of(Row.class));

        /** URL访问边 */
        public static final OutputTag<Row> URL_ACCESS = new OutputTag<>("nebula_edge_url_access", TypeInformation.of(Row.class));

        /** 证书使用边 */
        public static final OutputTag<Row> CERTIFICATE_USE = new OutputTag<>("nebula_edge_certificate_use", TypeInformation.of(Row.class));

        /** 证书颁发边 */
        public static final OutputTag<Row> CERTIFICATE_ISSUE = new OutputTag<>("nebula_edge_certificate_issue", TypeInformation.of(Row.class));

        /** 证书主体边 */
        public static final OutputTag<Row> CERTIFICATE_SUBJECT = new OutputTag<>("nebula_edge_certificate_subject", TypeInformation.of(Row.class));

        /** 域名组织关联边 */
        public static final OutputTag<Row> DOMAIN_ORGANIZATION = new OutputTag<>("nebula_edge_domain_organization", TypeInformation.of(Row.class));

        /** 证书组织关联边 */
        public static final OutputTag<Row> CERTIFICATE_ORGANIZATION = new OutputTag<>("nebula_edge_certificate_organization", TypeInformation.of(Row.class));
    }
}
