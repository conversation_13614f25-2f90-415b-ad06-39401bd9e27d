package com.geeksec.nta.graphbuilder.model.edge;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户端接收证书边
 * 表示客户端在TLS会话中接收/验证服务端的证书
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ClientReceivesCertEdge extends BaseEdge {

    /**
     * SNI信息
     */
    private String sni;

    /**
     * 获取边类型
     *
     * @return 边类型常量
     */
    @Override
    public EdgeType getEdgeType() {
        return EdgeType.CLIENT_RECEIVES_CERT;
    }
}
