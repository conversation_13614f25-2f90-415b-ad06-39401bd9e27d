package com.geeksec.nta.graphbuilder.io.source;

import com.alibaba.fastjson2.JSONObject;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.SerializationException;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import lombok.extern.slf4j.Slf4j;

/**
 * Kafka JSON消息反序列化器
 * 将Kafka消息反序列化为Map<String, Object>对象
 *
 * 主要用于从data-warehouse-processor接收JSON格式的会话和协议元数据
 * 与data-warehouse-processor中的JSON序列化方式对应，使用fastjson2进行反序列化
 *
 * <AUTHOR> Team
 */
@Slf4j
public class KafkaJsonDeserializer implements KafkaRecordDeserializationSchema<Map<String, Object>> {

    @Override
    public TypeInformation<Map<String, Object>> getProducedType() {
        return TypeInformation.of(new TypeHint<Map<String, Object>>(){});
    }

    @Override
    public void deserialize(ConsumerRecord<byte[], byte[]> consumerRecord, Collector<Map<String, Object>> collector) throws IOException {
        byte[] values = consumerRecord.value();

        if (values == null) {
            log.warn("Received null value from Kafka");
            return;
        }

        try {
            // 将JSON字符串转换为Map对象
            String jsonString = new String(values, StandardCharsets.UTF_8);
            Map<String, Object> resultMap = new HashMap<>();
            
            // 使用fastjson2解析JSON
            JSONObject jsonObject = JSONObject.parseObject(jsonString);
            if (jsonObject != null) {
                // 将JSONObject转换为Map
                for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                    resultMap.put(entry.getKey(), entry.getValue());
                }
                
                // 记录消息类型（如果存在）
                if (resultMap.containsKey("type")) {
                    log.debug("Deserialized message of type: {}", resultMap.get("type"));
                }
                
                collector.collect(resultMap);
            } else {
                log.warn("Failed to parse JSON: {}", jsonString);
            }
        } catch (Exception e) {
            log.error("Error deserializing JSON message: {}", e.getMessage(), e);
            throw new SerializationException("Error deserializing JSON message: " + e.getMessage(), e);
        }
    }
}
