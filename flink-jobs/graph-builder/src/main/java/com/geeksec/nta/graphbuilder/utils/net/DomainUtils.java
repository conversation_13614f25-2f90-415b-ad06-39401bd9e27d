package com.geeksec.nta.graphbuilder.utils.net;

import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.routines.DomainValidator;
import org.apache.commons.validator.routines.UrlValidator;

import com.google.common.net.InternetDomainName;

import lombok.extern.slf4j.Slf4j;

/**
 * 域名工具类
 * 提供域名处理的通用方法
 * 使用Guava的InternetDomainName类和Apache Commons Validator实现
 *
 * <AUTHOR> Team
 */
@Slf4j
public class DomainUtils { 

    /**
     * 用于验证域名的DomainValidator实例，允许本地域名
     */
    private static final DomainValidator DOMAIN_VALIDATOR = DomainValidator.getInstance(true);

    /**
     * 用于验证URL的UrlValidator实例，支持http、https和ftp协议
     */
    private static final UrlValidator URL_VALIDATOR = new UrlValidator(
            new String[] { "http", "https", "ftp" },
            UrlValidator.ALLOW_LOCAL_URLS);

    /**
     * 顶级域名正则表达式
     */
    private static final Pattern TLD_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\\.[a-zA-Z]{2,})+$");

    /**
     * 反向查询域名后缀
     */
    private static final String PTR_SUFFIX = "arpa";

    /**
     * 域名标签最小长度
     */
    private static final int MIN_DOMAIN_PARTS = 2;

    /**
     * 通配符前缀
     */
    private static final String WILDCARD_PREFIX = "*.";

    /**
     * 检测分布式服务部署证书的最小域名数量
     */
    private static final int MIN_COMMON_SUBDOMAIN_COUNT = 4;

    /**
     * 检测分布式服务部署证书的最小重复次数
     */
    private static final int MIN_SUBDOMAIN_REPEAT_COUNT = 3;

    /**
     * 域名部分索引：第一部分
     */
    private static final int DOMAIN_PART_FIRST = 1;

    /**
     * 域名部分索引：第二部分
     */
    private static final int DOMAIN_PART_SECOND = 2;

    /**
     * 私有构造函数，防止实例化
     */
    private DomainUtils() {
        throw new AssertionError("工具类不需要实例化");
    }

    /**
     * 判断字符串是否为有效的URL
     * 使用Apache Commons Validator的UrlValidator实现
     *
     * @param url 要验证的URL字符串
     * @return 如果是有效的URL则返回true，否则返回false
     */
    public static boolean isUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return false;
        }

        // 使用UrlValidator验证URL
        return URL_VALIDATOR.isValid(url);
    }

    /**
     * 从URL中提取域名
     *
     * @param url URL字符串
     * @return 提取的域名，如果URL无效则返回null
     */
    public static String extractDomainFromUrl(String url) {
        try {
            URL urlObj = new URL(url);
            return urlObj.getHost();
        } catch (Exception e) {
            log.debug("从URL提取域名失败: {}", url, e);
            return null;
        }
    }

    /**
     * 判断是否为PTR请求域名
     * 内部方法，用于检查域名是否以"arpa"结尾
     *
     * @param domain 要检查的域名（已预处理，不含通配符）
     * @return 如果是PTR请求域名则返回true，否则返回false
     */
    private static boolean isPtrDomain(String domain) {
        String[] domainItems = domain.split("\\.");
        if (domainItems.length > 0) {
            String suffix = domainItems[domainItems.length - 1];
            return PTR_SUFFIX.equalsIgnoreCase(suffix);
        }
        return false;
    }

    /**
     * 判断当前域名是否有效
     * 该方法提供完整的域名验证，包括：
     * 1. 基本的域名格式验证
     * 2. 检查是否为IP地址
     * 3. 检查是否为PTR请求
     *
     * @param domain 域名字符串
     * @return 如果是有效的域名则返回true，否则返回false
     */
    public static boolean isValidDomain(String domain) {
        // 判断是否为空
        if (StringUtils.isEmpty(domain)) {
            return false;
        }

        // 判断是否为IP类型
        if (IpUtils.isIpAddress(domain)) {
            return false;
        }

        // 处理通配符域名
        String processedDomain = domain;
        if (domain.startsWith(WILDCARD_PREFIX)) {
            processedDomain = domain.substring(WILDCARD_PREFIX.length());
        }

        // 使用DomainValidator验证域名
        if (!DOMAIN_VALIDATOR.isValid(processedDomain)) {
            // 如果DomainValidator验证失败，尝试使用正则表达式验证
            try {
                new URI("http://" + processedDomain);
                if (!TLD_PATTERN.matcher(processedDomain).matches()) {
                    return false;
                }
            } catch (URISyntaxException e) {
                return false;
            }
        }

        // 判断是否为PTR请求
        if (isPtrDomain(processedDomain)) {
            return false;
        }

        return true;
    }

    /**
     * 判断SNI域名是否有效
     * 该方法除了进行域名验证外，还会检查域名是否与服务器IP相同
     *
     * @param domain   域名字符串
     * @param serverIp 服务器IP
     * @return 如果是有效的SNI域名则返回true，否则返回false
     */
    public static boolean isValidSniDomain(String domain, String serverIp) {
        // 判断是否为IP类型
        if (IpUtils.isIpAddress(domain)) {
            // 如果是IP类型且与服务器IP相同，返回false
            return !domain.equals(serverIp);
        }

        // 进行完整的域名验证
        if (!isValidDomain(domain)) {
            return false;
        }

        // 处理通配符域名
        String processedDomain = domain;
        if (domain.startsWith(WILDCARD_PREFIX)) {
            processedDomain = domain.substring(WILDCARD_PREFIX.length());
        }

        // 判断当前域名长度是否正确
        String[] domainParts = processedDomain.split("\\.");
        return domainParts.length >= MIN_DOMAIN_PARTS;
    }

    /**
     * 获取域名的主机名部分
     *
     * @param domain 域名
     * @return 主机名部分
     */
    public static String getHostname(String domain) {
        if (StringUtils.isEmpty(domain)) {
            return null;
        }
        try {
            URI uri = new URI("http://" + domain);
            return uri.getHost();
        } catch (URISyntaxException e) {
            log.debug("解析域名失败: {}", domain, e);
            return null;
        }
    }

    /**
     * 获取域名的顶级域名
     *
     * @param domain 域名
     * @return 顶级域名
     */
    public static String getTopLevelDomain(String domain) {
        if (StringUtils.isEmpty(domain)) {
            return null;
        }
        try {
            URI uri = new URI("http://" + domain);
            String host = uri.getHost();
            if (host == null) {
                return null;
            }
            int lastDot = host.lastIndexOf('.');
            if (lastDot == -1) {
                return host;
            }
            return host.substring(lastDot + 1);
        } catch (URISyntaxException e) {
            log.debug("解析域名失败: {}", domain, e);
            return null;
        }
    }

    /**
     * 获取域名的子域名部分
     *
     * @param domain 域名
     * @return 子域名部分
     */
    public static String getSubdomain(String domain) {
        if (StringUtils.isEmpty(domain)) {
            return null;
        }
        try {
            URI uri = new URI("http://" + domain);
            String host = uri.getHost();
            if (host == null) {
                return null;
            }
            int lastDot = host.lastIndexOf('.');
            if (lastDot == -1) {
                return null;
            }
            return host.substring(0, lastDot);
        } catch (URISyntaxException e) {
            log.debug("解析域名失败: {}", domain, e);
            return null;
        }
    }

    /**
     * 判断是否为子域名
     *
     * @param domain 域名
     * @param parentDomain 父域名
     * @return 如果是子域名则返回true
     */
    public static boolean isSubdomain(String domain, String parentDomain) {
        if (StringUtils.isEmpty(domain) || StringUtils.isEmpty(parentDomain)) {
            return false;
        }
        try {
            URI domainUri = new URI("http://" + domain);
            URI parentUri = new URI("http://" + parentDomain);
            String domainHost = domainUri.getHost();
            String parentHost = parentUri.getHost();
            if (domainHost == null || parentHost == null) {
                return false;
            }
            return domainHost.endsWith("." + parentHost);
        } catch (URISyntaxException e) {
            log.debug("解析域名失败: {} 或 {}", domain, parentDomain, e);
            return false;
        }
    }

    /**
     * 提取根域名（二级域名）
     *
     * @param domain 域名字符串
     * @return 提取的根域名，如果域名无效则返回原始域名
     */
    public static String extractRootDomain(String domain) {
        // 空值检查
        if (StringUtils.isEmpty(domain)) {
            return domain;
        }

        // 移除可能的通配符前缀
        String processedDomain = domain;
        if (domain.startsWith(WILDCARD_PREFIX)) {
            processedDomain = domain.substring(WILDCARD_PREFIX.length());
        }

        try {
            InternetDomainName internetDomain = InternetDomainName.from(processedDomain);

            // 尝试获取公共后缀的上一级域名（通常是二级域名）
            if (internetDomain.hasPublicSuffix()) {
                InternetDomainName topPrivateDomain = internetDomain.topPrivateDomain();
                return topPrivateDomain.toString();
            }

            // 如果无法确定公共后缀，则返回原始域名
            return domain;
        } catch (Exception e) {
            // 如果方法失败，返回原始域名
            log.debug("提取根域名失败: {}", domain, e);
            return domain;
        }
    }

    /**
     * 检查域名列表中是否有共同的子域名模式
     * 用于检测分布式服务部署证书
     *
     * @param domains 域名列表
     * @return 如果有共同的子域名模式则返回true
     */
    public static boolean hasCommonSubdomainPattern(java.util.List<String> domains) {
        if (domains == null || domains.size() < MIN_COMMON_SUBDOMAIN_COUNT) {
            return false;
        }

        java.util.Map<String, Integer> subdomainCounts = new java.util.HashMap<>();

        for (String domain : domains) {
            String[] parts = domain.split("\\.");
            int length = parts.length;

            // 构建子域名部分，排除第一部分和最后一部分
            StringBuilder subdomain = new StringBuilder();
            for (int i = DOMAIN_PART_FIRST; i < length - DOMAIN_PART_FIRST; i++) {
                subdomain.append(parts[i]);
                if (i < length - DOMAIN_PART_SECOND) {
                    subdomain.append(".");
                }
            }

            // 统计每个子域名的出现次数
            String subdomainStr = subdomain.toString();
            if (StringUtils.isNotEmpty(subdomainStr)) {
                subdomainCounts.put(subdomainStr, subdomainCounts.getOrDefault(subdomainStr, 0) + 1);
            }
        }

        // 检查是否有任何子域名的出现次数超过最小重复次数
        for (Integer count : subdomainCounts.values()) {
            if (count > MIN_SUBDOMAIN_REPEAT_COUNT) {
                return true;
            }
        }
        return false;
    }
}
