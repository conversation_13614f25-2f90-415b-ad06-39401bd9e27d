package com.geeksec.nta.graphbuilder.model.vertex;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 证书主体顶点
 * 表示证书主体信息
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SubjectVertex extends BaseVertex {

    /**
     * 国家C
     */
    private String country;

    /**
     * 通用名称CN
     */
    private String commonName;

    /**
     * 组织名称O
     */
    private String organization;

    /**
     * 组织单位OU
     */
    private String orgUnit;

    /**
     * 州/省ST
     */
    private String state;

    /**
     * 地区L
     */
    private String locality;

    /**
     * 邮箱地址E
     */
    private String email;

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 街道地址
     */
    private String streetAddress;

    /**
     * 邮政编码
     */
    private String postalCode;

    /**
     * 业务类别
     */
    private String businessCategory;

    /**
     * 描述
     */
    private String description;

    /**
     * 威胁分数
     */
    private Integer threatScore;

    /**
     * 信任分数
     */
    private Integer trustScore;

    /**
     * 获取顶点ID
     *
     * @return 顶点ID
     */
    @Override
    public String getVertexId() {
        return commonName;
    }

    /**
     * 获取顶点标签
     *
     * @return 顶点标签常量
     */
    @Override
    public VertexTag getVertexTag() {
        return VertexTag.SUBJECT;
    }
}
