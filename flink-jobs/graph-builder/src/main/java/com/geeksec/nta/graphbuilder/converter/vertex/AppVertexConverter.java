package com.geeksec.nta.graphbuilder.converter.vertex;

import java.util.List;
import java.util.Map;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import com.geeksec.nta.graphbuilder.model.vertex.AppServiceVertex;
import com.geeksec.nta.graphbuilder.model.vertex.AppVertex;

import lombok.extern.slf4j.Slf4j;

/**
 * 应用顶点转换器
 * 处理应用相关的顶点数据，将其转换为Flink Row格式
 *
 * <AUTHOR>
 */
@Slf4j
public class AppVertexConverter extends ProcessFunction<Map<String, Object>, Row> {

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void processElement(Map<String, Object> vertexMap, Context ctx, Collector<Row> collector) throws Exception {
        // 处理应用服务顶点
        processAppServiceVertex(vertexMap, collector);

        // 处理应用顶点
        processAppVertex(vertexMap, collector);
    }

    /**
     * 处理应用服务顶点
     *
     * @param vertexMap 顶点数据映射
     * @param collector 收集器
     */
    private void processAppServiceVertex(Map<String, Object> vertexMap, Collector<Row> collector) {
        // 处理应用服务顶点
        AppServiceVertex appServiceVertex = (AppServiceVertex) vertexMap.get("appTag");
        if (appServiceVertex != null) {
            Row appServiceVertexRow = new Row(6);
            appServiceVertexRow.setField(0, appServiceVertex.getServiceId());
            appServiceVertexRow.setField(1, appServiceVertex.getAppName());
            appServiceVertexRow.setField(2, appServiceVertex.getIpAddress());
            appServiceVertexRow.setField(3, appServiceVertex.getPort());
            appServiceVertexRow.setField(4, appServiceVertex.getIpProtocol());
            appServiceVertexRow.setField(5, appServiceVertex.getRemark());
            collector.collect(appServiceVertexRow);
        }
    }

    /**
     * 处理应用顶点
     *
     * @param vertexMap 顶点数据映射
     * @param collector 收集器
     */
    private void processAppVertex(Map<String, Object> vertexMap, Collector<Row> collector) {
        // 处理应用顶点
        List<AppVertex> appVertexList = (List<AppVertex>) vertexMap.get("appVertexList");
        if (appVertexList != null && !appVertexList.isEmpty()) {
            for (AppVertex appVertex : appVertexList) {
                Row appVertexRow = new Row(4);
                appVertexRow.setField(0, appVertex.getAppId());
                appVertexRow.setField(1, appVertex.getAppName());
                appVertexRow.setField(2, appVertex.getAppVersion());
                appVertexRow.setField(3, appVertex.getThreatScore());
                collector.collect(appVertexRow);
            }
        }
    }
}
