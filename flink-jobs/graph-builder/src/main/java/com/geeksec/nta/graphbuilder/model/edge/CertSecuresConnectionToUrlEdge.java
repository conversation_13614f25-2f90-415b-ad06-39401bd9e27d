package com.geeksec.nta.graphbuilder.model.edge;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 证书为URL连接提供安全保障边
 * 表示证书与URL之间的安全保障关系
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CertSecuresConnectionToUrlEdge extends BaseEdge {
    
    /**
     * 获取边类型
     *
     * @return 边类型常量
     */
    @Override
    public EdgeType getEdgeType() {
        return EdgeType.CERT_SECURES_CONNECTION_TO_URL;
    }
}
