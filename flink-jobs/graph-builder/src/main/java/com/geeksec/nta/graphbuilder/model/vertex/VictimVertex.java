package com.geeksec.nta.graphbuilder.model.vertex;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 受害者标签
 * 表示受害者角色标记，应用于IP节点
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VictimVertex extends BaseVertex {

    /**
     * 受害者ID（通常是IP地址）
     */
    private String victimId;

    /**
     * 获取顶点ID
     *
     * @return 顶点ID
     */
    @Override
    public String getVertexId() {
        return victimId;
    }

    /**
     * 获取顶点标签
     * 注意：受害者是IP节点的一个标签，而不是独立的顶点类型
     *
     * @return 顶点标签常量
     */
    @Override
    public VertexTag getVertexTag() {
        return VertexTag.IP;
    }

    /**
     * 获取角色标签
     *
     * @return 角色标签常量
     */
    public VertexTag getRoleTag() {
        return VertexTag.VICTIM;
    }
}
