<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>flink-jobs</artifactId>
        <groupId>com.geeksec</groupId>
        <version>3.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>graph-builder</artifactId>
    <packaging>jar</packaging>

    <properties>
        <main.class>com.geeksec.nta.graphbuilder.pipeline.NetworkGraphPipeline</main.class>
        <docker.image.prefix>nta</docker.image.prefix>
    </properties>

    <dependencies>
        <!-- shared-core module -->
        <dependency>
            <groupId>com.geeksec</groupId>
            <artifactId>shared-core</artifactId>
        </dependency>

        <!-- Nebula图数据库相关 -->
        <dependency>
            <groupId>com.vesoft</groupId>
            <artifactId>nebula-flink-connector</artifactId>
        </dependency>

        <!-- Public Suffix List -->
        <dependency>
            <groupId>de.malkusch.whois-server-list</groupId>
            <artifactId>public-suffix-list</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <configuration>
                    <images>
                        <image>
                            <name>
                                ${docker.registry}/${docker.image.prefix}/${project.artifactId}:${project.version}</name>
                            <build>
                                <contextDir>${project.basedir}</contextDir>
                                <dockerFile>${project.basedir}/Dockerfile</dockerFile>
                                <args>
                                    <JAR_FILE>target/${project.build.finalName}.jar</JAR_FILE>
                                    <FLINK_VERSION>${flink.version}</FLINK_VERSION>
                                </args>
                            </build>
                        </image>
                    </images>
                </configuration>
                <executions>
                    <execution>
                        <id>build-image</id>
                        <phase>package</phase>
                        <goals>
                            <goal>build</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>push-image</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>push</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


</project>