package com.geeksec.common.utils.json;

import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import lombok.extern.slf4j.Slf4j;

/**
 * JSON工具类
 * 提供JSON处理的通用方法，增加了错误处理和日志记录
 *
 * <AUTHOR> Team
 */
@Slf4j
public final class JsonUtils {

    /**
     * 私有构造函数，防止实例化
     */
    private JsonUtils() {
        throw new AssertionError("工具类不需要实例化");
    }

    /**
     * 将对象转换为JSON字符串
     *
     * @param object 要转换的对象
     * @return JSON字符串，如果转换失败则返回null
     */
    public static String toJsonString(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return JSON.toJSONString(object, SerializerFeature.WriteMapNullValue);
        } catch (Exception e) {
            log.error("对象转JSON字符串失败", e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为对象
     *
     * @param jsonString JSON字符串
     * @param clazz      目标类型
     * @param <T>        目标类型
     * @return 转换后的对象，如果转换失败则返回null
     */
    public static <T> T parseObject(String jsonString, Class<T> clazz) {
        if (jsonString == null || jsonString.isEmpty()) {
            return null;
        }
        try {
            return JSON.parseObject(jsonString, clazz);
        } catch (Exception e) {
            log.error("JSON字符串转对象失败: {}", jsonString, e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为List
     *
     * @param jsonString JSON字符串
     * @param clazz      目标元素类型
     * @param <T>        目标元素类型
     * @return 转换后的List，如果转换失败则返回null
     */
    public static <T> List<T> parseArray(String jsonString, Class<T> clazz) {
        if (jsonString == null || jsonString.isEmpty()) {
            return null;
        }
        try {
            return JSON.parseArray(jsonString, clazz);
        } catch (Exception e) {
            log.error("JSON字符串转List失败: {}", jsonString, e);
            return null;
        }
    }

    /**
     * 将对象转换为Map
     *
     * @param object 要转换的对象
     * @return 转换后的Map，如果转换失败则返回null
     */
    public static Map<String, Object> objectToMap(Object object) {
        if (object == null) {
            return null;
        }
        try {
            // 先将对象转为JSON字符串，再解析为JSONObject
            String jsonString = toJsonString(object);
            return parseObject(jsonString, JSONObject.class);
        } catch (Exception e) {
            log.error("对象转Map失败", e);
            return null;
        }
    }
}
