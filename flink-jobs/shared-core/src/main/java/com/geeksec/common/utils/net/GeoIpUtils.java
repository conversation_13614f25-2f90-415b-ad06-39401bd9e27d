package com.geeksec.common.utils.net;

import java.io.IOException;
import java.io.InputStream;
import java.net.InetAddress;
import java.net.UnknownHostException;

import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.exception.GeoIp2Exception;
import com.maxmind.geoip2.model.AsnResponse;
import com.maxmind.geoip2.model.CityResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * IP地址地理位置查询工具类
 * 使用MaxMind GeoIP2数据库进行IP地址地理位置查询
 *
 * <AUTHOR> Team
 */
@Slf4j
public class GeoIpUtils {

    /**
     * 单例实例
     */
    private static GeoIpUtils instance = null;

    /**
     * 城市数据库读取器
     */
    private static DatabaseReader cityReader = null;

    /**
     * ASN数据库读取器
     */
    private static DatabaseReader asnReader = null;

    /**
     * 城市数据库文件名
     */
    private static final String CITY_DB_FILENAME = "GeoLite2-City.mmdb";

    /**
     * ASN数据库文件名
     */
    private static final String ASN_DB_FILENAME = "GeoLite2-ASN.mmdb";

    /**
     * 中文语言代码
     */
    private static final String CHINESE_LANG_CODE = "zh-CN";

    /**
     * 私有构造函数，初始化数据库读取器
     */
    private GeoIpUtils() {
        try {
            InputStream cityIn = this.getClass().getClassLoader().getResourceAsStream(CITY_DB_FILENAME);
            InputStream asnIn = this.getClass().getClassLoader().getResourceAsStream(ASN_DB_FILENAME);

            if (cityIn != null) {
                cityReader = new DatabaseReader.Builder(cityIn).build();
            } else {
                log.warn("城市数据库文件未找到: {}", CITY_DB_FILENAME);
            }

            if (asnIn != null) {
                asnReader = new DatabaseReader.Builder(asnIn).build();
            } else {
                log.warn("ASN数据库文件未找到: {}", ASN_DB_FILENAME);
            }
        } catch (IOException e) {
            log.error("初始化GeoIP2数据库失败", e);
        }
    }

    /**
     * 获取单例实例
     *
     * @return GeoIpUtils单例实例
     */
    public static GeoIpUtils getInstance() {
        if (instance == null) {
            synchronized (GeoIpUtils.class) {
                if (instance == null) {
                    instance = new GeoIpUtils();
                }
            }
        }
        return instance;
    }

    /**
     * 从CityResponse中获取国家名称
     *
     * @param response CityResponse对象
     * @return 国家名称，如果未找到则返回空字符串
     */
    public static String getCountry(CityResponse response) {
        if (response == null) {
            return "";
        }

        String country = response.getCountry().getNames().get(CHINESE_LANG_CODE);
        return country != null ? country : "";
    }

    /**
     * 从CityResponse中获取省份名称
     *
     * @param response CityResponse对象
     * @return 省份名称，如果未找到则返回空字符串
     */
    public static String getProvince(CityResponse response) {
        if (response == null) {
            return "";
        }

        String province = response.getMostSpecificSubdivision().getNames().get(CHINESE_LANG_CODE);
        return province != null ? province : "";
    }

    /**
     * 从CityResponse中获取城市名称
     *
     * @param response CityResponse对象
     * @return 城市名称，如果未找到则返回空字符串
     */
    public static String getCity(CityResponse response) {
        if (response == null) {
            return "";
        }

        String city = response.getCity().getNames().get(CHINESE_LANG_CODE);
        return city != null ? city : "";
    }

    /**
     * 从CityResponse中获取经度
     *
     * @param response CityResponse对象
     * @return 经度，如果未找到则返回0.0
     */
    public static Double getLongitude(CityResponse response) {
        if (response == null) {
            return 0.0;
        }

        Double longitude = response.getLocation().getLongitude();
        return longitude != null ? longitude : 0.0;
    }

    /**
     * 从CityResponse中获取纬度
     *
     * @param response CityResponse对象
     * @return 纬度，如果未找到则返回0.0
     */
    public static Double getLatitude(CityResponse response) {
        if (response == null) {
            return 0.0;
        }

        Double latitude = response.getLocation().getLatitude();
        return latitude != null ? latitude : 0.0;
    }

    /**
     * 从AsnResponse中获取AS编号
     *
     * @param response AsnResponse对象
     * @return AS编号，如果未找到则返回空字符串
     */
    public static String getAsn(AsnResponse response) {
        if (response == null) {
            return "";
        }

        Long asn = response.getAutonomousSystemNumber();
        return asn != null ? asn.toString() : "";
    }

    /**
     * 根据IP地址获取ASN详细信息
     *
     * @param ipAddress IP地址字符串
     * @return AsnResponse 对象，如果无法获取则返回 null
     */
    public static AsnResponse getAsnDetails(String ipAddress) {
        // 确保 asnReader 已通过 getInstance() 初始化
        // getInstance() 会负责初始化 asnReader
        getInstance(); 
        if (ipAddress == null || ipAddress.isEmpty() || asnReader == null) {
            if (asnReader == null && (ipAddress != null && !ipAddress.isEmpty())) {
                 log.warn("ASN数据库读取器未初始化，无法获取 {} 的ASN详细信息", ipAddress);
            }
            return null;
        }
        try {
            InetAddress ip = InetAddress.getByName(ipAddress);
            return asnReader.asn(ip);
        } catch (UnknownHostException e) {
            // 内网IP或无效IP地址解析时会抛出此异常，属于正常情况，记录trace级别日志
            log.trace("ASN数据库：无法识别的主机IP: {}", ipAddress);
        } catch (GeoIp2Exception | IOException e) {
            log.warn("ASN数据库：查询IP {} 的ASN信息时出错", ipAddress, e);
        }
        return null;
    }

    /**
     * 获取IP地址的国家代码
     * 简化方法，适用于不需要完整GeoIP功能的场景
     *
     * @param ipAddress IP地址
     * @return 国家代码，如果无法获取则返回"--"
     */
    public static String getCountryCode(String ipAddress) {
        if (ipAddress == null || ipAddress.isEmpty()) {
            return "--";
        }

        try {
            CityResponse response = getAddrInfo(ipAddress);
            if (response != null) {
                String countryCode = response.getCountry().getIsoCode();
                return countryCode != null ? countryCode : "--";
            }
            return "--";
        } catch (Exception e) {
            log.error("获取IP地址国家代码失败: {}", ipAddress, e);
            return "--";
        }
    }

    /**
     * 获取IP地址的ASN信息字符串
     * 简化方法，适用于不需要完整GeoIP功能的场景
     *
     * @param ipAddress IP地址
     * @return ASN信息，如果无法获取则返回"--"
     */
    public static String getAsnInfoString(String ipAddress) {
        if (ipAddress == null || ipAddress.isEmpty()) {
            return "--";
        }

        try {
            AsnResponse response = getAsnInfo(ipAddress);
            if (response != null) {
                String asn = getAsn(response);
                return asn.isEmpty() ? "--" : "AS" + asn;
            }
            return "--";
        } catch (Exception e) {
            log.error("获取IP地址ASN信息失败: {}", ipAddress, e);
            return "--";
        }
    }

    /**
     * 获取IP地址的地理位置信息
     *
     * @param ip IP地址
     * @return CityResponse对象，如果查询失败则返回null
     */
    public static CityResponse getAddrInfo(String ip) {
        if (ip == null || cityReader == null) {
            return null;
        }

        try {
            InetAddress addr = InetAddress.getByName(ip);
            return cityReader.city(addr);
        } catch (UnknownHostException e) {
            log.error("无效的IP地址: {}", ip, e);
        } catch (IOException e) {
            log.error("读取城市数据库失败", e);
        } catch (GeoIp2Exception e) {
            // 若是内网IP，自动返回为空，不打印日志
            log.debug("IP地址未在数据库中找到: {}", ip);
        }

        return null;
    }

    /**
     * 获取IP地址的ASN信息
     *
     * @param ip IP地址
     * @return AsnResponse对象，如果查询失败则返回null
     */
    public static AsnResponse getAsnInfo(String ip) {
        if (ip == null || asnReader == null) {
            return null;
        }

        try {
            InetAddress addr = InetAddress.getByName(ip);
            return asnReader.asn(addr);
        } catch (UnknownHostException e) {
            log.error("无效的IP地址: {}", ip, e);
        } catch (IOException e) {
            log.error("读取ASN数据库失败", e);
        } catch (GeoIp2Exception e) {
            // 若是内网IP，自动返回为空，不打印日志
            log.debug("IP地址未在数据库中找到: {}", ip);
        }

        return null;
    }

}
