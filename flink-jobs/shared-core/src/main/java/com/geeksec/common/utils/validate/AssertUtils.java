package com.geeksec.common.utils.validate;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;

/**
 * 断言工具类
 * 提供断言相关的工具方法，用于参数校验
 *
 * <AUTHOR> Team
 */
public class AssertUtils {

    /**
     * 私有构造函数，防止实例化
     */
    private AssertUtils() {
        // 私有构造函数，防止实例化
    }

    /**
     * 断言对象不为null
     *
     * @param obj 要检查的对象
     * @param message 断言失败时的错误消息
     * @throws IllegalArgumentException 如果对象为null
     */
    public static void notNull(Object obj, String message) {
        if (obj == null) {
            throw new IllegalArgumentException(message);
        }
    }

    /**
     * 断言对象不为null
     *
     * @param obj 要检查的对象
     * @throws IllegalArgumentException 如果对象为null
     */
    public static void notNull(Object obj) {
        notNull(obj, "The validated object is null");
    }

    /**
     * 断言字符串不为空
     *
     * @param str 要检查的字符串
     * @param message 断言失败时的错误消息
     * @throws IllegalArgumentException 如果字符串为null或空
     */
    public static void notEmpty(String str, String message) {
        if (StringUtils.isEmpty(str)) {
            throw new IllegalArgumentException(message);
        }
    }

    /**
     * 断言字符串不为空
     *
     * @param str 要检查的字符串
     * @throws IllegalArgumentException 如果字符串为null或空
     */
    public static void notEmpty(String str) {
        notEmpty(str, "The validated string is empty");
    }

    /**
     * 断言字符串有文本内容
     *
     * @param str 要检查的字符串
     * @param message 断言失败时的错误消息
     * @throws IllegalArgumentException 如果字符串为null、空或只包含空白字符
     */
    public static void hasText(String str, String message) {
        if (!StringUtils.isNotBlank(str)) {
            throw new IllegalArgumentException(message);
        }
    }

    /**
     * 断言字符串有文本内容
     *
     * @param str 要检查的字符串
     * @throws IllegalArgumentException 如果字符串为null、空或只包含空白字符
     */
    public static void hasText(String str) {
        hasText(str, "The validated string has no text");
    }

    /**
     * 断言集合不为空
     *
     * @param collection 要检查的集合
     * @param message 断言失败时的错误消息
     * @throws IllegalArgumentException 如果集合为null或空
     */
    public static void notEmpty(Collection<?> collection, String message) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new IllegalArgumentException(message);
        }
    }

    /**
     * 断言集合不为空
     *
     * @param collection 要检查的集合
     * @throws IllegalArgumentException 如果集合为null或空
     */
    public static void notEmpty(Collection<?> collection) {
        notEmpty(collection, "The validated collection is empty");
    }

    /**
     * 断言Map不为空
     *
     * @param map 要检查的Map
     * @param message 断言失败时的错误消息
     * @throws IllegalArgumentException 如果Map为null或空
     */
    public static void notEmpty(Map<?, ?> map, String message) {
        if (map == null || map.isEmpty()) {
            throw new IllegalArgumentException(message);
        }
    }

    /**
     * 断言Map不为空
     *
     * @param map 要检查的Map
     * @throws IllegalArgumentException 如果Map为null或空
     */
    public static void notEmpty(Map<?, ?> map) {
        notEmpty(map, "The validated map is empty");
    }

    /**
     * 断言数组不为空
     *
     * @param array 要检查的数组
     * @param message 断言失败时的错误消息
     * @throws IllegalArgumentException 如果数组为null或空
     */
    public static void notEmpty(Object[] array, String message) {
        if (array == null || array.length == 0) {
            throw new IllegalArgumentException(message);
        }
    }

    /**
     * 断言数组不为空
     *
     * @param array 要检查的数组
     * @throws IllegalArgumentException 如果数组为null或空
     */
    public static void notEmpty(Object[] array) {
        notEmpty(array, "The validated array is empty");
    }

    /**
     * 断言表达式为true
     *
     * @param expression 要检查的表达式
     * @param message 断言失败时的错误消息
     * @throws IllegalArgumentException 如果表达式为false
     */
    public static void isTrue(boolean expression, String message) {
        if (!expression) {
            throw new IllegalArgumentException(message);
        }
    }

    /**
     * 断言表达式为true
     *
     * @param expression 要检查的表达式
     * @throws IllegalArgumentException 如果表达式为false
     */
    public static void isTrue(boolean expression) {
        isTrue(expression, "The validated expression is false");
    }

    /**
     * 断言对象相等
     *
     * @param obj1 第一个对象
     * @param obj2 第二个对象
     * @param message 断言失败时的错误消息
     * @throws IllegalArgumentException 如果对象不相等
     */
    public static void equals(Object obj1, Object obj2, String message) {
        if (!Objects.equals(obj1, obj2)) {
            throw new IllegalArgumentException(message);
        }
    }

    /**
     * 断言对象相等
     *
     * @param obj1 第一个对象
     * @param obj2 第二个对象
     * @throws IllegalArgumentException 如果对象不相等
     */
    public static void equals(Object obj1, Object obj2) {
        equals(obj1, obj2, "The validated objects are not equal");
    }

    /**
     * 断言对象是指定类型的实例
     *
     * @param obj 要检查的对象
     * @param type 期望的类型
     * @param message 断言失败时的错误消息
     * @throws IllegalArgumentException 如果对象不是指定类型的实例
     */
    public static void isInstanceOf(Object obj, Class<?> type, String message) {
        if (obj == null || !type.isInstance(obj)) {
            throw new IllegalArgumentException(message);
        }
    }

    /**
     * 断言对象是指定类型的实例
     *
     * @param obj 要检查的对象
     * @param type 期望的类型
     * @throws IllegalArgumentException 如果对象不是指定类型的实例
     */
    public static void isInstanceOf(Object obj, Class<?> type) {
        isInstanceOf(obj, type, "The validated object is not an instance of " + type.getName());
    }

    /**
     * 断言数值在指定范围内
     *
     * @param value 要检查的数值
     * @param min 最小值（包含）
     * @param max 最大值（包含）
     * @param message 断言失败时的错误消息
     * @throws IllegalArgumentException 如果数值不在指定范围内
     */
    public static void inRange(int value, int min, int max, String message) {
        if (value < min || value > max) {
            throw new IllegalArgumentException(message);
        }
    }

    /**
     * 断言数值在指定范围内
     *
     * @param value 要检查的数值
     * @param min 最小值（包含）
     * @param max 最大值（包含）
     * @throws IllegalArgumentException 如果数值不在指定范围内
     */
    public static void inRange(int value, int min, int max) {
        inRange(value, min, max, "The validated value is not in the range " + min + " to " + max);
    }

    /**
     * 断言数值在指定范围内
     *
     * @param value 要检查的数值
     * @param min 最小值（包含）
     * @param max 最大值（包含）
     * @param message 断言失败时的错误消息
     * @throws IllegalArgumentException 如果数值不在指定范围内
     */
    public static void inRange(long value, long min, long max, String message) {
        if (value < min || value > max) {
            throw new IllegalArgumentException(message);
        }
    }

    /**
     * 断言数值在指定范围内
     *
     * @param value 要检查的数值
     * @param min 最小值（包含）
     * @param max 最大值（包含）
     * @throws IllegalArgumentException 如果数值不在指定范围内
     */
    public static void inRange(long value, long min, long max) {
        inRange(value, min, max, "The validated value is not in the range " + min + " to " + max);
    }

    /**
     * 断言数值在指定范围内
     *
     * @param value 要检查的数值
     * @param min 最小值（包含）
     * @param max 最大值（包含）
     * @param message 断言失败时的错误消息
     * @throws IllegalArgumentException 如果数值不在指定范围内
     */
    public static void inRange(double value, double min, double max, String message) {
        if (value < min || value > max) {
            throw new IllegalArgumentException(message);
        }
    }

    /**
     * 断言数值在指定范围内
     *
     * @param value 要检查的数值
     * @param min 最小值（包含）
     * @param max 最大值（包含）
     * @throws IllegalArgumentException 如果数值不在指定范围内
     */
    public static void inRange(double value, double min, double max) {
        inRange(value, min, max, "The validated value is not in the range " + min + " to " + max);
    }
}
