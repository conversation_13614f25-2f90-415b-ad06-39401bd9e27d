package com.geeksec.common.utils.unit;

import java.text.DecimalFormat;
import java.util.concurrent.TimeUnit;

/**
 * 单位转换工具类
 * 提供各种单位转换的工具方法
 *
 * <AUTHOR> Team
 */
public class UnitUtils {   

    /**
     * 字节单位数组
     */
    private static final String[] BYTE_UNITS = {"字节", "KB", "MB", "GB", "TB", "PB", "EB"};

    /**
     * 时间单位数组
     */
    private static final String[] TIME_UNITS = {"秒", "分", "时", "天"};

    /**
     * 私有构造函数，防止实例化
     */
    private UnitUtils() {
        // 私有构造函数，防止实例化
    }

    /**
     * 格式化字节大小
     *
     * @param bytes 字节数
     * @param digits 小数位数
     * @return 格式化后的字符串
     */
    public static String formatBytes(long bytes, int digits) {
        if (bytes <= 0) {
            return "0 " + BYTE_UNITS[0];
        }
        
        int unitIndex = (int) (Math.log10(bytes) / Math.log10(1024));
        if (unitIndex >= BYTE_UNITS.length) {
            unitIndex = BYTE_UNITS.length - 1;
        }
        
        double value = bytes / Math.pow(1024, unitIndex);
        
        DecimalFormat df = new DecimalFormat("#." + "#".repeat(digits));
        return df.format(value) + " " + BYTE_UNITS[unitIndex];
    }

    /**
     * 格式化字节大小（默认2位小数）
     *
     * @param bytes 字节数
     * @return 格式化后的字符串
     */
    public static String formatBytes(long bytes) {
        return formatBytes(bytes, 2);
    }

    /**
     * 格式化秒数为时间字符串
     *
     * @param seconds 秒数
     * @return 格式化后的时间字符串
     */
    public static String formatSeconds(long seconds) {
        if (seconds < 0) {
            return "0秒";
        }
        
        long days = seconds / (24 * 3600);
        long hours = (seconds % (24 * 3600)) / 3600;
        long minutes = (seconds % 3600) / 60;
        long secs = seconds % 60;
        
        StringBuilder sb = new StringBuilder();
        if (days > 0) {
            sb.append(days).append("天");
        }
        if (hours > 0 || sb.length() > 0) {
            sb.append(hours).append("时");
        }
        if (minutes > 0 || sb.length() > 0) {
            sb.append(minutes).append("分");
        }
        sb.append(secs).append("秒");
        
        return sb.toString();
    }

    /**
     * 格式化毫秒数为时间字符串
     *
     * @param millis 毫秒数
     * @return 格式化后的时间字符串
     */
    public static String formatMillis(long millis) {
        return formatSeconds(millis / 1000);
    }

    /**
     * 将字节数转换为指定单位
     *
     * @param bytes 字节数
     * @param unit 目标单位（0:字节, 1:KB, 2:MB, 3:GB, 4:TB, 5:PB, 6:EB）
     * @return 转换后的值
     */
    public static double convertBytes(long bytes, int unit) {
        if (bytes <= 0 || unit < 0 || unit >= BYTE_UNITS.length) {
            return 0;
        }
        
        return bytes / Math.pow(1024, unit);
    }

    /**
     * 将字节数转换为KB
     *
     * @param bytes 字节数
     * @return KB值
     */
    public static double bytesToKB(long bytes) {
        return convertBytes(bytes, 1);
    }

    /**
     * 将字节数转换为MB
     *
     * @param bytes 字节数
     * @return MB值
     */
    public static double bytesToMB(long bytes) {
        return convertBytes(bytes, 2);
    }

    /**
     * 将字节数转换为GB
     *
     * @param bytes 字节数
     * @return GB值
     */
    public static double bytesToGB(long bytes) {
        return convertBytes(bytes, 3);
    }

    /**
     * 将KB转换为字节数
     *
     * @param kb KB值
     * @return 字节数
     */
    public static long kbToBytes(double kb) {
        return (long) (kb * 1024);
    }

    /**
     * 将MB转换为字节数
     *
     * @param mb MB值
     * @return 字节数
     */
    public static long mbToBytes(double mb) {
        return (long) (mb * 1024 * 1024);
    }

    /**
     * 将GB转换为字节数
     *
     * @param gb GB值
     * @return 字节数
     */
    public static long gbToBytes(double gb) {
        return (long) (gb * 1024 * 1024 * 1024);
    }

    /**
     * 将秒数转换为指定时间单位
     *
     * @param seconds 秒数
     * @param unit 目标时间单位
     * @return 转换后的值
     */
    public static long convertSeconds(long seconds, TimeUnit unit) {
        if (seconds <= 0) {
            return 0;
        }
        
        return unit.convert(seconds, TimeUnit.SECONDS);
    }

    /**
     * 将秒数转换为分钟
     *
     * @param seconds 秒数
     * @return 分钟数
     */
    public static long secondsToMinutes(long seconds) {
        return convertSeconds(seconds, TimeUnit.MINUTES);
    }

    /**
     * 将秒数转换为小时
     *
     * @param seconds 秒数
     * @return 小时数
     */
    public static long secondsToHours(long seconds) {
        return convertSeconds(seconds, TimeUnit.HOURS);
    }

    /**
     * 将秒数转换为天
     *
     * @param seconds 秒数
     * @return 天数
     */
    public static long secondsToDays(long seconds) {
        return convertSeconds(seconds, TimeUnit.DAYS);
    }

    /**
     * 将分钟转换为秒数
     *
     * @param minutes 分钟数
     * @return 秒数
     */
    public static long minutesToSeconds(long minutes) {
        return TimeUnit.MINUTES.toSeconds(minutes);
    }

    /**
     * 将小时转换为秒数
     *
     * @param hours 小时数
     * @return 秒数
     */
    public static long hoursToSeconds(long hours) {
        return TimeUnit.HOURS.toSeconds(hours);
    }

    /**
     * 将天转换为秒数
     *
     * @param days 天数
     * @return 秒数
     */
    public static long daysToSeconds(long days) {
        return TimeUnit.DAYS.toSeconds(days);
    }

    /**
     * 格式化比特率（bps）
     *
     * @param bps 比特率
     * @param digits 小数位数
     * @return 格式化后的字符串
     */
    public static String formatBitRate(long bps, int digits) {
        if (bps <= 0) {
            return "0 bps";
        }
        
        String[] units = {"bps", "Kbps", "Mbps", "Gbps", "Tbps"};
        int unitIndex = (int) (Math.log10(bps) / Math.log10(1000));
        if (unitIndex >= units.length) {
            unitIndex = units.length - 1;
        }
        
        double value = bps / Math.pow(1000, unitIndex);
        
        DecimalFormat df = new DecimalFormat("#." + "#".repeat(digits));
        return df.format(value) + " " + units[unitIndex];
    }

    /**
     * 格式化比特率（bps）（默认2位小数）
     *
     * @param bps 比特率
     * @return 格式化后的字符串
     */
    public static String formatBitRate(long bps) {
        return formatBitRate(bps, 2);
    }

    /**
     * 格式化百分比
     *
     * @param value 值
     * @param total 总值
     * @param digits 小数位数
     * @return 格式化后的百分比字符串
     */
    public static String formatPercentage(long value, long total, int digits) {
        if (total <= 0) {
            return "0%";
        }
        
        double percentage = (double) value / total * 100;
        DecimalFormat df = new DecimalFormat("#." + "#".repeat(digits) + "%");
        return df.format(percentage / 100);
    }

    /**
     * 格式化百分比（默认2位小数）
     *
     * @param value 值
     * @param total 总值
     * @return 格式化后的百分比字符串
     */
    public static String formatPercentage(long value, long total) {
        return formatPercentage(value, total, 2);
    }
}
