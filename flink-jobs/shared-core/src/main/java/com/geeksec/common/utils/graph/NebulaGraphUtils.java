package com.geeksec.common.utils.graph;

import static com.geeksec.common.config.ConfigConstants.NEBULA_GRAPH_ADDR;
import static com.geeksec.common.config.ConfigConstants.NEBULA_META_ADDR;
import static com.geeksec.common.config.ConfigConstants.NEBULA_PASSWORD;
import static com.geeksec.common.config.ConfigConstants.NEBULA_POOL_IDLE_TIME;
import static com.geeksec.common.config.ConfigConstants.NEBULA_POOL_MAX_CONN_SIZE;
import static com.geeksec.common.config.ConfigConstants.NEBULA_POOL_MIN_CONN_SIZE;
import static com.geeksec.common.config.ConfigConstants.NEBULA_POOL_TIMEOUT;
import static com.geeksec.common.config.ConfigConstants.NEBULA_SESSION_SIZE;
import static com.geeksec.common.config.ConfigConstants.NEBULA_SPACE_NAME;
import static com.geeksec.common.config.ConfigConstants.NEBULA_USERNAME;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.nebula.connection.NebulaClientOptions;
import org.apache.flink.connector.nebula.connection.NebulaGraphConnectionProvider;
import org.apache.flink.connector.nebula.connection.NebulaMetaConnectionProvider;

import com.geeksec.common.config.ConfigurationManager;
import com.vesoft.nebula.client.graph.NebulaPoolConfig;
import com.vesoft.nebula.client.graph.data.HostAddress;
import com.vesoft.nebula.client.graph.data.ResultSet;
import com.vesoft.nebula.client.graph.data.ValueWrapper;
import com.vesoft.nebula.client.graph.exception.IOErrorException;
import com.vesoft.nebula.client.graph.net.NebulaPool;
import com.vesoft.nebula.client.graph.net.Session;

import lombok.extern.slf4j.Slf4j;

/**
 * Nebula Graph工具类
 * 提供Nebula Graph连接池和会话管理功能
 *
 * <AUTHOR> Team
 */
@Slf4j
public class NebulaGraphUtils {

    /**
     * 配置工具实例
     */
    private static final ParameterTool CONFIG = ConfigurationManager.getConfig();

    /**
     * Nebula连接池
     */
    private static volatile NebulaPool NEBULA_POOL = null;

    /**
     * Nebula连接池最大连接数
     */
    private static final int NEBULA_POOL_MAX_CONN_SIZE_VALUE = CONFIG.getInt(NEBULA_POOL_MAX_CONN_SIZE, 1000);

    /**
     * Nebula连接池最小连接数
     */
    private static final int NEBULA_POOL_MIN_CONN_SIZE_VALUE = CONFIG.getInt(NEBULA_POOL_MIN_CONN_SIZE, 50);

    /**
     * Nebula连接池空闲时间（毫秒）
     */
    private static final int NEBULA_POOL_IDLE_TIME_MS = CONFIG.getInt(NEBULA_POOL_IDLE_TIME, 180000);

    /**
     * Nebula连接池超时时间（毫秒）
     */
    private static final int NEBULA_POOL_TIMEOUT_MS = CONFIG.getInt(NEBULA_POOL_TIMEOUT, 300000);

    /**
     * Nebula集群地址
     */
    private static final String NEBULA_CLUSTER = CONFIG.get(NEBULA_GRAPH_ADDR);

    /**
     * Nebula元数据地址
     */
    private static final String NEBULA_META_CLUSTER = CONFIG.get(NEBULA_META_ADDR);

    /**
     * Nebula用户名
     */
    private static final String USERNAME = CONFIG.get(NEBULA_USERNAME);

    /**
     * Nebula密码
     */
    private static final String PASSWORD = CONFIG.get(NEBULA_PASSWORD);

    /**
     * Nebula空间名称
     */
    private static final String SPACE = CONFIG.get(NEBULA_SPACE_NAME);

    /**
     * Nebula会话队列大小
     */
    private static final Integer SESSION_QUEUE_SIZE = CONFIG.getInt(NEBULA_SESSION_SIZE, 10);

    /**
     * 会话队列
     */
    private static final LinkedBlockingQueue<Session> SESSION_QUEUE = new LinkedBlockingQueue<>();

    /**
     * Flink Nebula连接客户端选项
     */
    private static volatile NebulaClientOptions NEBULA_CLIENT_OPTIONS = null;

    /**
     * Flink Nebula Graph连接提供者
     */
    private static volatile NebulaGraphConnectionProvider GRAPH_CONNECTION_PROVIDER = null;

    /**
     * Flink Nebula Meta连接提供者
     */
    private static volatile NebulaMetaConnectionProvider META_CONNECTION_PROVIDER = null;

    /**
     * 私有构造函数，防止实例化
     */
    private NebulaGraphUtils() {
        // 私有构造函数，防止实例化
    }

    /**
     * 创建Nebula连接池配置
     *
     * @return Nebula连接池配置
     */
    public static NebulaPoolConfig createNebulaPoolConfig() {
        NebulaPoolConfig nebulaPoolConfig = new NebulaPoolConfig();
        nebulaPoolConfig.setMaxConnSize(NEBULA_POOL_MAX_CONN_SIZE_VALUE);
        nebulaPoolConfig.setMinConnSize(NEBULA_POOL_MIN_CONN_SIZE_VALUE);
        nebulaPoolConfig.setIdleTime(NEBULA_POOL_IDLE_TIME_MS);
        nebulaPoolConfig.setTimeout(NEBULA_POOL_TIMEOUT_MS);
        return nebulaPoolConfig;
    }

    /**
     * 解析主机地址字符串
     *
     * @param hostAddressStr 主机地址字符串，格式：host1:port1,host2:port2,...
     * @return 主机地址列表
     */
    public static List<HostAddress> parseHostAddresses(String hostAddressStr) {
        if (StringUtils.isEmpty(hostAddressStr)) {
            return new ArrayList<>();
        }

        return Arrays.stream(hostAddressStr.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotEmpty)
                .map(addr -> {
                    String[] parts = addr.split(":");
                    if (parts.length != 2) {
                        log.error("无效的主机地址格式: {}", addr);
                        return null;
                    }
                    try {
                        return new HostAddress(parts[0], Integer.parseInt(parts[1]));
                    } catch (NumberFormatException e) {
                        log.error("无效的端口号: {}", parts[1], e);
                        return null;
                    }
                })
                .filter(addr -> addr != null)
                .collect(Collectors.toList());
    }

    /**
     * 初始化Nebula连接池
     *
     * @param nebulaPoolConfig Nebula连接池配置
     * @return Nebula连接池
     */
    public static synchronized NebulaPool initNebulaPool(NebulaPoolConfig nebulaPoolConfig) {
        if (NEBULA_POOL != null) {
            return NEBULA_POOL;
        }

        List<HostAddress> addresses = parseHostAddresses(NEBULA_CLUSTER);
        if (addresses.isEmpty()) {
            log.error("Nebula Graph地址解析失败");
            return null;
        }

        NEBULA_POOL = new NebulaPool();
        try {
            boolean initResult = NEBULA_POOL.init(addresses, nebulaPoolConfig);
            if (!initResult) {
                log.error("初始化Nebula连接池失败");
                NEBULA_POOL = null;
            } else {
                log.info("初始化Nebula连接池成功");
            }
        } catch (Exception e) {
            log.error("初始化Nebula连接池异常", e);
            NEBULA_POOL = null;
        }
        return NEBULA_POOL;
    }

    /**
     * 初始化Nebula会话队列
     */
    public static void initSessionQueue() {
        try {
            if (NEBULA_POOL == null) {
                synchronized (NebulaGraphUtils.class) {
                    if (NEBULA_POOL == null) {
                        initNebulaPool(createNebulaPoolConfig());
                    }
                }
            }

            log.info("开始初始化Nebula Session连接池，连接队列长度设置：{}", SESSION_QUEUE_SIZE);
            SESSION_QUEUE.clear();

            for (int i = 0; i < SESSION_QUEUE_SIZE; i++) {
                Session session = NEBULA_POOL.getSession(USERNAME, PASSWORD, false);
                // 使用空间
                if (StringUtils.isNotEmpty(SPACE)) {
                    session.execute("USE " + SPACE);
                }
                SESSION_QUEUE.put(session);
            }

            log.info("初始化Nebula Session队列成功! 队列长度: {}", SESSION_QUEUE.size());
        } catch (Exception e) {
            log.error("初始化Nebula Session队列失败", e);
        }
    }

    /**
     * 获取Nebula会话
     *
     * @return Nebula会话
     */
    public static Session getSession() {
        try {
            // 如果队列为空，初始化会话队列
            if (SESSION_QUEUE.isEmpty()) {
                initSessionQueue();
            }

            if (!SESSION_QUEUE.isEmpty()) {
                return SESSION_QUEUE.take();
            }

            // 如果队列仍然为空，创建新会话
            if (NEBULA_POOL == null) {
                initNebulaPool(createNebulaPoolConfig());
            }

            if (NEBULA_POOL != null) {
                Session session = NEBULA_POOL.getSession(USERNAME, PASSWORD, false);

                // 如果空间名称不为空，则使用该空间
                if (StringUtils.isNotEmpty(SPACE)) {
                    session.execute("USE " + SPACE);
                }
                return session;
            }
        } catch (Exception e) {
            log.error("获取Nebula Session失败", e);
        }
        return null;
    }

    /**
     * 释放Nebula会话
     *
     * @param session Nebula会话
     */
    public static void releaseSession(Session session) {
        if (session == null) {
            return;
        }

        try {
            SESSION_QUEUE.put(session);
            log.debug("Nebula Session已返回连接池，当前连接池大小: {}", SESSION_QUEUE.size());
        } catch (Exception e) {
            log.error("释放Nebula Session失败", e);
        }
    }

    /**
     * 执行Nebula查询
     *
     * @param query 查询语句
     * @return 查询结果
     */
    public static ResultSet executeNebulaQuery(String query) {
        Session session = getSession();
        if (session == null) {
            log.error("无法获取Nebula Session，执行查询失败");
            return null;
        }

        try {
            log.debug("获取Nebula Session成功，当前活动会话数: {}", SESSION_QUEUE.size());
            ResultSet resultSet = session.execute(query);

            if (!resultSet.isSucceeded()) {
                log.error("执行查询失败: `{}`, 错误信息: {}", query, resultSet.getErrorMessage());
            }

            return resultSet;
        } catch (Exception e) {
            log.error("执行Nebula查询失败", e);
            return null;
        } finally {
            releaseSession(session);
        }
    }

    /**
     * 关闭Nebula连接池
     */
    public static void closeNebulaPool() {
        if (NEBULA_POOL != null) {
            try {
                // 清空会话队列
                SESSION_QUEUE.clear();
                // 关闭连接池
                NEBULA_POOL.close();
                NEBULA_POOL = null;
                log.info("Nebula连接池已关闭");
            } catch (Exception e) {
                log.error("关闭Nebula连接池失败", e);
            }
        }
    }

    /**
     * 获取Nebula空间名称
     *
     * @return Nebula空间名称
     */
    public static String getSpaceName() {
        return SPACE;
    }

    /**
     * 创建Nebula客户端选项
     *
     * @return Nebula客户端选项
     */
    public static synchronized NebulaClientOptions createNebulaClientOptions() {
        if (NEBULA_CLIENT_OPTIONS != null) {
            return NEBULA_CLIENT_OPTIONS;
        }

        NEBULA_CLIENT_OPTIONS = new NebulaClientOptions.NebulaClientOptionsBuilder()
                .setGraphAddress(NEBULA_CLUSTER)
                .setMetaAddress(NEBULA_META_CLUSTER)
                .build();

        return NEBULA_CLIENT_OPTIONS;
    }

    /**
     * 获取Nebula Graph连接提供者
     *
     * @return Nebula Graph连接提供者
     */
    public static synchronized NebulaGraphConnectionProvider getGraphConnectionProvider() {
        if (GRAPH_CONNECTION_PROVIDER != null) {
            return GRAPH_CONNECTION_PROVIDER;
        }

        NebulaClientOptions clientOptions = createNebulaClientOptions();
        GRAPH_CONNECTION_PROVIDER = new NebulaGraphConnectionProvider(clientOptions);

        return GRAPH_CONNECTION_PROVIDER;
    }

    /**
     * 获取Nebula Meta连接提供者
     *
     * @return Nebula Meta连接提供者
     */
    public static synchronized NebulaMetaConnectionProvider getMetaConnectionProvider() {
        if (META_CONNECTION_PROVIDER != null) {
            return META_CONNECTION_PROVIDER;
        }

        NebulaClientOptions clientOptions = createNebulaClientOptions();
        META_CONNECTION_PROVIDER = new NebulaMetaConnectionProvider(clientOptions);

        return META_CONNECTION_PROVIDER;
    }

    /**
     * 查询顶点属性
     *
     * @param tagType 顶点类型
     * @param vid     顶点ID
     * @return 查询结果
     * @throws IOErrorException             如果查询失败
     * @throws UnsupportedEncodingException 如果编码不支持
     */
    public static HashMap<String, Object> queryVertexProperties(String tagType, String vid)
            throws IOErrorException, UnsupportedEncodingException {
        Session session = getSession();
        if (session == null) {
            log.error("无法获取Nebula Session，查询顶点数据失败");
            return null;
        }

        try {
            String query = String.format("use %s;match (v:%s) where id(v) == \"%s\" return properties(v) as tagProps",
                    SPACE, tagType, vid);
            ResultSet rs = session.execute(query);

            if (!rs.isSucceeded()) {
                log.error(String.format("执行查询失败: `%s', 错误信息: %s", query, rs.getErrorMessage()));
                return null;
            }

            if (rs.rowsSize() == 0) {
                // 数据库中不存在此vid的点
                return null;
            } else if (rs.rowsSize() == 1) {
                ResultSet.Record record = rs.rowValues(0);
                ValueWrapper value = record.get("tagProps");
                if (value.isEmpty() || value.isNull()) {
                    log.info(String.format(
                            "调试错误: 结果集行数为1但标签类型为空 ### 类型: %s, 顶点ID: %s", tagType, vid));
                    return null;
                } else {
                    // 点存在
                    HashMap<String, ValueWrapper> kvs = value.asMap();
                    HashMap<String, Object> result = new HashMap<>();
                    result.put("firstTime", kvs.get("first_time").asLong());
                    result.put("lastTime", kvs.get("last_time").asLong());

                    // 根据顶点类型添加特定字段
                    switch (tagType) {
                        case "IP":
                            result.put("ipKey", kvs.get("ip_key").asString());
                            result.put("ipAddr", kvs.get("ip_addr").asString());
                            result.put("version", kvs.get("version").asString());

                            ValueWrapper cityWrapper = kvs.get("city");
                            if (cityWrapper.isNull()) {
                                result.put("city", StringUtils.EMPTY);
                            } else {
                                result.put("city", cityWrapper.asString());
                            }

                            ValueWrapper countryWrapper = kvs.get("country");
                            if (countryWrapper.isNull()) {
                                result.put("country", StringUtils.EMPTY);
                            } else {
                                result.put("country", countryWrapper.asString());
                            }

                            result.put("bytes", kvs.get("bytes").asLong());
                            result.put("sendBytes", kvs.get("send_bytes").asLong());
                            result.put("recvBytes", kvs.get("recv_bytes").asLong());
                            result.put("packets", (int) kvs.get("packets").asLong());
                            result.put("averageBps", kvs.get("average_bps").asLong());
                            result.put("blackList", kvs.get("black_list").asLong());
                            result.put("whiteList", kvs.get("white_list").asLong());
                            result.put("remark", kvs.get("remark").asString());
                            result.put("times", kvs.get("times").asLong());
                            break;

                        case "DOMAIN":
                            result.put("domainAddr", kvs.get("domain_addr").asString());
                            result.put("blackList", kvs.get("black_list").asLong());
                            result.put("whiteList", kvs.get("white_list").asLong());
                            result.put("alexaRank", kvs.get("alexa_rank").asLong());
                            result.put("remark", kvs.get("remark").asString());
                            result.put("bytes", kvs.get("bytes").asLong());
                            result.put("average_bps", kvs.get("average_bps").asLong());
                            result.put("whois", kvs.get("whois").asString());
                            break;

                        case "APP":
                            result.put("appName", kvs.get("app_name").asString());
                            if (kvs.containsKey("app_version")) {
                                result.put("appVersion", kvs.get("app_version").asString());
                            }
                            break;

                        default:
                            // 其他顶点类型不需要特殊处理
                            break;
                    }

                    return result;
                }
            }
        } catch (Exception e) {
            log.error("查询Nebula顶点数据失败", e);
        } finally {
            releaseSession(session);
        }

        return null;
    }

    /**
     * 查询边属性
     *
     * @param edgeType 边类型
     * @param src      源顶点ID
     * @param dst      目标顶点ID
     * @return 查询结果
     * @throws IOErrorException             如果查询失败
     * @throws UnsupportedEncodingException 如果编码不支持
     */
    public static HashMap<String, Object> queryEdgeProperties(String edgeType, String src, String dst)
            throws IOErrorException, UnsupportedEncodingException {
        Session session = getSession();
        if (session == null) {
            log.error("无法获取Nebula Session，查询边数据失败");
            return null;
        }

        try {
            String query = String.format(
                    "use %s; match (v0)-[e:`%s`]-(v1) where id(v0) == \"%s\" and id(v1) == \"%s\" return properties(e) as edgeProps, rank(e) as edgeRank;",
                    SPACE, edgeType, src, dst);
            ResultSet rs = session.execute(query);

            if (!rs.isSucceeded()) {
                log.error(String.format("执行查询失败: `%s', 错误信息: %s", query, rs.getErrorMessage()));
                return null;
            }

            if (rs.rowsSize() == 0) {
                // 两点之间不存在边
                return null;
            } else if (rs.rowsSize() == 1) {
                // 两点之间存在一条边(大部分情况下)
                ResultSet.Record record = rs.rowValues(0);
                ValueWrapper value = record.get("edgeProps");
                if (value.isEmpty() || value.isNull()) {
                    log.info(String.format(
                            "调试错误: 结果集行数为1但边类型为空 ### 类型: %s, 源点: %s, 目标点: %s",
                            edgeType, src, dst));
                    return null;
                } else if (value.isMap()) {
                    HashMap<String, ValueWrapper> kvs = value.asMap();
                    HashMap<String, Object> result = new HashMap<>();
                    result.put("firstTime", kvs.get("first_time").asLong());
                    result.put("lastTime", kvs.get("last_time").asLong());
                    result.put("sessionCnt", kvs.get("session_cnt").asLong());

                    // 根据边类型添加特定字段
                    switch (edgeType) {
                        case "src_bind":
                        case "dst_bind":
                            result.put("ip", kvs.get("ip").asString());
                            result.put("mac", kvs.get("mac").asString());
                            result.put("bytes", kvs.get("bytes").asLong());
                            result.put("packets", (int) kvs.get("packets").asLong());
                            break;

                        case "connect":
                        case "connect_ip":
                        case "connect_mac":
                            result.put("appName", kvs.get("app_name").asString());
                            result.put("dPort", kvs.get("dport").asLong());
                            result.put("bytes", kvs.get("bytes").asLong());
                            result.put("packets", (int) kvs.get("packets").asLong());
                            result.put("sendBytes", kvs.get("send_bytes").asLong());
                            result.put("recvBytes", kvs.get("recv_bytes").asLong());
                            result.put("sendPackets", (int) kvs.get("send_packets").asLong());
                            result.put("recvPackets", (int) kvs.get("recv_packets").asLong());
                            break;

                        case "client_app":
                        case "app_server":
                            result.put("ip", kvs.get("ip").asString());
                            result.put("appName", kvs.get("app_name").asString());
                            break;

                        case "client_query_domain":
                        case "client_query_dns_server":
                            result.put("dnsType", kvs.get("dns_type").asLong());
                            result.put("answerType", kvs.get("answer_type").asLong());
                            break;

                        case "dns_server_domain":
                            // 特殊处理,此处从Nebula查出dns_type字段为String
                            result.put("dnsType", Long.valueOf(kvs.get("dns_type").asString()));
                            result.put("answerType", kvs.get("answer_type").asLong());
                            break;

                        case "parse_to":
                            result.put("dnsServer", kvs.get("dns_server").asString());
                            result.put("finalParse", kvs.get("final_parse").asBoolean());
                            result.put("maxTTL", (int) kvs.get("max_ttl").asLong());
                            result.put("minTTL", (int) kvs.get("min_ttl").asLong());
                            break;

                        case "client_use_cert":
                        case "client_connect_cert":
                        case "sslfinger_connect_cert":
                            result.put("sni", kvs.get("sni").asString());
                            break;

                        case "server_use_cert":
                            result.put("sni", kvs.get("sni").asString());
                            result.put("certId", kvs.get("cert_id").asString());
                            break;

                        default:
                            // 其他边类型不需要特殊处理
                            break;
                    }

                    return result;
                } else {
                    log.info(String.format("调试错误: 类型不正确 ### 类型: %s, 源点: %s, 目标点: %s",
                            edgeType, src, dst));
                    return null;
                }
            } else {
                // 两点之间存在多条边(少部分情况)
                // 聚合处理 - 暂未实现
                log.info(String.format("两点之间存在多条边，需要聚合处理 ### 类型: %s, 源点: %s, 目标点: %s",
                        edgeType, src, dst));
                return null;
            }
        } catch (Exception e) {
            log.error("查询Nebula边数据失败", e);
        } finally {
            releaseSession(session);
        }

        return null;
    }
}
