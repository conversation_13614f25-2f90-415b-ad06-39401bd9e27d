package com.geeksec.common.utils.number;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Random;

/**
 * 数字工具类
 * 提供数字处理的通用方法
 *
 * <AUTHOR> Team
 */
public class NumberUtils {

    /**
     * 日志记录器
     */
    private static final Logger logger = LoggerFactory.getLogger(NumberUtils.class);

    /**
     * 随机数生成器
     */
    private static final Random RANDOM = new Random();

    /**
     * 私有构造函数，防止实例化
     */
    private NumberUtils() {
        // 私有构造函数，防止实例化
    }

    /**
     * 将对象转换为整数
     *
     * @param obj 要转换的对象
     * @param defaultValue 默认值
     * @return 转换后的整数，如果转换失败则返回默认值
     */
    public static int toInt(Object obj, int defaultValue) {
        if (obj == null) {
            return defaultValue;
        }

        if (obj instanceof Number) {
            return ((Number) obj).intValue();
        }

        try {
            return Integer.parseInt(obj.toString().trim());
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * 将对象转换为长整数
     *
     * @param obj 要转换的对象
     * @param defaultValue 默认值
     * @return 转换后的长整数，如果转换失败则返回默认值
     */
    public static long toLong(Object obj, long defaultValue) {
        if (obj == null) {
            return defaultValue;
        }

        if (obj instanceof Number) {
            return ((Number) obj).longValue();
        }

        try {
            return Long.parseLong(obj.toString().trim());
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * 将对象转换为双精度浮点数
     *
     * @param obj 要转换的对象
     * @param defaultValue 默认值
     * @return 转换后的双精度浮点数，如果转换失败则返回默认值
     */
    public static double toDouble(Object obj, double defaultValue) {
        if (obj == null) {
            return defaultValue;
        }

        if (obj instanceof Number) {
            return ((Number) obj).doubleValue();
        }

        try {
            return Double.parseDouble(obj.toString().trim());
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * 将对象转换为浮点数
     *
     * @param obj 要转换的对象
     * @param defaultValue 默认值
     * @return 转换后的浮点数，如果转换失败则返回默认值
     */
    public static float toFloat(Object obj, float defaultValue) {
        if (obj == null) {
            return defaultValue;
        }

        if (obj instanceof Number) {
            return ((Number) obj).floatValue();
        }

        try {
            return Float.parseFloat(obj.toString().trim());
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * 将对象转换为BigDecimal
     *
     * @param obj 要转换的对象
     * @param defaultValue 默认值
     * @return 转换后的BigDecimal，如果转换失败则返回默认值
     */
    public static BigDecimal toBigDecimal(Object obj, BigDecimal defaultValue) {
        if (obj == null) {
            return defaultValue;
        }

        if (obj instanceof BigDecimal) {
            return (BigDecimal) obj;
        }

        try {
            return new BigDecimal(obj.toString().trim());
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * 将对象转换为BigInteger
     *
     * @param obj 要转换的对象
     * @param defaultValue 默认值
     * @return 转换后的BigInteger，如果转换失败则返回默认值
     */
    public static BigInteger toBigInteger(Object obj, BigInteger defaultValue) {
        if (obj == null) {
            return defaultValue;
        }

        if (obj instanceof BigInteger) {
            return (BigInteger) obj;
        }

        try {
            return new BigInteger(obj.toString().trim());
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * 将对象转换为布尔值
     *
     * @param obj 要转换的对象
     * @param defaultValue 默认值
     * @return 转换后的布尔值，如果转换失败则返回默认值
     */
    public static boolean toBoolean(Object obj, boolean defaultValue) {
        if (obj == null) {
            return defaultValue;
        }

        if (obj instanceof Boolean) {
            return (Boolean) obj;
        }

        String strValue = obj.toString().trim().toLowerCase();
        return "true".equals(strValue) || "1".equals(strValue) || "yes".equals(strValue) || "y".equals(strValue);
    }

    /**
     * 判断字符串是否为数字
     *
     * @param str 要检查的字符串
     * @return 如果字符串是数字则返回true，否则返回false
     */
    public static boolean isNumber(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }

        try {
            Double.parseDouble(str.trim());
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 判断字符串是否为整数
     *
     * @param str 要检查的字符串
     * @return 如果字符串是整数则返回true，否则返回false
     */
    public static boolean isInteger(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }

        try {
            Long.parseLong(str.trim());
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 判断字符串是否为浮点数
     *
     * @param str 要检查的字符串
     * @return 如果字符串是浮点数则返回true，否则返回false
     */
    public static boolean isFloat(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }

        try {
            Float.parseFloat(str.trim());
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 判断字符串是否为双精度浮点数
     *
     * @param str 要检查的字符串
     * @return 如果字符串是双精度浮点数则返回true，否则返回false
     */
    public static boolean isDouble(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }

        try {
            Double.parseDouble(str.trim());
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 格式化数字
     *
     * @param number 要格式化的数字
     * @param pattern 格式化模式
     * @return 格式化后的字符串
     */
    public static String format(Number number, String pattern) {
        if (number == null) {
            return "";
        }

        try {
            DecimalFormat df = new DecimalFormat(pattern);
            return df.format(number);
        } catch (Exception e) {
            logger.error("格式化数字失败: {}", number, e);
            return number.toString();
        }
    }

    /**
     * 格式化数字为千分位
     *
     * @param number 要格式化的数字
     * @return 格式化后的字符串
     */
    public static String formatWithCommas(Number number) {
        return format(number, "#,##0.##");
    }

    /**
     * 格式化数字为指定小数位数
     *
     * @param number 要格式化的数字
     * @param scale 小数位数
     * @return 格式化后的字符串
     */
    public static String formatWithScale(Number number, int scale) {
        if (number == null) {
            return "";
        }

        StringBuilder pattern = new StringBuilder("#,##0");
        if (scale > 0) {
            pattern.append(".");
            for (int i = 0; i < scale; i++) {
                pattern.append("0");
            }
        }

        return format(number, pattern.toString());
    }

    /**
     * 四舍五入
     *
     * @param value 要四舍五入的数值
     * @param scale 小数位数
     * @return 四舍五入后的数值
     */
    public static double round(double value, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("小数位数不能为负数");
        }

        BigDecimal bd = BigDecimal.valueOf(value);
        bd = bd.setScale(scale, RoundingMode.HALF_UP);
        return bd.doubleValue();
    }

    /**
     * 向上取整
     *
     * @param value 要向上取整的数值
     * @param scale 小数位数
     * @return 向上取整后的数值
     */
    public static double ceil(double value, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("小数位数不能为负数");
        }

        BigDecimal bd = BigDecimal.valueOf(value);
        bd = bd.setScale(scale, RoundingMode.CEILING);
        return bd.doubleValue();
    }

    /**
     * 向下取整
     *
     * @param value 要向下取整的数值
     * @param scale 小数位数
     * @return 向下取整后的数值
     */
    public static double floor(double value, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("小数位数不能为负数");
        }

        BigDecimal bd = BigDecimal.valueOf(value);
        bd = bd.setScale(scale, RoundingMode.FLOOR);
        return bd.doubleValue();
    }

    /**
     * 生成指定范围内的随机整数
     *
     * @param min 最小值（包含）
     * @param max 最大值（包含）
     * @return 生成的随机整数
     */
    public static int random(int min, int max) {
        if (min > max) {
            throw new IllegalArgumentException("最小值不能大于最大值");
        }

        return min + RANDOM.nextInt(max - min + 1);
    }

    /**
     * 生成指定范围内的随机长整数
     *
     * @param min 最小值（包含）
     * @param max 最大值（包含）
     * @return 生成的随机长整数
     */
    public static long random(long min, long max) {
        if (min > max) {
            throw new IllegalArgumentException("最小值不能大于最大值");
        }

        return min + (long) (RANDOM.nextDouble() * (max - min + 1));
    }

    /**
     * 生成指定范围内的随机双精度浮点数
     *
     * @param min 最小值（包含）
     * @param max 最大值（包含）
     * @return 生成的随机双精度浮点数
     */
    public static double random(double min, double max) {
        if (min > max) {
            throw new IllegalArgumentException("最小值不能大于最大值");
        }

        return min + RANDOM.nextDouble() * (max - min);
    }

    /**
     * 计算两个数的最大公约数
     *
     * @param a 第一个数
     * @param b 第二个数
     * @return 最大公约数
     */
    public static int gcd(int a, int b) {
        if (b == 0) {
            return a;
        }
        return gcd(b, a % b);
    }

    /**
     * 计算两个数的最小公倍数
     *
     * @param a 第一个数
     * @param b 第二个数
     * @return 最小公倍数
     */
    public static int lcm(int a, int b) {
        return a * b / gcd(a, b);
    }

    /**
     * 判断一个数是否为质数
     *
     * @param n 要判断的数
     * @return 如果是质数则返回true，否则返回false
     */
    public static boolean isPrime(int n) {
        if (n <= 1) {
            return false;
        }
        if (n <= 3) {
            return true;
        }
        if (n % 2 == 0 || n % 3 == 0) {
            return false;
        }

        for (int i = 5; i * i <= n; i += 6) {
            if (n % i == 0 || n % (i + 2) == 0) {
                return false;
            }
        }

        return true;
    }

    /**
     * 计算阶乘
     *
     * @param n 要计算阶乘的数
     * @return 阶乘结果
     */
    public static long factorial(int n) {
        if (n < 0) {
            throw new IllegalArgumentException("负数没有阶乘");
        }

        long result = 1;
        for (int i = 2; i <= n; i++) {
            result *= i;
        }

        return result;
    }

    /**
     * 计算组合数 C(n,k)
     *
     * @param n 总数
     * @param k 选择数
     * @return 组合数
     */
    public static long combination(int n, int k) {
        if (n < 0 || k < 0 || k > n) {
            throw new IllegalArgumentException("参数不合法");
        }

        if (k == 0 || k == n) {
            return 1;
        }

        k = Math.min(k, n - k);
        long result = 1;

        for (int i = 1; i <= k; i++) {
            result = result * (n - k + i) / i;
        }

        return result;
    }

    /**
     * 计算排列数 P(n,k)
     *
     * @param n 总数
     * @param k 选择数
     * @return 排列数
     */
    public static long permutation(int n, int k) {
        if (n < 0 || k < 0 || k > n) {
            throw new IllegalArgumentException("参数不合法");
        }

        long result = 1;
        for (int i = n - k + 1; i <= n; i++) {
            result *= i;
        }

        return result;
    }
}
