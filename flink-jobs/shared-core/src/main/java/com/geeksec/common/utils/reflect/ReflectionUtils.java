package com.geeksec.common.utils.reflect;

import org.apache.commons.lang3.ClassUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 反射工具类
 * 提供基于Apache Commons Lang的反射工具类的增强功能，主要添加了错误处理
 *
 * <AUTHOR> Team
 */
public final class ReflectionUtils {
    private static final Logger logger = LoggerFactory.getLogger(ReflectionUtils.class);

    private ReflectionUtils() {
        throw new AssertionError("工具类不需要实例化");
    }

    /**
     * 判断对象是否为指定类型的实例
     *
     * @param obj 要检查的对象
     * @param className 类名
     * @return 如果对象是指定类型的实例则返回true
     */
    public static boolean isInstance(Object obj, String className) {
        try {
            Class<?> clazz = Class.forName(className);
            return ClassUtils.isAssignable(obj.getClass(), clazz);
        } catch (ClassNotFoundException e) {
            logger.error("类未找到: {}", className, e);
            return false;
        }
    }

    /**
     * 判断类是否为另一个类的子类
     *
     * @param childClass 子类
     * @param parentClass 父类
     * @return 如果childClass是parentClass的子类则返回true
     */
    public static boolean isSubclass(Class<?> childClass, Class<?> parentClass) {
        return ClassUtils.isAssignable(childClass, parentClass);
    }

    /**
     * 将Long类型转换为Integer类型
     *
     * @param value Long类型值
     * @return Integer类型值
     */
    public static Integer longInstanceofInt(Long value) {
        if (value == null) {
            return 0;
        }
        return value.intValue();
    }

    /**
     * 将Integer类型转换为Long类型
     *
     * @param value Integer类型值
     * @return Long类型值
     */
    public static Long intInstanceofLong(Integer value) {
        if (value == null) {
            return 0L;
        }
        return value.longValue();
    }
}
