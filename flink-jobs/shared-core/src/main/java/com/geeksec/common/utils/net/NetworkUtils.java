package com.geeksec.common.utils.net;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.util.Enumeration;

import lombok.extern.slf4j.Slf4j;

/**
 * 网络工具类
 * 提供网络接口和TTL相关的通用方法
 *
 * <AUTHOR> Team
 */
@Slf4j
public final class NetworkUtils {

    /**
     * 默认TTL值
     */
    private static final int DEFAULT_TTL = 64;

    /**
     * 最大TTL值
     */
    private static final int MAX_TTL = 255;

    /**
     * TTL查找表大小
     */
    private static final int TTL_LOOKUP_TABLE_SIZE = 256;

    /**
     * TTL查找表，用于根据观察到的TTL值推断原始TTL值
     */
    private static final int[] TTL_LOOKUP_TABLE = new int[TTL_LOOKUP_TABLE_SIZE];

    static {
        // 初始化TTL查找表
        int ttl = 1;
        for (int i = 0; i < TTL_LOOKUP_TABLE_SIZE; i++) {
            if (i > ttl) {
                if (ttl >= 0x80) {
                    ttl = 0xff;
                } else {
                    ttl = ttl << 1;
                }
            }
            TTL_LOOKUP_TABLE[i] = ttl;
        }
    }

    private NetworkUtils() {
        throw new AssertionError("工具类不需要实例化");
    }

    /**
     * 获取本机IP地址
     *
     * @return 本机IP地址，如果获取失败则返回null
     */
    public static String getLocalIp() {
        try {
            return InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            log.error("获取本机IP地址失败", e);
            return null;
        }
    }

    /**
     * 获取网络接口数量
     *
     * @return 网络接口数量
     */
    public static int getNetworkInterfaceCount() {
        try {
            int count = 0;
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                interfaces.nextElement();
                count++;
            }
            return count;
        } catch (SocketException e) {
            log.error("获取网络接口数量失败", e);
            return 0;
        }
    }

    /**
     * 判断是否有活动的网络接口
     *
     * @return 如果有活动的网络接口则返回true
     */
    public static boolean hasActiveInterface() {
        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface networkInterface = interfaces.nextElement();
                if (networkInterface.isUp() && !networkInterface.isLoopback()) {
                    return true;
                }
            }
        } catch (SocketException e) {
            log.error("检查网络接口状态失败", e);
        }
        return false;
    }

    /**
     * 获取本机主机名
     *
     * @return 本机主机名，如果获取失败则返回null
     */
    public static String getLocalHostname() {
        try {
            return InetAddress.getLocalHost().getHostName();
        } catch (UnknownHostException e) {
            log.error("获取本机主机名失败", e);
            return null;
        }
    }

    /**
     * 判断IP地址是否可达
     *
     * @param ipAddress IP地址
     * @param timeout 超时时间（毫秒）
     * @return 如果可达则返回true，否则返回false
     */
    public static boolean isReachable(String ipAddress, int timeout) {
        try {
            InetAddress address = InetAddress.getByName(ipAddress);
            return address.isReachable(timeout);
        } catch (Exception e) {
            log.error("检查IP地址可达性失败: {}", ipAddress, e);
            return false;
        }
    }

    /**
     * 判断TTL值是否合理
     *
     * @param ttl TTL值
     * @return 如果TTL值在合理范围内则返回true
     */
    public static boolean isReasonableTtl(int ttl) {
        return ttl > 0 && ttl <= MAX_TTL;
    }

    /**
     * 获取建议的TTL值
     *
     * 注意：这个方法总是返回默认值64，因为无法通过Java API准确获取系统TTL值
     *
     * @return 建议的TTL值
     */
    public static int getSuggestedTtl() {
        return DEFAULT_TTL;
    }

    /**
     * 根据观察到的TTL值推断原始TTL值
     *
     * 常见的TTL初始值为：
     * - Windows系统: 128
     * - Unix/Linux系统: 64
     * - 某些路由设备: 255
     *
     * @param observedTtl 观察到的TTL值
     * @return 推断的原始TTL值
     */
    public static int inferOriginalTtl(int observedTtl) {
        if (observedTtl < 0 || observedTtl > MAX_TTL) {
            return DEFAULT_TTL;
        }
        return TTL_LOOKUP_TABLE[observedTtl];
    }

    /**
     * 计算TTL距离（跳数）
     *
     * @param originalTtl 原始TTL值
     * @param observedTtl 观察到的TTL值
     * @return TTL距离（跳数）
     */
    public static int calculateTtlDistance(int originalTtl, int observedTtl) {
        if (!isReasonableTtl(originalTtl) || !isReasonableTtl(observedTtl) || observedTtl > originalTtl) {
            return 0;
        }
        return originalTtl - observedTtl;
    }

    /**
     * 计算初始TTL值
     * 从NetworkTtlCalculator合并的方法
     * 根据观察到的TTL值推断初始TTL值
     *
     * @param observedTtl 观察到的TTL值
     * @return 推断的初始TTL值
     */
    public static int calculateInitialTtl(int observedTtl) {
        if (observedTtl <= 0) {
            return DEFAULT_TTL;
        }

        // 常见的初始TTL值
        int[] commonTtls = {32, 64, 128, 255};

        // 找到大于等于观察值的最小初始TTL
        for (int ttl : commonTtls) {
            if (ttl >= observedTtl) {
                return ttl;
            }
        }

        return DEFAULT_TTL;
    }

    /**
     * 计算跳数
     * 从NetworkTtlCalculator合并的方法
     * 根据初始TTL和观察到的TTL计算经过的跳数
     *
     * @param initialTtl 初始TTL值
     * @param observedTtl 观察到的TTL值
     * @return 经过的跳数
     */
    public static int calculateHops(int initialTtl, int observedTtl) {
        if (initialTtl < observedTtl || observedTtl <= 0) {
            return 0;
        }

        return initialTtl - observedTtl;
    }

    /**
     * 估算距离
     * 从NetworkTtlCalculator合并的方法
     * 根据跳数粗略估算网络距离（仅供参考）
     *
     * @param hops 跳数
     * @return 估算的距离（单位：公里）
     */
    public static double estimateDistance(int hops) {
        if (hops <= 0) {
            return 0.0;
        }

        // 这是一个非常粗略的估算，实际距离取决于网络拓扑
        // 假设每跳平均500公里
        return hops * 500.0;
    }
}
