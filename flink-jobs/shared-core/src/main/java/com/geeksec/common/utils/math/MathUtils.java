package com.geeksec.common.utils.math;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 数学工具类
 * 提供数学计算相关的工具方法
 *
 * <AUTHOR> Team
 */
public class MathUtils {

    /**
     * 私有构造函数，防止实例化
     */
    private MathUtils() {
        // 私有构造函数，防止实例化
    }

    /**
     * 高精度加法
     *
     * @param a 第一个数
     * @param b 第二个数
     * @return 加法结果
     */
    public static double add(double a, double b) {
        BigDecimal bd1 = BigDecimal.valueOf(a);
        BigDecimal bd2 = BigDecimal.valueOf(b);
        return bd1.add(bd2).doubleValue();
    }

    /**
     * 高精度减法
     *
     * @param a 第一个数
     * @param b 第二个数
     * @return 减法结果
     */
    public static double subtract(double a, double b) {
        BigDecimal bd1 = BigDecimal.valueOf(a);
        BigDecimal bd2 = BigDecimal.valueOf(b);
        return bd1.subtract(bd2).doubleValue();
    }

    /**
     * 高精度乘法
     *
     * @param a 第一个数
     * @param b 第二个数
     * @return 乘法结果
     */
    public static double multiply(double a, double b) {
        BigDecimal bd1 = BigDecimal.valueOf(a);
        BigDecimal bd2 = BigDecimal.valueOf(b);
        return bd1.multiply(bd2).doubleValue();
    }

    /**
     * 高精度除法
     *
     * @param a 第一个数
     * @param b 第二个数
     * @param scale 小数位数
     * @param roundingMode 舍入模式
     * @return 除法结果
     */
    public static double divide(double a, double b, int scale, RoundingMode roundingMode) {
        if (b == 0) {
            throw new ArithmeticException("除数不能为0");
        }
        
        BigDecimal bd1 = BigDecimal.valueOf(a);
        BigDecimal bd2 = BigDecimal.valueOf(b);
        return bd1.divide(bd2, scale, roundingMode).doubleValue();
    }

    /**
     * 高精度除法（默认四舍五入，2位小数）
     *
     * @param a 第一个数
     * @param b 第二个数
     * @return 除法结果
     */
    public static double divide(double a, double b) {
        return divide(a, b, 2, RoundingMode.HALF_UP);
    }

    /**
     * 四舍五入
     *
     * @param value 要四舍五入的数值
     * @param scale 小数位数
     * @return 四舍五入后的数值
     */
    public static double round(double value, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("小数位数不能为负数");
        }
        
        BigDecimal bd = BigDecimal.valueOf(value);
        bd = bd.setScale(scale, RoundingMode.HALF_UP);
        return bd.doubleValue();
    }

    /**
     * 向上取整
     *
     * @param value 要向上取整的数值
     * @param scale 小数位数
     * @return 向上取整后的数值
     */
    public static double ceil(double value, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("小数位数不能为负数");
        }
        
        BigDecimal bd = BigDecimal.valueOf(value);
        bd = bd.setScale(scale, RoundingMode.CEILING);
        return bd.doubleValue();
    }

    /**
     * 向下取整
     *
     * @param value 要向下取整的数值
     * @param scale 小数位数
     * @return 向下取整后的数值
     */
    public static double floor(double value, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("小数位数不能为负数");
        }
        
        BigDecimal bd = BigDecimal.valueOf(value);
        bd = bd.setScale(scale, RoundingMode.FLOOR);
        return bd.doubleValue();
    }

    /**
     * 计算百分比
     *
     * @param value 值
     * @param total 总值
     * @param scale 小数位数
     * @return 百分比值
     */
    public static double percentage(double value, double total, int scale) {
        if (total == 0) {
            return 0;
        }
        
        return round(value / total * 100, scale);
    }

    /**
     * 计算百分比（默认2位小数）
     *
     * @param value 值
     * @param total 总值
     * @return 百分比值
     */
    public static double percentage(double value, double total) {
        return percentage(value, total, 2);
    }

    /**
     * 判断两个浮点数是否相等（考虑精度误差）
     *
     * @param a 第一个数
     * @param b 第二个数
     * @param epsilon 精度误差
     * @return 如果两个数相等则返回true，否则返回false
     */
    public static boolean equals(double a, double b, double epsilon) {
        return Math.abs(a - b) < epsilon;
    }

    /**
     * 判断两个浮点数是否相等（默认精度误差为1e-10）
     *
     * @param a 第一个数
     * @param b 第二个数
     * @return 如果两个数相等则返回true，否则返回false
     */
    public static boolean equals(double a, double b) {
        return equals(a, b, 1e-10);
    }

    /**
     * 计算平均值
     *
     * @param values 数值数组
     * @return 平均值
     */
    public static double average(double[] values) {
        if (values == null || values.length == 0) {
            return 0;
        }
        
        double sum = 0;
        for (double value : values) {
            sum = add(sum, value);
        }
        
        return divide(sum, values.length);
    }

    /**
     * 计算最大值
     *
     * @param values 数值数组
     * @return 最大值
     */
    public static double max(double[] values) {
        if (values == null || values.length == 0) {
            throw new IllegalArgumentException("数组不能为空");
        }
        
        double max = values[0];
        for (int i = 1; i < values.length; i++) {
            if (values[i] > max) {
                max = values[i];
            }
        }
        
        return max;
    }

    /**
     * 计算最小值
     *
     * @param values 数值数组
     * @return 最小值
     */
    public static double min(double[] values) {
        if (values == null || values.length == 0) {
            throw new IllegalArgumentException("数组不能为空");
        }
        
        double min = values[0];
        for (int i = 1; i < values.length; i++) {
            if (values[i] < min) {
                min = values[i];
            }
        }
        
        return min;
    }

    /**
     * 计算总和
     *
     * @param values 数值数组
     * @return 总和
     */
    public static double sum(double[] values) {
        if (values == null || values.length == 0) {
            return 0;
        }
        
        double sum = 0;
        for (double value : values) {
            sum = add(sum, value);
        }
        
        return sum;
    }

    /**
     * 计算方差
     *
     * @param values 数值数组
     * @return 方差
     */
    public static double variance(double[] values) {
        if (values == null || values.length == 0) {
            return 0;
        }
        
        double avg = average(values);
        double sum = 0;
        
        for (double value : values) {
            double diff = subtract(value, avg);
            sum = add(sum, multiply(diff, diff));
        }
        
        return divide(sum, values.length);
    }

    /**
     * 计算标准差
     *
     * @param values 数值数组
     * @return 标准差
     */
    public static double standardDeviation(double[] values) {
        return Math.sqrt(variance(values));
    }

    /**
     * 计算中位数
     *
     * @param values 数值数组
     * @return 中位数
     */
    public static double median(double[] values) {
        if (values == null || values.length == 0) {
            throw new IllegalArgumentException("数组不能为空");
        }
        
        // 复制数组并排序
        double[] sortedValues = values.clone();
        java.util.Arrays.sort(sortedValues);
        
        int length = sortedValues.length;
        if (length % 2 == 0) {
            // 偶数个元素，取中间两个元素的平均值
            return divide(add(sortedValues[length / 2 - 1], sortedValues[length / 2]), 2);
        } else {
            // 奇数个元素，取中间元素
            return sortedValues[length / 2];
        }
    }

    /**
     * 高精度加法（兼容前端方法名）
     *
     * @param a 第一个数
     * @param b 第二个数
     * @return 加法结果
     */
    public static double accAdd(double a, double b) {
        return add(a, b);
    }

    /**
     * 高精度减法（兼容前端方法名）
     *
     * @param a 第一个数
     * @param b 第二个数
     * @return 减法结果
     */
    public static double accSub(double a, double b) {
        return subtract(a, b);
    }

    /**
     * 高精度乘法（兼容前端方法名）
     *
     * @param a 第一个数
     * @param b 第二个数
     * @return 乘法结果
     */
    public static double accMul(double a, double b) {
        return multiply(a, b);
    }

    /**
     * 高精度除法（兼容前端方法名）
     *
     * @param a 第一个数
     * @param b 第二个数
     * @param scale 小数位数
     * @return 除法结果
     */
    public static double accDiv(double a, double b, int scale) {
        return divide(a, b, scale, RoundingMode.HALF_UP);
    }
}
