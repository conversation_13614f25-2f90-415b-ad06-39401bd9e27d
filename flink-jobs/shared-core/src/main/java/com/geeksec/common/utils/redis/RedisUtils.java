package com.geeksec.common.utils.redis;

import static com.geeksec.common.config.ConfigConstants.REDIS_HOST;
import static com.geeksec.common.config.ConfigConstants.REDIS_KEY_TTL;
import static com.geeksec.common.config.ConfigConstants.REDIS_PASSWORD;
import static com.geeksec.common.config.ConfigConstants.REDIS_PORT;
import static com.geeksec.common.config.ConfigConstants.REDIS_TIMEOUT;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.types.Row;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.common.config.ConfigurationManager;

import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.params.SetParams;

/**
 * Redis工具类
 * 提供Redis连接池和常用操作方法
 *
 * <AUTHOR> Team
 */
@Slf4j
public class RedisUtils {

    /**
     * 配置工具实例
     */
    private static final ParameterTool CONFIG = ConfigurationManager.getConfig();

    /**
     * Redis连接池
     */
    private static JedisPool jedisPool = null;

    /**
     * Redis主机地址
     */
    private static final String REDIS_HOST_ADDR = CONFIG.get(REDIS_HOST);

    /**
     * Redis端口
     */
    private static final int REDIS_PORT_NUM = CONFIG.getInt(REDIS_PORT, 6379);

    /**
     * Redis密码
     */
    private static final String REDIS_PWD = CONFIG.get(REDIS_PASSWORD, "");

    /**
     * Redis超时时间（毫秒）
     */
    private static final int REDIS_TIMEOUT_MS = CONFIG.getInt(REDIS_TIMEOUT, 10000);

    /**
     * Redis键过期时间（秒）
     */
    private static final int REDIS_EXPIRE_SECOND = CONFIG.getInt(REDIS_KEY_TTL, 3600 * 24 * 7);

    /**
     * 初始化Redis连接池
     *
     * @return Redis连接池
     */
    public static synchronized JedisPool initJedisPool() {
        if (jedisPool != null && !jedisPool.isClosed()) {
            return jedisPool;
        }

        try {
            JedisPoolConfig config = new JedisPoolConfig();
            config.setMaxTotal(100);
            config.setMaxIdle(50);
            config.setMinIdle(10);
            config.setMaxWaitMillis(10000);
            config.setTestOnBorrow(true);
            config.setTestOnReturn(true);

            if (StringUtils.isNotEmpty(REDIS_PWD)) {
                jedisPool = new JedisPool(config, REDIS_HOST_ADDR, REDIS_PORT_NUM, REDIS_TIMEOUT_MS, REDIS_PWD);
            } else {
                jedisPool = new JedisPool(config, REDIS_HOST_ADDR, REDIS_PORT_NUM, REDIS_TIMEOUT_MS);
            }
            log.info("Redis连接池初始化成功");
        } catch (Exception e) {
            log.error("Redis连接池初始化失败", e);
        }
        return jedisPool;
    }

    /**
     * 获取Jedis实例
     *
     * @return Jedis实例
     */
    public static synchronized Jedis getJedis() {
        if (jedisPool == null || jedisPool.isClosed()) {
            jedisPool = initJedisPool();
        }

        Jedis jedis = null;
        try {
            if (jedisPool != null) {
                jedis = jedisPool.getResource();
            }
        } catch (Exception e) {
            log.error("获取Jedis实例失败", e);
        }
        return jedis;
    }

    /**
     * 检查键是否存在
     *
     * @param key 键
     * @return 如果键存在则返回true，否则返回false
     */
    public static boolean existsKey(String key) {
        Jedis jedis = getJedis();
        try {
            if (jedis != null) {
                return jedis.exists(key);
            }
        } catch (Exception e) {
            log.error("检查Redis键是否存在失败: {}", key, e);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
        return false;
    }

    /**
     * 设置字符串值
     *
     * @param key           键
     * @param value         值
     * @param expireSeconds 过期时间（秒）
     * @return 操作结果
     */
    public static String setString(String key, String value, int expireSeconds) {
        Jedis jedis = getJedis();
        try {
            if (jedis != null) {
                return jedis.setex(key, expireSeconds, value);
            }
        } catch (Exception e) {
            log.error("设置Redis键值失败: {}", key, e);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
        return null;
    }

    /**
     * 获取字符串值
     *
     * @param key 键
     * @return 值
     */
    public static String getString(String key) {
        Jedis jedis = getJedis();
        try {
            if (jedis != null) {
                return jedis.get(key);
            }
        } catch (Exception e) {
            log.error("获取Redis键值失败: {}", key, e);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
        return null;
    }

    /**
     * 删除键
     *
     * @param key 键
     * @return 删除的键数量
     */
    public static long deleteKey(String key) {
        Jedis jedis = getJedis();
        try {
            if (jedis != null) {
                return jedis.del(key);
            }
        } catch (Exception e) {
            log.error("删除Redis键失败: {}", key, e);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
        return 0;
    }

    /**
     * 通过Row信息设置缓存
     *
     * @param row     行数据
     * @param jedis   Jedis实例
     * @param isFirst 是否首次插入
     */
    public static void setValueByRow(Row row, Jedis jedis, boolean isFirst) {
        if (row == null || jedis == null) {
            return;
        }

        try {
            JSONObject rowJson = new JSONObject();
            for (int i = 0; i < row.getArity(); i++) {
                rowJson.put(String.valueOf(i), row.getField(i));
            }

            String rowJsonStr = rowJson.toJSONString();
            SetParams setParams = new SetParams();
            String key = StringUtils.EMPTY;

            // 常量定义
            final String tagSuffix = "TAG";
            final String tagPrefix = "TAG:";
            final String edgePrefix = "EDGE:";
            final String edgeSuffix = "_EDGE";

            if (row.getFieldAs(0).toString().endsWith(tagSuffix)) {
                key = tagPrefix + row.getFieldAs(0).toString().split("_")[0] + ":" + row.getFieldAs(1).toString();
            } else {
                String edgeType = row.getFieldAs(0).toString();
                // 如果是类似 "IP_CONNECT_EDGE" 这样的格式，提取 "IP_CONNECT" 部分
                if (edgeType.endsWith(edgeSuffix)) {
                    edgeType = edgeType.substring(0, edgeType.length() - edgeSuffix.length());
                }
                key = edgePrefix + edgeType + ":" + row.getFieldAs(1).toString() + "_"
                        + row.getFieldAs(2).toString();
            }

            if (isFirst) {
                // NX是不存在时才set， XX是存在时才set， EX是秒，PX是毫秒
                // 初次插入
                setParams.ex(REDIS_EXPIRE_SECOND).nx();
                jedis.set(key, rowJsonStr, setParams);
            } else {
                setParams.ex(REDIS_EXPIRE_SECOND).xx();
                jedis.set(key, rowJsonStr, setParams);
            }
        } catch (Exception e) {
            log.error("写入Redis键值失败: {}", row, e);
        }
    }

    /**
     * 获取键对应的值（兼容旧方法名）
     *
     * @param key 键
     * @return 值
     * @deprecated 使用 {@link #getString(String)} 代替
     */
    @Deprecated
    public static String getValue(String key) {
        return getString(key);
    }

    /**
     * 设置哈希表字段值
     *
     * @param key   键
     * @param field 字段
     * @param value 值
     * @return 操作结果
     */
    public static long setHash(String key, String field, String value) {
        Jedis jedis = getJedis();
        try {
            if (jedis != null) {
                return jedis.hset(key, field, value);
            }
        } catch (Exception e) {
            log.error("设置Redis哈希表字段值失败: {}:{}", key, field, e);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
        return 0;
    }

    /**
     * 获取哈希表字段值
     *
     * @param key   键
     * @param field 字段
     * @return 值
     */
    public static String getHash(String key, String field) {
        Jedis jedis = getJedis();
        try {
            if (jedis != null) {
                return jedis.hget(key, field);
            }
        } catch (Exception e) {
            log.error("获取Redis哈希表字段值失败: {}:{}", key, field, e);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
        return null;
    }

    /**
     * 获取哈希表所有字段和值
     *
     * @param key 键
     * @return 字段和值的映射
     */
    public static Map<String, String> getAllHash(String key) {
        Jedis jedis = getJedis();
        try {
            if (jedis != null) {
                return jedis.hgetAll(key);
            }
        } catch (Exception e) {
            log.error("获取Redis哈希表所有字段和值失败: {}", key, e);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
        return null;
    }

    /**
     * 关闭Redis连接池
     */
    public static void closeJedisPool() {
        if (jedisPool != null && !jedisPool.isClosed()) {
            jedisPool.close();
            jedisPool = null;
            log.info("Redis连接池已关闭");
        }
    }
}
