package com.geeksec.common.utils.string;

import com.google.common.base.CaseFormat;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 字符串辅助类
 * 提供一些特定的字符串处理方法，主要是对Apache Commons Lang3和Google Guava的补充
 *
 * <AUTHOR> Team
 */
public final class StringHelper {

    /**
     * 日志记录器
     */
    private static final Logger logger = LoggerFactory.getLogger(StringHelper.class);

    /**
     * 私有构造函数，防止实例化
     */
    private StringHelper() {
        throw new AssertionError("工具类不需要实例化");
    }

    /**
     * 判断字符串是否为空或"null"字符串
     *
     * @param str 要检查的字符串
     * @return 如果字符串为null、空字符串或"null"字符串，则返回true
     */
    public static boolean isNullOrEmpty(String str) {
        return StringUtils.isEmpty(str) || "null".equalsIgnoreCase(str);
    }

    /**
     * 将驼峰式命名转为下划线命名
     *
     * @param str 要转换的字符串
     * @return 转换后的字符串
     */
    public static String camelToUnderscore(String str) {
        if (StringUtils.isEmpty(str)) {
            return str;
        }

        // 使用Google Guava的CaseFormat进行转换
        if (Character.isUpperCase(str.charAt(0))) {
            // 如果首字母大写，则为UpperCamel (PascalCase)
            return CaseFormat.UPPER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, str);
        } else {
            // 如果首字母小写，则为lowerCamel
            return CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, str);
        }
    }

    /**
     * 将下划线命名转为驼峰式命名
     *
     * @param str 要转换的字符串
     * @param capitalizeFirstLetter 是否将首字母大写
     * @return 转换后的字符串
     */
    public static String underscoreToCamel(String str, boolean capitalizeFirstLetter) {
        if (StringUtils.isEmpty(str)) {
            return str;
        }

        // 使用Google Guava的CaseFormat进行转换
        if (capitalizeFirstLetter) {
            return CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.UPPER_CAMEL, str);
        } else {
            return CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, str);
        }
    }
}
