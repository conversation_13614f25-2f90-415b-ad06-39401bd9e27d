package com.geeksec.common.utils.net;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.base.CharMatcher;
import com.google.common.base.Joiner;

/**
 * MAC地址工具类
 * 提供MAC地址验证、格式化和厂商信息查询功能
 *
 * <AUTHOR>
 */
public final class MacUtils {

    private static final Logger log = LoggerFactory.getLogger(MacUtils.class);

    /**
     * MAC地址长度常量
     */
    private static final int MAC_ADDRESS_LENGTH = 12;
    private static final int MAC_BYTE_LENGTH = 2;
    private static final int MAC_BYTES_COUNT = 6;

    /**
     * OUI长度常量（AA:BB:CC格式）
     */
    private static final int OUI_LENGTH = 8;

    /**
     * MAC地址正则表达式（支持多种格式）
     * 支持格式：
     * - AA:BB:CC:DD:EE:FF
     * - AA-BB-CC-DD-EE-FF
     * - AABBCCDDEEFF
     * - AA.BB.CC.DD.EE.FF
     */
    private static final Pattern MAC_PATTERN = Pattern.compile(
            "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$|^[0-9A-Fa-f]{12}$|^([0-9A-Fa-f]{2}\\.){5}([0-9A-Fa-f]{2})$");

    /**
     * 十六进制字符匹配器
     */
    private static final CharMatcher HEX_CHARS = CharMatcher.anyOf("0123456789ABCDEFabcdef");

    /**
     * MAC地址分隔符匹配器
     */
    private static final CharMatcher MAC_SEPARATORS = CharMatcher.anyOf(":-.");

    /**
     * OUI数据库文件路径
     */
    private static final String OUI_DATABASE_FILE = "standards-oui.ieee.org.txt";

    /**
     * OUI缓存，使用ConcurrentHashMap保证线程安全
     */
    private static final Map<String, String> OUI_CACHE = new ConcurrentHashMap<>();

    /**
     * MAC地址格式化器（使用冒号分隔）
     */
    private static final Joiner MAC_JOINER = Joiner.on(':');

    /**
     * 初始化标志
     */
    private static volatile boolean initialized = false;

    /**
     * 初始化锁
     */
    private static final Object INIT_LOCK = new Object();

    private MacUtils() {
        throw new AssertionError("工具类不需要实例化");
    }

    /**
     * 验证MAC地址格式是否有效
     *
     * @param macAddress MAC地址
     * @return 如果格式有效则返回true
     */
    public static boolean isValidMac(String macAddress) {
        if (StringUtils.isEmpty(macAddress)) {
            return false;
        }
        return MAC_PATTERN.matcher(macAddress.trim()).matches();
    }

    /**
     * 标准化MAC地址格式为 AA:BB:CC:DD:EE:FF
     *
     * @param macAddress 原始MAC地址
     * @return 标准化后的MAC地址，如果格式无效则返回null
     */
    public static String normalizeMac(String macAddress) {
        if (!isValidMac(macAddress)) {
            return null;
        }

        // 移除所有分隔符并转换为大写
        String cleanMac = macAddress.replaceAll("[:-.]", "").toUpperCase();

        // 确保长度为12
        if (cleanMac.length() != MAC_ADDRESS_LENGTH) {
            return null;
        }

        // 使用Guava的Joiner格式化为 AA:BB:CC:DD:EE:FF
        String[] macBytes = new String[MAC_BYTES_COUNT];
        for (int i = 0; i < cleanMac.length(); i += MAC_BYTE_LENGTH) {
            macBytes[i / MAC_BYTE_LENGTH] = cleanMac.substring(i, i + MAC_BYTE_LENGTH);
        }

        return MAC_JOINER.join(macBytes);
    }

    /**
     * 获取MAC地址的OUI（前3个字节）
     *
     * @param macAddress MAC地址
     * @return OUI字符串，格式为 AA:BB:CC，如果MAC地址无效则返回null
     */
    public static String getOui(String macAddress) {
        String normalizedMac = normalizeMac(macAddress);
        if (normalizedMac == null) {
            return null;
        }

        // 返回前3个字节（前8个字符加2个冒号）
        return normalizedMac.substring(0, 8);
    }

    /**
     * 获取MAC地址的厂商信息
     *
     * @param macAddress MAC地址
     * @return 厂商信息，如果未找到则返回"Unknown"
     */
    public static String getVendor(String macAddress) {
        String oui = getOui(macAddress);
        if (oui == null) {
            return "Unknown";
        }

        // 确保OUI数据库已初始化
        ensureInitialized();

        // 查询OUI缓存
        String vendor = OUI_CACHE.get(oui);
        return vendor != null ? vendor : "Unknown";
    }

    /**
     * 获取MAC地址的详细信息
     *
     * @param macAddress MAC地址
     * @return 包含MAC地址详细信息的Map
     */
    public static Map<String, Object> getMacInfo(String macAddress) {
        Map<String, Object> macInfo = new HashMap<>(8);

        // 标准化MAC地址
        String normalizedMac = normalizeMac(macAddress);
        macInfo.put("mac", normalizedMac != null ? normalizedMac : macAddress);
        macInfo.put("is_valid", normalizedMac != null);

        if (normalizedMac != null) {
            // 获取OUI
            String oui = getOui(normalizedMac);
            macInfo.put("oui", oui);

            // 获取厂商信息
            String vendor = getVendor(normalizedMac);
            macInfo.put("vendor", vendor);

            // 判断是否为本地管理地址
            boolean isLocallyAdministered = isLocallyAdministered(normalizedMac);
            macInfo.put("is_locally_administered", isLocallyAdministered);

            // 判断是否为组播地址
            boolean isMulticast = isMulticast(normalizedMac);
            macInfo.put("is_multicast", isMulticast);

            // 判断是否可能为随机MAC地址（本地管理地址且非组播）
            boolean isPotentiallyRandomized = isLocallyAdministered && !isMulticast;
            macInfo.put("is_potentially_randomized", isPotentiallyRandomized);

            // 判断是否为虚拟化厂商
            boolean isVirtualized = isVirtualizedVendor(vendor);
            macInfo.put("is_virtualized", isVirtualized);
        } else {
            macInfo.put("oui", null);
            macInfo.put("vendor", "Unknown");
            macInfo.put("is_locally_administered", false);
            macInfo.put("is_multicast", false);
            macInfo.put("is_potentially_randomized", false);
            macInfo.put("is_virtualized", false);
        }

        return macInfo;
    }

    /**
     * 判断MAC地址是否为本地管理地址
     * 本地管理地址的第一个字节的第二位为1
     *
     * @param macAddress 标准化的MAC地址
     * @return 如果是本地管理地址则返回true
     */
    public static boolean isLocallyAdministered(String macAddress) {
        String normalizedMac = normalizeMac(macAddress);
        if (normalizedMac == null) {
            return false;
        }

        // 获取第一个字节
        String firstByte = normalizedMac.substring(0, 2);
        try {
            int byteValue = Integer.parseInt(firstByte, 16);
            // 检查第二位（从右数第二位）是否为1
            return (byteValue & 0x02) != 0;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 判断MAC地址是否为组播地址
     * 组播地址的第一个字节的最低位为1
     *
     * @param macAddress 标准化的MAC地址
     * @return 如果是组播地址则返回true
     */
    public static boolean isMulticast(String macAddress) {
        String normalizedMac = normalizeMac(macAddress);
        if (normalizedMac == null) {
            return false;
        }

        // 获取第一个字节
        String firstByte = normalizedMac.substring(0, 2);
        try {
            int byteValue = Integer.parseInt(firstByte, 16);
            // 检查最低位是否为1
            return (byteValue & 0x01) != 0;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 判断是否为虚拟化厂商
     *
     * @param vendor 厂商名称
     * @return 如果是虚拟化厂商则返回true
     */
    public static boolean isVirtualizedVendor(String vendor) {
        if (vendor == null) {
            return false;
        }

        String vendorLower = vendor.toLowerCase();
        return vendorLower.contains("vmware") ||
                vendorLower.contains("virtualbox") ||
                vendorLower.contains("parallels") ||
                (vendorLower.contains("microsoft") && vendorLower.contains("virtual")) ||
                vendorLower.contains("xen") ||
                vendorLower.contains("qemu") ||
                vendorLower.contains("kvm") ||
                vendorLower.contains("hyper-v") ||
                vendorLower.contains("citrix");
    }

    /**
     * 确保OUI数据库已初始化
     */
    private static void ensureInitialized() {
        if (!initialized) {
            synchronized (INIT_LOCK) {
                if (!initialized) {
                    loadOuiDatabase();
                    initialized = true;
                }
            }
        }
    }

    /**
     * 加载OUI数据库
     */
    private static void loadOuiDatabase() {
        log.info("开始加载IEEE OUI数据库...");

        try (InputStream inputStream = MacUtils.class.getClassLoader().getResourceAsStream(OUI_DATABASE_FILE)) {
            if (inputStream == null) {
                log.warn("未找到OUI数据库文件: {}", OUI_DATABASE_FILE);
                return;
            }

            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
                String line;
                String currentOui = null;
                String currentVendor = null;

                while ((line = reader.readLine()) != null) {
                    line = line.trim();

                    // 跳过空行和标题行
                    if (line.isEmpty() || line.startsWith("OUI/MA-L") || line.startsWith("company_id")) {
                        continue;
                    }

                    // 解析OUI行（格式：AA-BB-CC (hex) Vendor Name）
                    if (line.contains("(hex)")) {
                        String[] parts = line.split("\\s+", 3);
                        if (parts.length >= 3) {
                            String ouiPart = parts[0].trim();
                            // 将 AA-BB-CC 格式转换为 AA:BB:CC
                            currentOui = ouiPart.replace("-", ":");
                            // 提取厂商名称（去掉(hex)部分）
                            String vendorPart = line.substring(line.indexOf("(hex)") + 5).trim();
                            currentVendor = vendorPart;

                            // 存储到缓存
                            if (currentOui != null && currentVendor != null && !currentVendor.isEmpty()) {
                                OUI_CACHE.put(currentOui, currentVendor);
                            }
                        }
                    }
                }

                log.info("IEEE OUI数据库加载完成，共加载 {} 条记录", OUI_CACHE.size());
            }
        } catch (IOException e) {
            log.error("加载OUI数据库时发生错误", e);
        }
    }

    /**
     * 获取OUI缓存大小（用于测试和监控）
     *
     * @return OUI缓存中的记录数量
     */
    public static int getOuiCacheSize() {
        ensureInitialized();
        return OUI_CACHE.size();
    }

    /**
     * 清空OUI缓存并重新加载（用于刷新数据）
     */
    public static void refreshOuiDatabase() {
        synchronized (INIT_LOCK) {
            OUI_CACHE.clear();
            initialized = false;
            ensureInitialized();
        }
    }
}
