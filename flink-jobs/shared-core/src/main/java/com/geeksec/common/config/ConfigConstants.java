package com.geeksec.common.config;

/**
 * 配置常量类
 * 定义所有模块通用的配置项常量
 */
public class ConfigConstants {
    // Kafka相关配置
    public static final String KAFKA_BOOTSTRAP_SERVERS = "kafka.bootstrap.servers";
    public static final String KAFKA_GROUP_ID = "kafka.group.id";
    public static final String KAFKA_TOPIC_META = "kafka.topic.meta";
    public static final String KAFKA_MODEL_SWITCH_TOPIC = "kafka.modelSwitch.topic";
    public static final String KAFKA_MODEL_SWITCH_GROUP_ID = "kafka.modelSwitch.group.id";
    public static final String KAFKA_OUTPUT_TOPIC = "kafka.output.topic";
    public static final String KAFKA_ORDER_TOPIC = "kafka.order.topic";
    public static final String KAFKA_ORDER_GROUP_ID = "kafka.order.group.id";
    public static final String KAFKA_ALERT_TOPIC = "kafka.alert.topic";
    public static final String KAFKA_ALERT_GROUP_ID = "kafka.alert.group.id";
    
    // Kafka Topic 配置
    public static final String KAFKA_TOPIC_CONNECT = "kafka.topic.connect";
    public static final String KAFKA_TOPIC_HTTP = "kafka.topic.http";
    public static final String KAFKA_TOPIC_DNS = "kafka.topic.dns";
    public static final String KAFKA_TOPIC_SSL = "kafka.topic.ssl";
    public static final String KAFKA_TOPIC_SSH = "kafka.topic.ssh";
    public static final String KAFKA_TOPIC_RLOGIN = "kafka.topic.rlogin";
    public static final String KAFKA_TOPIC_TELNET = "kafka.topic.telnet";
    public static final String KAFKA_TOPIC_RDP = "kafka.topic.rdp";
    public static final String KAFKA_TOPIC_VNC = "kafka.topic.vnc";
    public static final String KAFKA_TOPIC_XDMCP = "kafka.topic.xdmcp";
    public static final String KAFKA_TOPIC_NTP = "kafka.topic.ntp";
    public static final String KAFKA_TOPIC_ICMP = "kafka.topic.icmp";
    
    public static final String KAFKA_SECURITY_PROTOCOL = "kafka.security.protocol";
    public static final String KAFKA_SASL_MECHANISM = "kafka.sasl.mechanism";
    public static final String KAFKA_SASL_JAAS_CONFIG = "kafka.sasl.jaas.config";
    public static final String KAFKA_CLIENT_USER = "KAFKA_CLIENT_USER";
    public static final String KAFKA_CLIENT_PASSWORD = "KAFKA_CLIENT_PASSWORD";

    // 证书相关配置
    public static final String CERT_KAFKA_TOPIC_NAME = "cert.kafka.topic.name";
    public static final String CERT_KAFKA_GROUP_ID = "cert.kafka.group.id";

    // Redis相关配置
    public static final String REDIS_HOST = "redis.host";
    public static final String REDIS_PORT = "redis.port";
    public static final String REDIS_PASSWORD = "redis.password";
    public static final String REDIS_TIMEOUT = "redis.timeout";
    public static final String REDIS_POOL_MAX_TOTAL = "redis.pool.max-total";
    public static final String REDIS_POOL_MAX_IDLE = "redis.pool.max-idle";
    public static final String REDIS_POOL_MIN_IDLE = "redis.pool.min-idle";
    public static final String REDIS_POOL_MAX_WAIT = "redis.pool.max-wait";
    public static final String REDIS_KEY_TTL = "redis.key.ttl";

    // Elasticsearch相关配置
    public static final String ELASTICSEARCH_HOST = "elasticsearch.host";
    public static final String ELASTICSEARCH_PORT = "elasticsearch.port";
    public static final String ELASTICSEARCH_USERNAME = "ELASTICSEARCH_USERNAME";
    public static final String ELASTICSEARCH_PASSWORD = "ELASTICSEARCH_PASSWORD";

    // MySQL相关配置
    public static final String MYSQL_URL = "mysql.url";
    public static final String MYSQL_HOST = "mysql.host";
    public static final String MYSQL_PORT = "mysql.port";
    public static final String MYSQL_DATABASE = "mysql.database";
    public static final String MYSQL_USERNAME = "MYSQL_USERNAME";
    public static final String MYSQL_PASSWORD = "MYSQL_PASSWORD";

    // Nebula相关配置
    public static final String NEBULA_META_ADDR = "nebula.meta.addr";
    public static final String NEBULA_GRAPH_ADDR = "nebula.graph.addr";
    public static final String NEBULA_SPACE_NAME = "nebula.space.name";
    public static final String NEBULA_USERNAME = "NEBULA_USERNAME";
    public static final String NEBULA_PASSWORD = "NEBULA_PASSWORD";
    public static final String NEBULA_POOL_MAX_CONN_SIZE = "nebula.pool.max.conn.size";
    public static final String NEBULA_POOL_MIN_CONN_SIZE = "nebula.pool.min.conn.size";
    public static final String NEBULA_POOL_IDLE_TIME = "nebula.pool.idle.time";
    public static final String NEBULA_POOL_TIMEOUT = "nebula.pool.timeout";
    public static final String NEBULA_SESSION_SIZE = "nebula.session.size";

    // MinIO相关配置
    public static final String MINIO_ENDPOINT = "minio.endpoint";
    public static final String MINIO_ACCESS_KEY = "MINIO_ACCESS_KEY";
    public static final String MINIO_SECRET_KEY = "MINIO_SECRET_KEY";
    public static final String MINIO_BUCKET_NAME = "minio.bucket.name";
    public static final String MINIO_PATH_PREFIX = "minio.path.prefix";

    // Doris相关配置
    public static final String DORIS_FE_NODES = "doris.fe.nodes";
    public static final String DORIS_DATABASE = "doris.database";
    public static final String DORIS_USERNAME = "DORIS_USERNAME";
    public static final String DORIS_PASSWORD = "DORIS_PASSWORD";
    public static final String DORIS_SINK_PARALLELISM = "doris.sink.parallelism";

    // 并行度相关配置
    public static final String PARALLELISM_KAFKA_SOURCE = "parallelism.kafka.source";
    public static final String PARALLELISM_PARSING = "parallelism.parsing";
    public static final String PARALLELISM_PROCESSING = "parallelism.processing";
    public static final String PARALLELISM_ELASTICSEARCH_SINK = "parallelism.elasticsearch.sink";
    public static final String PARALLELISM_KAFKA_SINK = "parallelism.kafka.sink";
    public static final String PARALLELISM_KAFKA_JSON_SINK = "parallelism.kafka.json-sink";
    public static final String PARALLELISM_CONNECT_INFO_UPDATE = "parallelism.connect-info.update";
    public static final String PARALLELISM_NEBULA_SINK = "parallelism.nebula.sink";
    public static final String PARALLELISM_DORIS_SINK = "parallelism.doris.sink";
}
