package com.geeksec.common.utils.net;

import java.net.Inet4Address;
import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * IP工具类
 * 提供IP地址处理的通用方法
 *
 * <AUTHOR> Team
 */
public class IpUtils {

    /**
     * 判断是否为有效的IPv4地址
     *
     * @param ip IP地址
     * @return 如果是有效的IPv4地址则返回true
     */
    public static boolean isValidIpv4(String ip) {
        try {
            return InetAddress.getByName(ip) instanceof Inet4Address;
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 判断是否为有效的IPv6地址
     *
     * @param ip IP地址
     * @return 如果是有效的IPv6地址则返回true
     */
    public static boolean isValidIpv6(String ip) {
        try {
            return InetAddress.getByName(ip) instanceof Inet6Address;
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 判断是否为有效的IP地址（IPv4或IPv6）
     *
     * @param ip IP地址
     * @return 如果是有效的IP地址则返回true
     */
    public static boolean isValidIp(String ip) {
        try {
            InetAddress.getByName(ip);
            return true;
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 判断是否为内网IP地址
     *
     * @param ip IP地址
     * @return 如果是内网IP地址则返回true
     */
    public static boolean isInternalIp(String ip) {
        try {
            InetAddress inetAddress = InetAddress.getByName(ip);
            return inetAddress.isSiteLocalAddress() ||
                   inetAddress.isLoopbackAddress() ||
                   inetAddress.isLinkLocalAddress();
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 判断字符串是否为IP地址（IPv4或IPv6）
     * 该方法是isValidIp的别名，用于保持与其他代码的兼容性
     *
     * @param str 要检查的字符串
     * @return 如果是IP地址则返回true
     */
    public static boolean isIpAddress(String str) {
        return isValidIp(str);
    }
}
