package com.geeksec.common.utils.db;

import static com.geeksec.common.config.ConfigConstants.MYSQL_HOST;
import static com.geeksec.common.config.ConfigConstants.MYSQL_PASSWORD;
import static com.geeksec.common.config.ConfigConstants.MYSQL_USERNAME;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.flink.api.java.utils.ParameterTool;

import com.geeksec.common.config.ConfigurationManager;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据库连接管理器
 * 提供统一的数据库连接和查询执行功能
 *
 * <AUTHOR> Team
 */
@Slf4j
public class DatabaseConnectionManager {

    /**
     * 配置工具实例
     */
    private static final ParameterTool CONFIG = ConfigurationManager.getConfig();

    /**
     * MySQL数据库连接地址
     */
    private static final String HOST = CONFIG.get(MYSQL_HOST);

    /**
     * MySQL数据库用户名
     */
    private static final String USERNAME = CONFIG.get(MYSQL_USERNAME);

    /**
     * MySQL数据库密码
     */
    private static final String PASSWORD = CONFIG.get(MYSQL_PASSWORD);

    /**
     * 私有构造函数，防止实例化
     */
    private DatabaseConnectionManager() {
        // 私有构造函数，防止实例化
    }

    /**
     * 获取数据库连接
     *
     * @param database 数据库名称
     * @return 数据库连接
     */
    public static Connection getConnection(String database) {
        Connection conn = null;
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            String url = "jdbc:mysql://" + HOST + ":3306/" + database
                    + "?useUnicode=true&characterEncoding=utf-8&useSSL=false";
            conn = DriverManager.getConnection(url, USERNAME, PASSWORD);
        } catch (Exception e) {
            log.error("获取数据库连接失败", e);
        }
        return conn;
    }

    /**
     * 获取默认数据库连接
     *
     * @return 数据库连接
     * @throws Exception 如果获取连接失败
     */
    public static Connection getConnection() throws Exception {
        Class.forName("com.mysql.cj.jdbc.Driver").newInstance();
        return DriverManager.getConnection(HOST, USERNAME, PASSWORD);
    }

    /**
     * 关闭数据库连接
     *
     * @param conn 数据库连接
     * @param stmt Statement对象
     * @param rs   ResultSet对象
     */
    public static void closeConnection(Connection conn, Statement stmt, ResultSet rs) {
        try {
            if (rs != null) {
                rs.close();
            }
            if (stmt != null) {
                stmt.close();
            }
            if (conn != null) {
                conn.close();
            }
        } catch (SQLException e) {
            log.error("关闭数据库连接失败", e);
        }
    }

    /**
     * 执行查询SQL语句
     *
     * @param database 数据库名称
     * @param sql      SQL语句
     * @return 查询结果列表
     */
    public static List<Map<String, Object>> executeQuery(String database, String sql) {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List<Map<String, Object>> resultList = new ArrayList<>();

        try {
            conn = getConnection(database);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);

            while (rs.next()) {
                Map<String, Object> row = new HashMap<>();
                for (int i = 1; i <= rs.getMetaData().getColumnCount(); i++) {
                    String columnName = rs.getMetaData().getColumnName(i);
                    Object columnValue = rs.getObject(i);
                    row.put(columnName, columnValue);
                }
                resultList.add(row);
            }
        } catch (SQLException e) {
            log.error("执行查询SQL失败: {}", sql, e);
        } finally {
            closeConnection(conn, stmt, rs);
        }

        return resultList;
    }

    /**
     * 执行更新SQL语句（INSERT、UPDATE、DELETE）
     *
     * @param database 数据库名称
     * @param sql      SQL语句
     * @return 受影响的行数
     */
    public static int executeUpdate(String database, String sql) {
        Connection conn = null;
        Statement stmt = null;
        int result = 0;

        try {
            conn = getConnection(database);
            stmt = conn.createStatement();
            result = stmt.executeUpdate(sql);
        } catch (SQLException e) {
            log.error("执行更新SQL失败: {}", sql, e);
        } finally {
            closeConnection(conn, stmt, null);
        }

        return result;
    }
}
