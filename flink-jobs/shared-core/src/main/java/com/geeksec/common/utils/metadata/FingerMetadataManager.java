package com.geeksec.common.utils.metadata;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.geeksec.common.utils.db.DatabaseConnectionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * 指纹元数据管理器
 * 提供指纹相关元数据的获取和缓存功能
 *
 * <AUTHOR> Team
 */
@Slf4j
public class FingerMetadataManager {

    /**
     * 单例实例
     */
    private static volatile FingerMetadataManager instance = null;

    /**
     * 指纹类型映射
     */
    private final Map<String, String> fingerTypeMap = new ConcurrentHashMap<>();

    /**
     * 指纹JA3映射
     */
    private final Map<String, String> fingerJa3Map = new ConcurrentHashMap<>();

    /**
     * 私有构造函数
     */
    private FingerMetadataManager() {
        initialize();
    }

    /**
     * 获取单例实例
     *
     * @return FingerMetadataManager实例
     */
    public static FingerMetadataManager getInstance() {
        if (instance == null) {
            synchronized (FingerMetadataManager.class) {
                if (instance == null) {
                    instance = new FingerMetadataManager();
                }
            }
        }
        return instance;
    }

    /**
     * 初始化元数据
     */
    private void initialize() {
        try {
            // 初始化时不加载数据，等到需要时再加载
            log.info("指纹元数据管理器初始化成功");
        } catch (Exception e) {
            log.error("初始化指纹元数据管理器失败", e);
        }
    }

    /**
     * 加载指纹信息
     *
     * @return 指纹信息列表
     */
    public List<FingerInfo> loadFingerInfo() {
        List<FingerInfo> resultList = new ArrayList<>();
        
        try (Connection conn = DatabaseConnectionManager.getConnection();
             Statement stmt = conn.createStatement()) {
            
            String sql = "SELECT finger_es, finger_content, ja3_hash, es_type, finger_type FROM tb_finger_info";
            
            try (ResultSet rs = stmt.executeQuery(sql)) {
                while (rs.next()) {
                    FingerInfo info = new FingerInfo();
                    info.setFingerId(rs.getString(1));
                    info.setFingerContent(rs.getString(2));
                    info.setFingerJa3(rs.getString(3));
                    info.setFingerDirection(rs.getString(4));
                    info.setFingerType(rs.getString(5));
                    
                    // 更新缓存
                    fingerTypeMap.put(info.getFingerId(), info.getFingerType());
                    fingerJa3Map.put(info.getFingerId(), info.getFingerJa3());
                    
                    resultList.add(info);
                }
            }
            
            log.info("指纹信息加载成功，共加载 {} 条记录", resultList.size());
            return resultList;
        } catch (Exception e) {
            log.error("加载指纹信息失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取指纹类型映射
     *
     * @return 指纹类型映射
     */
    public Map<String, String> getFingerTypeMap() {
        if (fingerTypeMap.isEmpty()) {
            loadFingerInfo();
        }
        
        return Collections.unmodifiableMap(fingerTypeMap);
    }

    /**
     * 获取指纹JA3映射
     *
     * @return 指纹JA3映射
     */
    public Map<String, String> getFingerJa3Map() {
        if (fingerJa3Map.isEmpty()) {
            loadFingerInfo();
        }
        
        return Collections.unmodifiableMap(fingerJa3Map);
    }

    /**
     * 获取指纹类型
     *
     * @param fingerId 指纹ID
     * @return 指纹类型，如果不存在则返回null
     */
    public String getFingerType(String fingerId) {
        if (fingerTypeMap.isEmpty()) {
            loadFingerInfo();
        }
        
        return fingerTypeMap.get(fingerId);
    }

    /**
     * 获取指纹JA3
     *
     * @param fingerId 指纹ID
     * @return 指纹JA3，如果不存在则返回null
     */
    public String getFingerJa3(String fingerId) {
        if (fingerJa3Map.isEmpty()) {
            loadFingerInfo();
        }
        
        return fingerJa3Map.get(fingerId);
    }

    /**
     * 刷新指纹元数据缓存
     */
    public void refresh() {
        fingerTypeMap.clear();
        fingerJa3Map.clear();
        loadFingerInfo();
    }

    /**
     * 指纹信息类
     */
    public static class FingerInfo {
        private String fingerId;
        private String fingerContent;
        private String fingerJa3;
        private String fingerDirection;
        private String fingerType;

        public String getFingerId() {
            return fingerId;
        }

        public void setFingerId(String fingerId) {
            this.fingerId = fingerId;
        }

        public String getFingerContent() {
            return fingerContent;
        }

        public void setFingerContent(String fingerContent) {
            this.fingerContent = fingerContent;
        }

        public String getFingerJa3() {
            return fingerJa3;
        }

        public void setFingerJa3(String fingerJa3) {
            this.fingerJa3 = fingerJa3;
        }

        public String getFingerDirection() {
            return fingerDirection;
        }

        public void setFingerDirection(String fingerDirection) {
            this.fingerDirection = fingerDirection;
        }

        public String getFingerType() {
            return fingerType;
        }

        public void setFingerType(String fingerType) {
            this.fingerType = fingerType;
        }
    }
}
