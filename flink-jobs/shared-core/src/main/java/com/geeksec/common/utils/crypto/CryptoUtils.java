package com.geeksec.common.utils.crypto;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import lombok.extern.slf4j.Slf4j;

/**
 * 加密工具类，提供常用的加密、解密、编码和解码功能
 * 
 * <AUTHOR>
 */
@Slf4j
public class CryptoUtils {

    static {
        // 注册BouncyCastle提供者
        java.security.Security.addProvider(new BouncyCastleProvider());
    }

    private CryptoUtils() {
        // 私有构造函数，防止实例化
    }

    /**
     * 将字节数组编码为Base64字符串
     *
     * @param data 要编码的字节数组
     * @return Base64编码的字符串，如果输入为null则返回null
     */
    public static String encodeBase64(byte[] data) {
        if (data == null) {
            return null;
        }
        return Base64.getEncoder().encodeToString(data);
    }

    /**
     * 将Base64字符串解码为字节数组
     *
     * @param base64String Base64编码的字符串
     * @return 解码后的字节数组，如果输入为null或空字符串则返回null
     */
    public static byte[] decodeBase64(String base64String) {
        if (StringUtils.isBlank(base64String)) {
            return null;
        }
        return Base64.getDecoder().decode(base64String);
    }

    /**
     * 计算字符串的MD5哈希值
     *
     * @param input 输入字符串
     * @return MD5哈希值的十六进制字符串表示，如果输入为null则返回null
     */
    public static String md5(String input) {
        return hash(input, "MD5");
    }

    /**
     * 计算字符串的SHA-256哈希值
     *
     * @param input 输入字符串
     * @return SHA-256哈希值的十六进制字符串表示，如果输入为null则返回null
     */
    public static String sha256(String input) {
        return hash(input, "SHA-256");
    }

    /**
     * 计算字符串的SHA-512哈希值
     *
     * @param input 输入字符串
     * @return SHA-512哈希值的十六进制字符串表示，如果输入为null则返回null
     */
    public static String sha512(String input) {
        return hash(input, "SHA-512");
    }

    /**
     * 使用指定算法计算字符串的哈希值
     *
     * @param input 输入字符串
     * @param algorithm 哈希算法（如MD5、SHA-256等）
     * @return 哈希值的十六进制字符串表示，如果输入为null或算法不存在则返回null
     */
    public static String hash(String input, String algorithm) {
        if (input == null) {
            return null;
        }
        try {
            MessageDigest digest = MessageDigest.getInstance(algorithm);
            byte[] hashBytes = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            log.error("哈希算法不存在: {}", algorithm, e);
            return null;
        }
    }

    /**
     * 使用AES算法加密字符串
     *
     * @param plainText 明文字符串
     * @param key 密钥（16字节）
     * @param iv 初始化向量（16字节）
     * @return Base64编码的密文，如果输入为null或加密失败则返回null
     */
    public static String encryptAES(String plainText, byte[] key, byte[] iv) {
        if (plainText == null || key == null || iv == null) {
            return null;
        }
        try {
            SecretKeySpec secretKey = new SecretKeySpec(key, "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivSpec);
            byte[] encrypted = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            return encodeBase64(encrypted);
        } catch (Exception e) {
            log.error("AES加密失败", e);
            return null;
        }
    }

    /**
     * 使用AES算法解密字符串
     *
     * @param encryptedText Base64编码的密文
     * @param key 密钥（16字节）
     * @param iv 初始化向量（16字节）
     * @return 解密后的明文，如果输入为null或解密失败则返回null
     */
    public static String decryptAES(String encryptedText, byte[] key, byte[] iv) {
        if (encryptedText == null || key == null || iv == null) {
            return null;
        }
        try {
            byte[] encrypted = decodeBase64(encryptedText);
            SecretKeySpec secretKey = new SecretKeySpec(key, "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, secretKey, ivSpec);
            byte[] decrypted = cipher.doFinal(encrypted);
            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("AES解密失败", e);
            return null;
        }
    }

    /**
     * 生成AES密钥
     *
     * @param keySize 密钥大小（128、192或256位）
     * @return 生成的密钥字节数组
     * @throws NoSuchAlgorithmException 如果AES算法不可用
     */
    public static byte[] generateAESKey(int keySize) throws NoSuchAlgorithmException {
        KeyGenerator keyGen = KeyGenerator.getInstance("AES");
        keyGen.init(keySize, new SecureRandom());
        SecretKey secretKey = keyGen.generateKey();
        return secretKey.getEncoded();
    }

    /**
     * 生成随机初始化向量
     *
     * @param size 向量大小（通常为16字节）
     * @return 随机生成的初始化向量
     */
    public static byte[] generateIV(int size) {
        byte[] iv = new byte[size];
        new SecureRandom().nextBytes(iv);
        return iv;
    }

    /**
     * 将字节数组转换为十六进制字符串
     *
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    public static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder(2 * bytes.length);
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

    /**
     * 将十六进制字符串转换为字节数组
     *
     * @param hexString 十六进制字符串
     * @return 字节数组
     */
    public static byte[] hexToBytes(String hexString) {
        if (hexString == null || hexString.length() % 2 != 0) {
            throw new IllegalArgumentException("无效的十六进制字符串");
        }
        int len = hexString.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4)
                    + Character.digit(hexString.charAt(i + 1), 16));
        }
        return data;
    }
}
