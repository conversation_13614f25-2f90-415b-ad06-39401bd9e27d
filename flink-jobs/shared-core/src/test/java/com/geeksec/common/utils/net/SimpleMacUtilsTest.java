package com.geeksec.common.utils.net;

import java.util.Map;

/**
 * 简单的MAC地址工具类测试
 * 
 * <AUTHOR>
 */
public class SimpleMacUtilsTest {

    public static void main(String[] args) {
        System.out.println("=== MAC地址工具类测试 ===");
        
        // 测试MAC地址验证
        testMacValidation();
        
        // 测试MAC地址标准化
        testMacNormalization();
        
        // 测试MAC地址详细信息
        testMacInfo();
        
        // 测试地址类型分析
        testAddressTypeAnalysis();
        
        System.out.println("=== 测试完成 ===");
    }
    
    private static void testMacValidation() {
        System.out.println("\n--- MAC地址验证测试 ---");
        
        String[] validMacs = {
            "AA:BB:CC:DD:EE:FF",
            "aa:bb:cc:dd:ee:ff", 
            "AA-BB-CC-DD-EE-FF",
            "AABBCCDDEEFF",
            "AA.BB.CC.DD.EE.FF"
        };
        
        String[] invalidMacs = {
            "invalid",
            "AA:BB:CC:DD:EE",
            "GG:BB:CC:DD:EE:FF",
            ""
        };
        
        for (String mac : validMacs) {
            boolean isValid = MacUtils.isValidMac(mac);
            System.out.println(mac + " -> " + (isValid ? "有效" : "无效"));
        }
        
        for (String mac : invalidMacs) {
            boolean isValid = MacUtils.isValidMac(mac);
            System.out.println(mac + " -> " + (isValid ? "有效" : "无效"));
        }
    }
    
    private static void testMacNormalization() {
        System.out.println("\n--- MAC地址标准化测试 ---");
        
        String[] testMacs = {
            "aa:bb:cc:dd:ee:ff",
            "AA-BB-CC-DD-EE-FF", 
            "AABBCCDDEEFF",
            "AA.BB.CC.DD.EE.FF"
        };
        
        for (String mac : testMacs) {
            String normalized = MacUtils.normalizeMac(mac);
            System.out.println(mac + " -> " + normalized);
        }
    }
    
    private static void testMacInfo() {
        System.out.println("\n--- MAC地址详细信息测试 ---");
        
        String testMac = "F0:EE:7A:12:34:56";
        Map<String, Object> macInfo = MacUtils.getMacInfo(testMac);
        
        System.out.println("MAC地址: " + testMac);
        System.out.println("详细信息:");
        for (Map.Entry<String, Object> entry : macInfo.entrySet()) {
            System.out.println("  " + entry.getKey() + ": " + entry.getValue());
        }
    }
    
    private static void testAddressTypeAnalysis() {
        System.out.println("\n--- 地址类型分析测试 ---");
        
        // 测试本地管理地址
        String localMac = "02:00:00:12:34:56";
        System.out.println(localMac + " 是否为本地管理地址: " + MacUtils.isLocallyAdministered(localMac));
        
        // 测试组播地址
        String multicastMac = "01:00:00:12:34:56";
        System.out.println(multicastMac + " 是否为组播地址: " + MacUtils.isMulticast(multicastMac));
        
        // 测试全球唯一地址
        String globalMac = "00:00:00:12:34:56";
        System.out.println(globalMac + " 是否为本地管理地址: " + MacUtils.isLocallyAdministered(globalMac));
        System.out.println(globalMac + " 是否为组播地址: " + MacUtils.isMulticast(globalMac));
        
        // 测试虚拟化厂商
        System.out.println("VMware 是否为虚拟化厂商: " + MacUtils.isVirtualizedVendor("VMware, Inc."));
        System.out.println("Apple 是否为虚拟化厂商: " + MacUtils.isVirtualizedVendor("Apple, Inc."));
    }
}
