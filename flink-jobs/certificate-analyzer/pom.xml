<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.geeksec</groupId>
        <artifactId>flink-jobs</artifactId>
        <version>3.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>certificate-analyzer</artifactId>
    <packaging>jar</packaging>

    <properties>
        <main.class>com.geeksec.nta.pipeline.CertificateAnalysisPipeline</main.class>
    </properties>

    <dependencies>
        <!-- shared-core module -->
        <dependency>
            <groupId>com.geeksec</groupId>
            <artifactId>shared-core</artifactId>
        </dependency>

        <!-- 证书解析相关依赖 -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
        </dependency>

        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk15on</artifactId>
        </dependency>

        <dependency>
            <groupId>org.certificate-transparency</groupId>
            <artifactId>ctlog</artifactId>
        </dependency>

        <!-- 字符集检测和处理 -->
        <dependency>
            <groupId>net.sourceforge.jchardet</groupId>
            <artifactId>jchardet</artifactId>
        </dependency>

        <!-- 国际化支持 -->
        <dependency>
            <groupId>com.ibm.icu</groupId>
            <artifactId>icu4j</artifactId>
        </dependency>

        <!-- 序列化相关 -->
        <dependency>
            <groupId>com.esotericsoftware</groupId>
            <artifactId>kryo</artifactId>
        </dependency>

        <!-- Apache Thrift相关 -->
        <dependency>
            <groupId>com.twitter</groupId>
            <artifactId>chill-thrift</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.thrift</groupId>
            <artifactId>libthrift</artifactId>
        </dependency>

        <!-- HTTP客户端 -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>

        <!-- Protobuf工具类 -->
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java-util</artifactId>
        </dependency>

        <!-- NLP工具 -->
        <dependency>
            <groupId>org.apache.opennlp</groupId>
            <artifactId>opennlp-tools</artifactId>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- S3 文件系统依赖 -->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-s3-fs-hadoop</artifactId>
        </dependency>

        <!-- Hadoop AWS 依赖，用于S3兼容存储 -->
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-aws</artifactId>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>Apache OpenNLP Snapshots</id>
            <url>https://repository.apache.org/content/repositories/snapshots/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <configuration>
                    <images>
                        <image>
                            <name>${docker.registry}/${docker.image.prefix}/${project.artifactId}:${project.version}</name>
                            <build>
                                <contextDir>${project.basedir}</contextDir>
                                <dockerFile>${project.basedir}/Dockerfile</dockerFile>
                                <args>
                                    <JAR_FILE>target/${project.build.finalName}.jar</JAR_FILE>
                                    <FLINK_VERSION>${flink.version}</FLINK_VERSION>
                                </args>
                            </build>
                        </image>
                    </images>
                </configuration>
                <executions>
                    <execution>
                        <id>build-image</id>
                        <phase>package</phase>
                        <goals>
                            <goal>build</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>push-image</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>push</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
