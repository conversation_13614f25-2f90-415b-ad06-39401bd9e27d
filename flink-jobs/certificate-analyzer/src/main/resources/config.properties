# Kafka配置
kafka.bootstrap.servers=kafka:9094
kafka.group.id=analysis_cert_01
kafka.topic=certfile
kafka.auto.offset.reset.mode=latest

# Elasticsearch配置
elasticsearch.host=elasticsearch
elasticsearch.port=9200
elasticsearch.index.user=cert_user
elasticsearch.index.system=cert_system

# Redis配置
redis.host=redis
redis.port=6379
redis.timeout=10000
redis.pool.max=50
redis.expire.time=86400

# MySQL配置
mysql.database.host=**********************************************************************************************************
mysql.analysis.host=**************************************************************************************************************
mysql.database.user=root
mysql.database.password=simpleuse23306p


# Nebula Graph配置
nebula.meta.addr=host.docker.internal:9559
nebula.graph.addr=host.docker.internal:9669
nebula.graph.host=host.docker.internal
nebula.space.name=cert_analysis_graph
nebula.graph.port=9669
nebula.graph.username=root
nebula.graph.password=nebula
nebula.pool.max.size=200
nebula.pool.min.size=50
nebula.pool.idle.time=180000
nebula.pool.timeout=300000
nebula.session.size=200

# MinIO配置
minio.endpoint=http://minio:9000
minio.access.key=minioadmin
minio.secret.key=minioadmin
minio.bucket.name=certificates
minio.bucket.region=us-east-1

# 并行度配置
parallelism.kafka.source=4
parallelism.parsing=16
parallelism.elasticsearch.sink=8
