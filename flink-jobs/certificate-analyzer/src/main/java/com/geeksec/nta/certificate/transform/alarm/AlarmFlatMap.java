package com.geeksec.nta.certificate.transform.alarm;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysisFunction.writeAlarm.*;
import com.geeksec.utils.FileUtil;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2022/12/30
 */

public class AlarmFlatMap extends RichFlatMapFunction<Row, JSONObject> {

    private static final Logger logger = LoggerFactory.getLogger(AlarmFlatMap.class);

    public static Map<String,Map<String,String>> Alarm_Info_Map = new HashMap<>();

    @Override
    public void open(Configuration parameters) throws Exception {
        InputStream Alarm_stream = this.getClass().getClassLoader().getResourceAsStream("gk_alarm.csv");
        BufferedReader Alarm_buffer = new BufferedReader(new InputStreamReader(Alarm_stream));
        //加载配置文件
        try {
            Alarm_Info_Map = FileUtil.load_Alarm_Map(Alarm_buffer);
        } catch (Exception e) {
            throw new IOException("file load fail ==>", e);
        }
        super.open(parameters);
    }

    @Override
    public void flatMap(Row row, Collector<JSONObject> collector) throws Exception {
        String alarmType = row.getFieldAs(0);
        JSONObject sendJson;
        switch (alarmType){
            case "APT证书碰撞":
            case "威胁证书碰撞":
            case "失陷IP关联证书":
            case "恶意域名关联证书":
            case "APT证书上线":
            case "翻墙行为":
            case "TOR网络访问":
            case "C2证书请求":
            case "非法挖矿请求":
                sendJson = writeCertKnowledgeAlarm.getUseCertAlarmJson(row);
                collector.collect(sendJson);
                break;
            default:
                logger.error("告警类型出错");
                break;
        }
    }
}
