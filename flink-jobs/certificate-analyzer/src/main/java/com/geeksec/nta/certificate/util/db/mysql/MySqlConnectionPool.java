package com.geeksec.nta.certificate.util.db.mysql;

import com.geeksec.nta.certificate.util.FileUtil;
import com.mysql.cj.jdbc.MysqlDataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Properties;
import javax.sql.DataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * MySQL数据库连接池，用于管理数据库连接
 *
 * <AUTHOR>
 * @date 2023/3/17
 */
public class MySqlConnectionPool {
    private static final Logger LOGGER = LoggerFactory.getLogger(MySqlConnectionPool.class);
    private static final Properties PROPERTIES = FileUtil.getProperties("/config.properties");

    private static final String MYSQL_HOST = PROPERTIES.getOrDefault("mysql.database.host", "").toString();
    private static final String MYSQL_HOST_TH = PROPERTIES.getOrDefault("mysql.analysis.host", "").toString();
    private static final String MYSQL_USER = PROPERTIES.getOrDefault("mysql.database.user", "").toString();
    private static final String MYSQL_PASSWORD = PROPERTIES.getOrDefault("mysql.database.password", "").toString();
    
    private static DataSource dataSource;
    private static DataSource dataSourceTH;

    // 初始化主数据源
    static {
        MysqlDataSource mysqlDataSource = new MysqlDataSource();
        mysqlDataSource.setURL(MYSQL_HOST);
        mysqlDataSource.setUser(MYSQL_USER);
        mysqlDataSource.setPassword(MYSQL_PASSWORD);
        dataSource = mysqlDataSource;
        LOGGER.info("初始化主数据库连接池成功");
    }

    // 初始化分析数据源
    static {
        MysqlDataSource mysqlDataSource = new MysqlDataSource();
        mysqlDataSource.setURL(MYSQL_HOST_TH);
        mysqlDataSource.setUser(MYSQL_USER);
        mysqlDataSource.setPassword(MYSQL_PASSWORD);
        dataSourceTH = mysqlDataSource;
        LOGGER.info("初始化分析数据库连接池成功");
    }

    /**
     * 获取主数据库连接
     *
     * @return 数据库连接
     * @throws SQLException 如果获取连接失败
     */
    public static Connection getConnection() throws SQLException {
        return dataSource.getConnection();
    }

    /**
     * 获取分析数据库连接
     *
     * @return 数据库连接
     * @throws SQLException 如果获取连接失败
     */
    public static Connection getAnalysisConnection() throws SQLException {
        return dataSourceTH.getConnection();
    }
}
