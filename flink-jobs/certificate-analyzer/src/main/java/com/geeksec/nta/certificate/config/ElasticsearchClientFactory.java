package com.geeksec.nta.certificate.config;


import com.geeksec.nta.certificate.util.FileUtil;
import java.util.Properties;
import org.apache.commons.pool.BasePoolableObjectFactory;
import org.apache.http.HttpHost;
import org.apache.http.client.config.RequestConfig;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Elasticsearch客户端工厂类，用于创建和管理Elasticsearch客户端连接
 *
 * <AUTHOR>
 * @Date 2022/10/21
 */

public class ElasticsearchClientFactory extends BasePoolableObjectFactory<RestHighLevelClient> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ElasticsearchClientFactory.class);

    private static final Properties PROPERTIES = FileUtil.getProperties("/config.properties");
    public static final String ES_HOST = PROPERTIES.getOrDefault("cert.cluster.elasticsearch.host", "localhost").toString();
    public static final int ES_PORT = Integer.parseInt(PROPERTIES.getOrDefault("cert.cluster.elasticsearch.port", "9200").toString());


    @Override
    public RestHighLevelClient makeObject(){
        RestHighLevelClient client = null;
        try{
            HttpHost httpHosts = new HttpHost(ES_HOST, ES_PORT, "http");
            RestClientBuilder builder = RestClient.builder(httpHosts).setRequestConfigCallback(new RestClientBuilder.RequestConfigCallback() {
                @Override
                public RequestConfig.Builder customizeRequestConfig(RequestConfig.Builder requestConfigBuilder) {
                    requestConfigBuilder.setConnectTimeout(500000);
                    requestConfigBuilder.setSocketTimeout(400000);
                    requestConfigBuilder.setConnectionRequestTimeout(1000000);
                    return requestConfigBuilder;
                }
            });
            client = new RestHighLevelClient(builder);
        } catch (Exception e) {
            LOGGER.error("Failed to create Elasticsearch client", e);
        }
        return client;
    }

    @Override
    public boolean validateObject(RestHighLevelClient restHighLevelClient) {
        return true;
    }

    @Override
    public void activateObject(RestHighLevelClient restHighLevelClient) throws Exception {
        // 对象被激活时的操作
    }

    @Override
    public void destroyObject(RestHighLevelClient restHighLevelClient) throws Exception {
        // 关闭客户端连接
        if (restHighLevelClient != null) {
            try {
                restHighLevelClient.close();
            } catch (Exception e) {
                LOGGER.warn("Error closing Elasticsearch client", e);
            }
        }
    }

    @Override
    public void passivateObject(RestHighLevelClient restHighLevelClient) throws Exception {
        // 对象被钝化时的操作
    }
}
