package com.geeksec.nta.certificate.io.sink.minio;

import com.geeksec.nta.certificate.model.cert.X509Cert;
import com.geeksec.nta.certificate.util.FileUtil;
import org.apache.flink.streaming.api.functions.sink.filesystem.StreamingFileSink;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

/**
 * MinIO FileSink工厂类，用于创建连接到MinIO的FileSink
 *
 * <AUTHOR>
 */
public class MinioFileSinkFactory {
    private static final Logger logger = LoggerFactory.getLogger(MinioFileSinkFactory.class);

    // S3协议前缀
    private static final String S3_PROTOCOL = "s3://";

    // 配置属性
    private static final Properties properties = FileUtil.getProperties("/config.properties");

    // MinIO配置
    private static final String MINIO_ENDPOINT = properties.getProperty("minio.endpoint", "http://minio:9000");
    private static final String MINIO_ACCESS_KEY = properties.getProperty("minio.access.key", "minioadmin");
    private static final String MINIO_SECRET_KEY = properties.getProperty("minio.secret.key", "minioadmin");
    private static final String MINIO_BUCKET = properties.getProperty("minio.bucket.name", "certificates");

    /**
     * 创建证书的FileSink
     *
     * @return 配置好的StreamingFileSink
     */
    public static StreamingFileSink<X509Cert> createCertificateSink() {
        // 设置S3系统属性
        setS3Properties();

        // 构建S3路径
        String s3Path = S3_PROTOCOL + MINIO_BUCKET + "/certificates";
        logger.info("Creating certificate FileSink with path: {}", s3Path);

        return CertificateSerializer.createCertificateSink(s3Path);
    }

    /**
     * 设置S3系统属性，用于连接MinIO
     */
    private static void setS3Properties() {
        // 设置S3端点
        System.setProperty("s3.endpoint", MINIO_ENDPOINT);
        // 设置S3路径样式访问（MinIO需要）
        System.setProperty("s3.path.style.access", "true");
        // 设置S3凭证
        System.setProperty("s3.access.key", MINIO_ACCESS_KEY);
        System.setProperty("s3.secret.key", MINIO_SECRET_KEY);

        // 设置Hadoop AWS配置
        System.setProperty("fs.s3a.endpoint", MINIO_ENDPOINT);
        System.setProperty("fs.s3a.path.style.access", "true");
        System.setProperty("fs.s3a.access.key", MINIO_ACCESS_KEY);
        System.setProperty("fs.s3a.secret.key", MINIO_SECRET_KEY);

        logger.info("S3 properties set for MinIO endpoint: {}", MINIO_ENDPOINT);
    }
}
