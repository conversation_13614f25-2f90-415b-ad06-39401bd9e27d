package com.geeksec.nta.certificate.common;

import java.util.*;
import java.util.stream.Collectors;
import org.apache.commons.codec.digest.DigestUtils;

/**
 * 证书字段排序工具类
 *
 * 针对证书提取过程中subject，issuer内部字段乱序的统一.
 * 首先检查两个字符串是否都在NAME_LIST_ORDER中，如果是，则按照索引顺序进行比较。
 * 如果只有一个在NAME_LIST_ORDER中，则它排在前面。
 * 如果两个都不在NAME_LIST_ORDER中，则认为是OID或未知字符串，通过compareOidsOrUnknown方法来比较它们。
 * 在compareOidsOrUnknown方法中，首先检查两个字符串是否都是数字OID。
 * 如果一个是数字OID而另一个不是，则数字OID排在前面。
 * 如果两个都不是数字OID，则按照字典序比较。
 * 如果两个都是数字OID，则通过compareNumericOids方法进行比较。
 *
 * <AUTHOR>
 */
public class NameOidSort {

    /**
     * 证书字段名称的标准排序顺序
     */
    private static final List<String> NAME_LIST_ORDER = Arrays.asList(
            "CN", "C", "L", "ST", "STREET_ADDRESS", "O", "OU", "SERIAL_NUMBER",
            "SURNAME", "GIVEN_NAME", "TITLE", "GENERATION_QUALIFIER", "X500_UNIQUE_IDENTIFIER", "DN_QUALIFIER", "PSEUDONYM", "USER_ID",
            "DOMAIN_COMPONENT", "EMAIL_ADDRESS", "JURISDICTION_COUNTRY_NAME", "JURISDICTION_LOCALITY_NAME", "JURISDICTION_STATE_OR_PROVINCE_NAME",
            "BUSINESS_CATEGORY", "POSTAL_ADDRESS", "POSTAL_CODE", "INN", "OGRN", "SNILS", "UNSTRUCTURED_NAME"
    );

    /**
     * 按照自定义顺序对Map进行排序并转换为字符串
     *
     * @param map 需要排序的Map
     * @return 排序后的字符串表示
     */
    public static String sortMapByKey(Map<String, String> map) {
        Comparator<String> customComparator = (o1, o2) -> {
            int index1 = NAME_LIST_ORDER.indexOf(o1);
            int index2 = NAME_LIST_ORDER.indexOf(o2);

            if (index1 != -1 && index2 != -1) {
                return Integer.compare(index1, index2);
            } else if (index1 != -1) {
                return -1;
            } else if (index2 != -1) {
                return 1;
            } else {
                // Both are OIDs or unknown, handle them
                return compareOidsOrUnknown(o1, o2);
            }
        };

        return map.entrySet().stream()
                .sorted(Map.Entry.comparingByKey(customComparator))
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining(", "));
    }

    /**
     * 比较两个OID或未知字符串
     *
     * @param oid1 第一个OID或字符串
     * @param oid2 第二个OID或字符串
     * @return 比较结果
     */
    private static int compareOidsOrUnknown(String oid1, String oid2) {
        String[] parts1 = oid1.split("\\.");
        String[] parts2 = oid2.split("\\.");

        // Compare the number of parts
        if (parts1.length != parts2.length) {
            return Integer.compare(parts1.length, parts2.length);
        }

        // Compare each part
        for (int i = 0; i < parts1.length; i++) {
            // If the part is numeric, compare as integers, otherwise compare as strings
            try {
                int partCompare = Integer.compare(Integer.parseInt(parts1[i]), Integer.parseInt(parts2[i]));
                if (partCompare != 0) {
                    return partCompare;
                }
            } catch (NumberFormatException e) {
                return parts1[i].compareTo(parts2[i]);
            }
        }

        // OIDs are equal
        return 0;
    }

    /**
     * 计算排序后字符串的MD5哈希值
     *
     * @param map 需要排序的Map
     * @return 排序后字符串的MD5哈希值
     */
    public static String getMD5ForSortedMap(Map<String, String> map) {
        String sortedString = sortMapByKey(map);
        return DigestUtils.md5Hex(sortedString);
    }
}