package com.geeksec.nta.certificate.model.cert;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.annotation.JSONField;
import com.geeksec.nta.certificate.model.UncommonOID;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/10/26
 */
@Data
public class X509Cert {
    private byte[] cert;

    @JSONField(name = "CertID")
    private String certId;

    @JSONField(name = "Subject")
    private LinkedHashMap<String, String> subject = null;

    @JSONField(name = "Version")
    private String version;

    @JSONField(name = "Issuer")
    private LinkedHashMap<String, String> issuer = null;

    @JSONField(name = "SubjectMD5")
    private String subjectMD5 = null;

    @JSONField(name = "IssuerMD5")
    private String issuerMD5 = null;

    @JSONField(name = "NotBefore")
    private String notBefore = null;

    @JSO<PERSON>ield(name = "NotAfter")
    private String notAfter = null;

    @JSO<PERSON>ield(name = "Duration")
    private Long duration = 0L;

    @JSONField(name = "SerialNumber")
    private String serialNumber = null;

    @JSONField(name = "PublicKey")
    private String publicKey = null;

    @JSONField(name = "SPKISHA256")
    private String SPKISHA256 = null;

    @JSONField(name = "PubAlgOid")
    private String PubAlgOid = null;

    @JSONField(name = "PubAlgParamOid")
    private String PubAlgParamOid = null;

    @JSONField(name = "PublicKeyAlgorithm")
    private String PublicKeyAlgorithm;

    @JSONField(name = "PublicKeyAlgorithmLength")
    private String PublicKeyAlgorithmLength;

    @JSONField(name = "PublicKeyAlgorithmParameter")
    private String PublicKeyAlgorithmParameter;

    @JSONField(name = "SignatureAlgorithm")
    private String sigAlgName = null;

    @JSONField(name = "SignatureAlgOid")
    private String sigAlgOid = null;

    @JSONField(name = "Signature")
    private String signature = null;

    @JSONField(name = "Usage")
    private String usage = null;

    @JSONField(name = "ThreatLevel")
    private String ThreatLevel = null;

    @JSONField(name = "SAN")
    private Object SAN = null;

    @JSONField(name = "CN")
    private Object CN = null;

    @JSONField(name = "IssuerCN")
    private Object IssuerCN = null;

    @JSONField(name = "PemMD5")
    private String PemMD5 = null;

    @JSONField(name = "PemSHA1")
    private String PemSHA1 = null;

    @JSONField(name = "PemSHA256")
    private String PemSHA256 = null;

    @JSONField(name = "ASN1MD5")
    private String ASN1MD5 = null;

    @JSONField(name = "ASN1SHA1")
    private String ASN1SHA1 = null;

    @JSONField(name = "ASN1SHA256")
    private String ASN1SHA256 = null;

    @JSONField(name = "Extension")
    private HashMap<String, String> extension;

    @JSONField(name = "Labels")
    private List<String> tagList = new ArrayList<>();

    @JSONField(name = "AssociateDomain")
    private List<String> AssociateDomain = new ArrayList<>();

    @JSONField(name = "AssociateIP")
    private List<String> AssociateIP = new ArrayList<>();

    @JSONField(name = "AssociateURL")
    private List<String> AssociateURL = new ArrayList<>();

    @JSONField(name = "UserIDList")
    private List<String> UserIDList = new ArrayList<>();

    @JSONField(name = "Format")
    private String format;

    @JSONField(name = "ParentCertIDList")
    private List<String> ParentCertIDList = new ArrayList<>();

    @JSONField(name = "ParentCertID")
    private String ParentCertID = "";

    @JSONField(name = "WhiteCert")
    private String WhiteCert;

    @JSONField(name = "BlackList")
    private int BlackList;

    @JSONField(name = "WhiteList")
    private int WhiteList;

    @JSONField(name = "Method")
    private Boolean method;

    @JSONField(name = "ParseStatus")
    private Boolean ParseStatus;

    @JSONField(name = "ImportTime")
    private Long ImportTime;

    @JSONField(name = "IsDedupCert")
    private Boolean IsDedupCert;

    @JSONField(name = "TaskId")
    private String TaskId = null;

    @JSONField(name = "BatchId")
    private String BatchId = null;

    @JSONField(name = "UserType")
    private String UserType;

    @JSONField(name = "BusinessType")
    private String BusinessType;

    @JSONField(name = "CAType")
    private String CAType;

    @JSONField(name = "IndustryType")
    private String IndustryType;

    @JSONField(name = "SubjectArea")
    private String SubjectArea;

    @JSONField(name = "IssuerArea")
    private String IssuerArea;

    @JSONField(name = "CertSource")
    private String CertSource;

    @JSONField(name = "IsError")
    private Boolean IsError;

    @JSONField(name = "PositiveHash")
    private LinkedList<String> PositiveHash;

    @JSONField(name = "NegativeHash")
    private LinkedList<String> NegativeHash;

    @JSONField(name = "correctASN1SHA1")
    private String correctASN1SHA1 = null;

    @JSONField(name = "company")
    private String company = "";

    @JSONField(name = "iocIP")
    private String iocIP = "";

    @JSONField(name = "iocDomain")
    private String iocDomain = "";

    @JSONField(name = "UncommonOIDs")
    private List<UncommonOID> UncommonOIDs = new ArrayList<>();

    @JSONField(name = "KnowledgeCollisionResult")
    private List<Map<String,List<String>>> KnowledgeCollisionResult = new ArrayList<>();

    public X509Cert(byte[] cert){
        this.cert = cert;
    }
}
