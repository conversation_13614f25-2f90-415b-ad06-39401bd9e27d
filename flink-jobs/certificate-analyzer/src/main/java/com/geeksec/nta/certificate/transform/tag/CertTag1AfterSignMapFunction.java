package com.geeksec.nta.certificate.transform.tag;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.common.utils.IpUtils;
import com.geeksec.utils.*;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixList;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixListFactory;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import opennlp.tools.postag.POSModel;
import opennlp.tools.postag.POSTaggerME;
import opennlp.tools.tokenize.TokenizerME;
import opennlp.tools.tokenize.TokenizerModel;
import org.apache.commons.pool.impl.GenericObjectPool;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import org.elasticsearch.client.RestHighLevelClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.JedisPool;

/**
 * <AUTHOR>
 * @Date 2023/8/30
 */

public class CertTag1AfterSignMapFunction extends RichMapFunction<X509Cert, X509Cert> {

    protected static final Logger LOG = LoggerFactory.getLogger(CertTag1AfterSignMapFunction.class);
    private static transient GenericObjectPool<RestHighLevelClient> EsPool = null;
    private static transient JedisPool jedisPool = null;
    public static TokenizerModel tokenModel;
    public static POSModel posModel;
    public static TokenizerME tokenizer;
    public static POSTaggerME tagger;
    public static HashSet<String> wordDictionary = new HashSet<>();
    public static HashMap<String, Integer> ALEX_TOP10K_MAP = new HashMap<>();
    public static List<String> ALEX_TOP10K_LIST = new ArrayList<>();
    private static PublicSuffixList suffixList = null;
    private static Set<String> OPENSSL_FIELD = new HashSet<>(Arrays.asList("C","ST","L","O","OU","CN","EMAIL_ADDRESS"));

    @Override
    public void open(Configuration parameters) throws Exception {
        // ES 初始化
        EsPool = EsUtils.initEsPool();
        LOG.info("生成EsPool成功! {}", EsPool.getNumIdle(),EsPool.hashCode());

        // redis池 初始化.
        jedisPool = RedisUtils.initJedisPool();
        LOG.info("生成jedisPool成功! {}", jedisPool.getNumIdle(),jedisPool.hashCode());
        super.open(parameters);

        InputStream tokenModelInputStream = this.getClass().getClassLoader().getResourceAsStream("opennlp-en-ud-ewt-tokens-1.0-1.9.3.bin");
        InputStream posModelInputStream = this.getClass().getClassLoader().getResourceAsStream("opennlp-en-ud-ewt-pos-1.0-1.9.3.bin");
        InputStream dictionaryInputStream = this.getClass().getClassLoader().getResourceAsStream("en_US-large.txt");

        //加载配置文件
        try {
            tokenModel = new TokenizerModel(tokenModelInputStream);
            posModel = new POSModel(posModelInputStream);

            tokenizer = new TokenizerME(tokenModel);
            tagger = new POSTaggerME(posModel);

            Scanner scanner = new Scanner(dictionaryInputStream);

            while (scanner.hasNextLine()) {
                String word = scanner.nextLine().trim();
                wordDictionary.add(word);
            }
        } catch (Exception e) {
            throw new IOException("file load fail ==>", e);
        }

        PublicSuffixListFactory factory = new PublicSuffixListFactory();
        Properties properties = factory.getDefaults();
        properties.setProperty(PublicSuffixListFactory.PROPERTY_LIST_FILE, "/effective_tld_names.dat");
        suffixList = factory.build();

        loadData();
    }

    /**
     * 加载ALEX依赖文件
     * @throws IOException 加载失败则抛出异常
     */
    private void loadData() throws IOException {
        try {
            ALEX_TOP10K_MAP = FileUtil.loadAlexa10KMap("/alexa_rank_10K_domain.csv");
            ALEX_TOP10K_LIST = FileUtil.loadAlexa10KList("/alexa_rank_10K_domain.csv");
        } catch (Exception e) {
            throw new IOException("file load fail ==>", e);
        }
    }

    @Override
    public void close() throws Exception {
        if (jedisPool != null) {
            jedisPool.close();
        }
        if (EsPool != null) {
            EsPool.close();
        }
        super.close();
    }

    @Override
    public X509Cert map(X509Cert x509Cert) throws Exception {

        getDanaBotCert(x509Cert);
        getStealcCert(x509Cert);
        getFakeHotDomainCert(x509Cert);
        getQuakbotCert(x509Cert);
        getOpenSSlSelfSignCert(x509Cert);

        return x509Cert;
    }

    private static void getOpenSSlSelfSignCert(X509Cert x509Cert) {
        List<String> tagList = x509Cert.getTagList();
        if(tagList.contains("Self Signed Cert")){
            HashMap subject = CertFormatUtil.objectToMap(x509Cert.getSubject());
            Set<String> subjectField = subject.keySet();
            if(OPENSSL_FIELD.equals(subjectField)){
                tagList.add("OpenSSL Signed");
                x509Cert.setTagList(tagList);
            }
        }
    }

    /**
     * Botnet中的 DanaBot 的检测
     * */
    private static void getDanaBotCert(X509Cert x509Cert) throws IOException {
        MultiWordNounChecker.DanaBotChecker(x509Cert,tokenizer,tagger,wordDictionary);
    }

    /**
     * Botnet中的 Quakbot 的检测
     * */
    private static void getQuakbotCert(X509Cert x509Cert) throws IOException {
        MultiWordNounChecker.QuakbotChecker(x509Cert,tokenizer,tagger,wordDictionary);
    }

    /**
     * Botnet中的 Stealc 的检测
     * */
    private static void getStealcCert(X509Cert x509Cert){
        HashMap subject = CertFormatUtil.objectToMap(x509Cert.getSubject());
        String subjectCN = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "CN", ""));
        HashMap issuer = CertFormatUtil.objectToMap(x509Cert.getIssuer());
        String issuerCN = String.valueOf(CertFormatUtil.readDataFromKeys(issuer, "CN", ""));
        if (
                IpUtils.isIpAddress(subjectCN) && IpUtils.isIpAddress(issuerCN) && subjectCN.equals(issuerCN) && subject.size()==1 && issuer.size()==1
        ){
            List<String> tagList = x509Cert.getTagList();
            tagList.add("Botnet Stealc Cert");
            x509Cert.setTagList(tagList);
        }
    }

    /**
     * 伪装合法域名检测 Fake Hot Domain，检测的所有的字段中的域名
     * 检查证书的关联域名：
     * 其中如果有域名模仿的是alexa的前10000的域名，但是不完全相同，计算LevenshteinDistance。
     * 如果算出匹配程度为1-5，则认为是伪装合法域名。
     * 先进行过滤，去掉那些带通配符的
     * */
    private static void getFakeHotDomainCert(X509Cert x509Cert){
        List<String> associateDomains = x509Cert.getAssociateDomain();

        List<String> testDomainList = new ArrayList<>();

        for(String associateDomain:associateDomains){
            // 检查域名是否以'*.'开头
            if (associateDomain != null && associateDomain.startsWith("*.")) {
                // 去掉开头的'*.'字符
                associateDomain = associateDomain.substring(2);
            }
            if (!ALEX_TOP10K_LIST.contains(associateDomain) && !testDomainList.contains(associateDomain)){
                testDomainList.add(associateDomain);
            }
        }

        for (String testDomain:testDomainList){
            boolean foundMatch = false;
            try{
                for (String domain:ALEX_TOP10K_MAP.keySet()){
                    int levenshteinDistance = LevenshteinDistance.calculateMinDistance(testDomain.getBytes(StandardCharsets.UTF_8),domain.getBytes(StandardCharsets.UTF_8));
                    if (levenshteinDistance<=(0.2*domain.length())){
                        List<String> tagList = x509Cert.getTagList();
                        tagList.add("Fake Hot Domain");
                        x509Cert.setTagList(tagList);
                        foundMatch = true;
                        break;
                    }
                }
            }catch (Exception e){
                LOG.info("subjectCN无法编码并计算levenshteinDistance");
            }
            if (foundMatch) {
                break;
            }
        }
    }
}
