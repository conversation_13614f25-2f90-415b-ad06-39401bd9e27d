package com.geeksec.nta.certificate.transform.knowledge;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;

import com.geeksec.flinkTool.sideOutputTag.AlarmOutPutTag;
import java.util.*;
import org.apache.commons.pool.impl.GenericObjectPool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
* <AUTHOR>
* @Date  2024/10/10
*/

public class KnowledgeCollisionMapFunction extends ProcessFunction<X509Cert, X509Cert> {

    private final static Logger logger = LoggerFactory.getLogger(KnowledgeCollisionMapFunction.class);


    public static final String APT_COLUMN = "apt_info";
    public static final String THREAT_COLUMN = "threat_info";
    public static final String APP_COLUMN = "app_property_info";
    public static final String SCAN_COLUMN = "scan_detection_info";
    public static final String TRUST_COLUMN = "trust";
    public static final String DOMAIN_COLUMN = "domain_info";
    public static final String COMPANY_COLUMN = "service_company_info";
    public static final String CRL_COLUMN = "crl";

    public static final String APT_KEY = "apt_name";
    public static final String THREAT_KEY = "threat_type";
    public static final String APP_KEY = "app_property";
    public static final String SCAN_KEY = "scan_detection";
    public static final String TRUST_ISSUER_KEY = "issuer";
    public static final String TRUST_BUILTIN_KEY = "built_in";
    public static final String ALEXA_KEY = "alexa_rank";
    public static final String WHOIS_KEY = "whois";
    public static final String SUFFIX_KEY = "suffix";
    public static final String COMPANY_KEY = "company_name";
    public static final String CRL_KEY = "issuer";

    public static final String APT_RESULT_KEY = "APT证书知识库碰撞结果";
    public static final String THREAT_RESULT_KEY = "威胁证书库知识库碰撞结果";
    public static final String APP_RESULT_KEY = "APP证书及资产库碰撞结果";
    public static final String SCAN_RESULT_KEY = "扫描探测库碰撞结果";
    public static final String TRUST_ISSUER_RESULT_KEY = "可信白名单证书知识库碰撞结果";
    public static final String TRUST_BUILTIN_RESULT_KEY = "可信内置证书知识库碰撞结果";
    public static final String ALEXA_RESULT_KEY = "域名信息库知识库Alexa关联信息碰撞结果";
    public static final String WHOIS_RESULT_KEY = "域名信息库知识库Whois关联信息碰撞结果";
    public static final String SUFFIX_RESULT_KEY = "域名信息库知识库suffix关联信息碰撞结果";
    public static final String COMPANY_RESULT_KEY = "证书服务商信息知识库碰撞结果";
    public static final String CRL_RESULT_KEY = "吊销证书知识库碰撞结果";

    @Override
    public void close() throws Exception {
        super.close();

    }


    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

    }

    @Override
    public void processElement(X509Cert x509Cert, ProcessFunction<X509Cert, X509Cert>.Context context, Collector<X509Cert> collector) throws Exception {

        String sha1 = x509Cert.getASN1SHA1();
        List<String> domainList = x509Cert.getAssociateDomain();
        String company = x509Cert.getCompany();

        List<Map<String,List<String>>> KnowledgeCollisionResult = new ArrayList<>();

        try{

            // 证书 SHA1 关联 APT 碰撞
            Map<String,List<String>> APTResultMap = new HashMap<>();
            APTResultMap.put(APT_RESULT_KEY, Arrays.asList(""));
            KnowledgeCollisionResult.add(APTResultMap);
            logger.info("成功碰撞APT证书知识库数据");

            // 证书 SHA1 关联 威胁 信息碰撞
            Map<String,List<String>> threatResultMap = new HashMap<>();
            threatResultMap.put(THREAT_RESULT_KEY, Arrays.asList(""));
            KnowledgeCollisionResult.add(threatResultMap);
            logger.info("成功碰撞威胁证书知识库数据");

            // 证书 SHA1 关联 app 信息碰撞
            Map<String,List<String>> appResultMap = new HashMap<>();
            appResultMap.put(APP_RESULT_KEY, Arrays.asList(""));
            KnowledgeCollisionResult.add(appResultMap);
            logger.info("成功碰撞APP证书知识库数据");

            // 证书 SHA1 关联 扫描探测 信息碰撞
            Map<String,List<String>> scanResultMap = new HashMap<>();
            scanResultMap.put(SCAN_RESULT_KEY, Arrays.asList(""));
            KnowledgeCollisionResult.add(scanResultMap);
            logger.info("成功碰撞扫描探测证书知识库数据");

            // 证书 SHA1 关联 可信白名单证书 信息碰撞
            Map<String,List<String>> issuerResultMap = new HashMap<>();
            issuerResultMap.put(TRUST_ISSUER_RESULT_KEY, Arrays.asList(""));
            KnowledgeCollisionResult.add(issuerResultMap);
            logger.info("成功碰撞可信白名单证书知识库数据");

            // 证书 SHA1 关联 可信内置证书 信息碰撞
            Map<String,List<String>> builtinResultMap = new HashMap<>();
            builtinResultMap.put(TRUST_BUILTIN_RESULT_KEY, Arrays.asList(""));
            KnowledgeCollisionResult.add(builtinResultMap);
            logger.info("成功碰撞可信内置证书知识库数据");

            for (String domain: domainList){
                // 证书关联 域名alexa 碰撞
                Map<String,List<String>> alexaResultMap = new HashMap<>();
                alexaResultMap.put(ALEXA_RESULT_KEY, Arrays.asList(""));
                KnowledgeCollisionResult.add(alexaResultMap);
                logger.info("成功碰撞Alexa域名信息知识库数据");

                // 证书关联 域名whois 碰撞
                Map<String,List<String>> whoisResultMap = new HashMap<>();
                whoisResultMap.put(WHOIS_RESULT_KEY, Arrays.asList(""));
                KnowledgeCollisionResult.add(whoisResultMap);
                logger.info("成功碰撞Whois域名知识库数据");

                // 证书关联 域名 suffix 碰撞
                Map<String,List<String>> suffixResultMap = new HashMap<>();
                suffixResultMap.put(SUFFIX_RESULT_KEY, Arrays.asList(""));
                KnowledgeCollisionResult.add(suffixResultMap);
                logger.info("成功碰撞suffix域名知识库数据");
            }

            // 证书关联 company 碰撞
            Map<String,List<String>> companyResultMap = new HashMap<>();
            companyResultMap.put(COMPANY_RESULT_KEY, Arrays.asList(""));
            KnowledgeCollisionResult.add(companyResultMap);
            logger.info("成功碰撞证书服务商知识库数据");

            // 更新 CRL 吊销证书知识库碰撞结果
            Map<String,List<String>> crlResultMap = new HashMap<>();
            crlResultMap.put(CRL_RESULT_KEY, Arrays.asList(""));
            KnowledgeCollisionResult.add(crlResultMap);
            logger.info("成功碰撞吊销证书知识库数据");
            // 添加吊销证书标签
            List<String> tagList = x509Cert.getTagList();
            tagList.add("Withdraw Cert");
            x509Cert.setTagList(tagList);

        }catch (Exception e){
            logger.error("hbase查询失败，报错：{}",e.toString());
        }finally{

        }

        // 更新证书的KnowledgeCollisionResult
        x509Cert.setKnowledgeCollisionResult(KnowledgeCollisionResult);

        // 如果有APT和威胁证书的碰撞结果，则产生告警
        for (Map<String,List<String>> result:KnowledgeCollisionResult){
            if (result.containsKey(APT_RESULT_KEY)){
                context.output(AlarmOutPutTag.Alarm_APT_CERT_KNOWLEDGE_ALARM,x509Cert);
            }
            if(result.containsKey(THREAT_RESULT_KEY)){
                context.output(AlarmOutPutTag.Alarm_THREAT_CERT_KNOWLEDGE_ALARM,x509Cert);
            }
        }

        collector.collect(x509Cert);
    }
}
