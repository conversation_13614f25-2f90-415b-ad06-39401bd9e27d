package com.geeksec.nta.certificate.transform.sign;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.dbConnect.minio.MinioClient;
import com.geeksec.flinkTool.sideOutputTag.signOutPutTag;
import com.geeksec.utils.CertFormatUtil;
import com.geeksec.utils.CertificateParser;
import com.geeksec.utils.EsUtils;
import com.geeksec.utils.FileUtil;
import com.geeksec.utils.MinioCertificateClient;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.*;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.pool.impl.GenericObjectPool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.elasticsearch.client.RestHighLevelClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2023/1/17
 */

public class Cert1SignSplit extends ProcessFunction<Row, Row> {
    protected static final Logger LOG = LoggerFactory.getLogger(Cert1SignSplit.class);
    private static transient GenericObjectPool<RestHighLevelClient> esPool = null;
    private final Properties properties = FileUtil.getProperties("/config.properties");
    private static MinioClient minioClient = null;

    public static String SYSTEM_CERT_INDEX = null;

    @Override
    public void open(Configuration parameters) throws Exception {
        LOG.info("Initialization of global parameters starts.");
        Map<String, String> globalParam = getRuntimeContext().getExecutionConfig().getGlobalJobParameters().toMap();

        // 系统证书Index
        SYSTEM_CERT_INDEX = globalParam.get("es.system.index");
        LOG.info("cert system index:[ {} ].", SYSTEM_CERT_INDEX);

        // ES 初始化
        esPool = EsUtils.initEsPool();
        LOG.info("生成EsPool成功! {}", esPool.getNumIdle(), esPool.hashCode());

        // 初始化 MinIO 客户端
        minioClient = new MinioClient();
        LOG.info("MinIO client initialized with endpoint: {}", minioClient.getEndpoint());
    }

    @Override
    public void close() throws Exception {
        if (esPool != null) {
            esPool.close();
        }
        super.close();
    }

    /**
     * 处理证书验签
     * 应保持x509子证书的parentList字段与parentListMap的key相匹配
     */
    @Override
    public void processElement(Row signRow, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
        X509Cert x509Cert = (X509Cert) signRow.getField(0);
        Map<String,X509Cert> parentListMap = (Map<String, X509Cert>) signRow.getField(1);
        List<String> parentList = x509Cert.getParentCertIDList();
        List<String> tagList = x509Cert.getTagList();
        X509Cert testCert = x509Cert;
        OutputTag<Row> signStop = signOutPutTag.stop_sign1;
        OutputTag<Row> signContinue = signOutPutTag.continue_sign1;

        // 使用MinIO替代LMDB
        comprehensiveCertSign(signRow, context, testCert, tagList, x509Cert, signStop, parentList,
                             parentListMap, signContinue, esPool);
    }

    /**
     * 综合证书验签方法
     *
     * @param signRow 验签的传递Row
     * @param context 用于输出侧边流
     * @param testCert 用于验签的证书
     * @param tagList 原证书的标签列表
     * @param x509Cert 原证书
     * @param signStop OutputTag停止验签标志
     * @param parentList 原证书的父证书列表
     * @param parentListMap 验签的传递父证书Map
     * @param signContinue OutputTag继续验签标志
     * @param EsPool 用于验签的ES连接池
     */
    public static void comprehensiveCertSign(Row signRow, ProcessFunction<Row, Row>.Context context,
                                             X509Cert testCert, List<String> tagList, X509Cert x509Cert,
                                             OutputTag<Row> signStop, List<String> parentList,
                                             Map<String, X509Cert> parentListMap, OutputTag<Row> signContinue,
                                             GenericObjectPool<RestHighLevelClient> EsPool) throws Exception {
        // 验证证书是否是签发链可信,在验签过程发现该证书是一个白证书
        // 直接合并父证书列表
        RestHighLevelClient esClient = null;
        try {
            esClient = EsUtils.getClient(EsPool);
            //如果当前证书在系统中存在且是一个签发链可信证书，打上白名单标签并返回。
            List<String> whiteParentList = findExistWhiteCACert(testCert,esClient);
            if (whiteParentList!=null) {
                updateTagList(tagList,"White CA Cert");
                x509Cert.setTagList(tagList);
                updateParentWithKnownWhiteCaCert(parentList,whiteParentList);
                x509Cert.setParentCertIDList(parentList);
                signRow.setField(0, x509Cert);
                context.output(signStop, signRow);
                return;
            }
        }catch (Exception e){
            LOG.error("获取EsClient失败，error--->{}",e.toString());
        }finally {
            if(esClient != null){
                EsUtils.returnClient(esClient,EsPool);
            }
        }

        //将现有证书转化为原始文件准备验签
        //初始化一个x509证书实例certificate,并把当前的证书cert中的byte信息拷贝到标准证书实例中。
        X509Certificate certificate;
        try {
            certificate = CertificateParser.parseCertificate(testCert.getCert());
        } catch (CertificateException e) {
            context.output(signStop, signRow);
            return;
        }

        /* 查父证书 */
        RestHighLevelClient esClient1 = null;
        // ES中父证书结果采用Map<SHA1,MAP>的结构，如果查询到多个父证书，则会有多个SAH1
        HashMap<String, Map> parentEsCertResult = null;
        try{
            esClient1 = EsUtils.getClient(EsPool);
            //获取父证书hashmap
            parentEsCertResult = getParentCert(testCert,esClient1);
        }catch (Exception e){
            LOG.error("获取EsClient失败，error--->{}",e.toString());
        }finally {
            if(esClient1 != null){
                EsUtils.returnClient(esClient1,EsPool);
            }
        }

        // 查ES返回父证书为空，如果是null说明ES查询问题，不打标
        if (parentEsCertResult == null){
            LOG.error("获取EsClient失败，查询ES父证书报错，无法查询，返回空");
            context.output(signStop, signRow);
            return;
        }

        // 查ES返回父证书为空列表，未查询到父证书
        if(parentEsCertResult.isEmpty()){
            updateTagList(tagList,"Lost CertList");
            x509Cert.setTagList(tagList);
            signRow.setField(0, x509Cert);
            LOG.debug("无法查询到父证书.");
            context.output(signStop, signRow);
            return;
        }

        // 判断ES中查询到的父证书的数量判断是否存在多证书链
        Set<String> parentSha1List = parentEsCertResult.keySet();
        if(parentSha1List.size()>1){
            updateTagList(tagList,"Multi CertList");
        }

        // 对每一个父证书进行验证,并验证合法性
        Boolean isFake = null;
        HashMap parentSuccessVerifyMap = new HashMap<>();
        for (String sha1 : parentSha1List) {
            if (getIllegal(testCert,(HashMap) parentEsCertResult.get(sha1))){
                updateTagList(tagList,"Illegal Cert");
                updateTagList(tagList,"Insecure Chain");
            }
            //从备用数据中找ES中重复的数据。找到了才进行验签
            HashMap parentEsMap = (HashMap) parentEsCertResult.get(sha1);
            if (!ObjectUtils.allNotNull(parentEsMap) || parentEsMap.isEmpty()) {
                continue;
            }

            String parentPublicKeyStr = parentEsMap.getOrDefault("PublicKey", "").toString();
            String parentPubAlg = parentEsMap.getOrDefault("PublicKeyAlgorithm", "").toString();
            if(parentPubAlg.contains(".") && parentPubAlg.contains("(")){
                parentPubAlg = parentEsMap.getOrDefault("PubAlgOid", "").toString();
            }
            PublicKey parentPublicKey = CertFormatUtil.getPublicKey(parentPublicKeyStr,parentPubAlg);
            if (parentPublicKey == null){
                LOG.error("父证书公钥解析异常，异常key：——{}——，异常算法：——{}——",parentPublicKeyStr,parentPubAlg);
                continue;
            }
            //以下三个异常存在一个便算伪造证书。
            //利用之前的证书的原始文件和父证书的原始文件进行验签
            Boolean fakeBoolean = isFakeCert(certificate, parentPublicKey);
            if(fakeBoolean==null){
                continue;
            }
            if (!fakeBoolean) {
                // 有一个验签通过则进入后续逻辑，确认父证书存在，更新证书链
                isFake = false;
                parentSuccessVerifyMap = parentEsMap;
                break;
            } else {
                isFake = true;
                parentSuccessVerifyMap = parentEsMap;
            }
        }

        // 父证书查了一圈isFake没有赋值，打上缺失证书链标签，可能原因，伪造证书公钥，知识库中无父证书
        if (isFake == null) {
            updateTagList(tagList,"Lost CertList");
            x509Cert.setTagList(tagList);
            signRow.setField(0, x509Cert);
            context.output(signStop, signRow);
            return;
        }

        String parentSha1 = parentSuccessVerifyMap.get("ASN1SHA1").toString();
        String certSource = parentSuccessVerifyMap.get("CertSource").toString();


        if (isFake) {
            updateTagList(tagList,"Fake Cert");
            parentList.add(parentSha1);
            x509Cert.setTagList(tagList);
            x509Cert.setParentCertIDList(parentList);
            signRow.setField(0, x509Cert);
            context.output(signStop, signRow);
            return;
        }

        // !isFake 验签成功！
        // 若证书存在于cert_system,则表示为系统证书，否则对其父证书做再做效验
        boolean isSelfSign = isSelfSignCert(parentSuccessVerifyMap);
        // 如果父证书列表中已经有了该证书SHA1，且该证书不是根自签名证书，则出现循环验签，证书链乱序
        if(parentList.contains(parentSha1) && !isSelfSign){
            updateTagList(tagList,"Chain Mess");
            x509Cert.setTagList(tagList);
            signRow.setField(0, x509Cert);
            context.output(signStop, signRow);
            return;
        }

        boolean sysCaCert = false;
        if ("System".equals(certSource)) {
            sysCaCert = true;
        }else {
            try {
                // 使用MinIO检查是否是系统证书
                sysCaCert = minioClient.certificateExists(parentSha1, false);
            }catch (Exception e){
                LOG.error("系统证书查询失败，error--->{}",e.toString());
            }
        }

        // 如果已经出现了自签名，并且不是系统证书，就说明存在自建的根证书，打标未知CA
        if(isSelfSign && !sysCaCert){
            tagList.add("Unknown CA");
            x509Cert.setTagList(tagList);
            parentList.add(parentSha1);
            x509Cert.setParentCertIDList(parentList);
            signRow.setField(0, x509Cert);
            context.output(signStop, signRow);
            return;
        }

        //判断是否是系统证书
        if (sysCaCert) {
            updateTagList(tagList,"White CA Cert");
            parentList.add(parentSha1);
            x509Cert.setTagList(tagList);
            x509Cert.setParentCertIDList(parentList);
            signRow.setField(0, x509Cert);
            context.output(signStop, signRow);
            return;
        }

        // 如果都不是，那就需要进行进一步验签了，肯定不是系统证书，只用查用户证书了
        byte[] parentCertByte = queryParentByte(parentSha1);

        //提前进行父证书的更新
        parentList.add(parentSha1);
        x509Cert.setTagList(tagList);
        x509Cert.setParentCertIDList(parentList);
        signRow.setField(0, x509Cert);

        if(parentCertByte == null){
            context.output(signStop, signRow);
            return;
        }

        if(parentCertByte.length == 0){
            context.output(signStop, signRow);
            return;
        }

        X509Cert cert = new X509Cert(parentCertByte);
        try {
            //要进一步验签的话需要更新父证书的map信息
            CertFormatUtil.parseContent(cert);
            parentListMap.put(parentSha1,cert);
            signRow.setField(1, parentListMap);
            context.output(signContinue, signRow);
            return;
        } catch (CertificateException e) {
            context.output(signStop, signRow);
            return;
        }
    }

    private static void updateParentWithKnownWhiteCaCert(List<String> parentList, List<String> whiteParentList) {
        for (String parentSha1 : whiteParentList) {
            if (!parentList.contains(parentSha1)) {
                parentList.add(parentSha1);
            }
        }
    }

    public static void updateTagList(List<String> tagList,String tagText){
        if (!tagList.contains(tagText)){
            tagList.add(tagText);
        }
    }


    /**
     * 是否是伪造证书？
     *
     * @param cert  证书
     * @param parentPublicKey 父证书公钥
     * @return yes or no.
     */
    public static Boolean isFakeCert(Certificate cert, PublicKey parentPublicKey) {
        Boolean result = false;

        //抛出以下三个异常，被认为该证书是伪造的。
        try {
            cert.verify(parentPublicKey);
        } catch (NoSuchAlgorithmException | InvalidKeyException | SignatureException e) {
            result = true;
        } catch (Exception e) {
            result = null;
            LOG.warn("parent certificate publicKey parse fail.");
        }

        return result;
    }


    /**
     * 白名单证书是否存在
     * 第一次不需要，能进验签肯定不是白名单证书
     * 只查user
     * @param x509Cert
     * @return
     */
    public static List<String> findExistWhiteCACert(X509Cert x509Cert, RestHighLevelClient esClient) throws Exception {

        List<String> parentList = null;

        HashMap<String, Map> result = EsUtils.matchQuery("cert_user", "ASN1SHA1", x509Cert.getASN1SHA1(), null, null,esClient);

        if (!result.isEmpty()) {
            Set<String> dataIds = result.keySet();
            for (String dataId : dataIds) {
                Map data = result.get(dataId);
                List<String> tags = CertFormatUtil.castList(data.getOrDefault("Tags", new ArrayList<>()), String.class);
                if (tags.contains("227")) {
                    parentList = (List<String>) data.getOrDefault("ParentCertID", new ArrayList<>());
                    return parentList;
                }
            }
        } else {
            return null;
        }
        return null;
    }

    /**
     * 根据issuerMD5以及issuerKeyId进行父证书匹配
     *
     * @param x509Cert 证书对象
     * @param EsClient1 ES客户端
     * @return 父证书映射
     * @throws Exception 查询异常
     */
    public static HashMap<String, Map> getParentCert(X509Cert x509Cert, RestHighLevelClient EsClient1) throws Exception {
        HashMap<String, Map> result;

        HashMap extension = CertFormatUtil.objectToMap(x509Cert.getExtension());
        String parentKey = String.valueOf(CertFormatUtil.readDataFromKeys(extension, "authorityKeyIdentifier", ""));

        String issuerMD5 = x509Cert.getIssuerMD5();

        //模糊匹配父证书，必须满足颁发机构匹配MD5，颁发机构issuerKeyId
        result = EsUtils.nestedQuery("cert_*", "Extension", "SubjectMD5", issuerMD5, "subjectKeyIdentifier", parentKey,
                null, null, EsClient1);
        return result;
    }

    /**
     * 用ES查出来的父证书SHA1列表查询父证书信息列表
     *
     * @param parentSha1 父证书SHA1
     * @return 父证书数据
     */
    public static byte[] queryParentByte(String parentSha1) throws Exception {
        byte[] certByte = null;

        try {
            // 从MinIO查询证书数据
            List<String> sha1List = Arrays.asList(parentSha1);
            Map<String, byte[]> resultMap = MinioCertificateClient.queryDataList(sha1List);

            if (!resultMap.isEmpty() && resultMap.containsKey(parentSha1)) {
                certByte = resultMap.get(parentSha1);
            }
        } catch (Exception e) {
            LOG.error("MinIO查询失败，error--->{}, parentSha1 is--->{}",e.getMessage(), parentSha1);
        }

        if (certByte != null) {
            return certByte;
        }
        LOG.warn("MinIO没有查询到证书数据：——{}——", parentSha1);
        return null;
    }

    /**
     * base64 解码
     *
     * @param base64Str 用base64加密过的字符串
     * @return cert
     */
    public static byte[] decodeBase64(String base64Str) {
        // 若字符串为空
        if ("null".equals(base64Str) || base64Str.length() < 1) {
            return "".getBytes();
        }

        return Base64.getDecoder().decode(base64Str.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 当前证书的父证书查到了，但是当前证书的有效期范围和父证书冲突，或者父证书的IsCA字段不为CA证书
     * */
    public static boolean getIllegal(X509Cert x509Cert, HashMap x509Cert_Parent){
        HashMap extension = (HashMap) x509Cert_Parent.get("Extension");
        if (extension==null){
            return false;
        }
        if (extension.get("basicConstraints")==null){
            return false;
        }
        String basicConstraints = (String) extension.get("basicConstraints");
        boolean isCA  = basicConstraints.contains("CA:TRUE");
        long notAfter = Long.parseLong(x509Cert.getNotAfter().replaceAll("Z", ""));
        long notBefore = Long.parseLong(x509Cert.getNotBefore().replaceAll("Z", ""));
        String parentNotAfterString = (String) x509Cert_Parent.get("NotAfter");
        String parentNotBeforeString = (String) x509Cert_Parent.get("NotBefore");
        long parentNotAfter = Long.parseLong(parentNotAfterString.replaceAll("Z", ""));
        long parentNotBefore = Long.parseLong(parentNotBeforeString.replaceAll("Z", ""));

        //三个条件全部满足即当前证书CA身份没有不合法问题
        return parentNotBefore >= notBefore || parentNotAfter <= notAfter || !isCA;
    }

    public static boolean isSelfSignCert(HashMap parentMap){
        HashMap extension = (HashMap) parentMap.getOrDefault("Extension","");
        String basicConstraints = String.valueOf(CertFormatUtil.readDataFromKeys(extension, "basicConstraints", ""));
        String subjectMD5 = (String) parentMap.getOrDefault("SubjectMD5","");
        String issuerMD5 = (String) parentMap.getOrDefault("IssuerMD5","");
        String subjectKeyId = String.valueOf(CertFormatUtil.readDataFromKeys(extension, "subjectKeyIdentifier", ""));//取出subject的KeyId值
        if ("null".equals(subjectKeyId)) {
            subjectKeyId = "";
        }

        String issuerKeyId = String.valueOf(CertFormatUtil.readDataFromKeys(extension, "authorityKeyIdentifier", ""));//取出issuer的KeyId值
        if ("null".equals(issuerKeyId)) {
            issuerKeyId = "";
        }
        String testIssuerKeyId = (issuerKeyId.split(",")[0]).replaceAll("keyid:", "");
        if (basicConstraints.contains("CA:TRUE")) {
            return subjectMD5.equals(issuerMD5) || (!"".equals(subjectKeyId) && subjectKeyId.equals(testIssuerKeyId));
        }else {
            return false;
        }
    }
}
