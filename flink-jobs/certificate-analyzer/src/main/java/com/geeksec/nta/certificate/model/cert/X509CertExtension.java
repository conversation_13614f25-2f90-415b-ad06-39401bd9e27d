package com.geeksec.nta.certificate.model.cert;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.geeksec.nta.certificate.model.UncommonOID;
import com.geeksec.nta.certificate.common.CertFormatUtil;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.security.cert.X509Certificate;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

import lombok.Getter;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bouncycastle.asn1.*;
import org.bouncycastle.asn1.x500.AttributeTypeAndValue;
import org.bouncycastle.asn1.x509.*;
import org.bouncycastle.cert.X509CertificateHolder;
import org.bouncycastle.cert.jcajce.JcaX509CertificateHolder;
import org.certificatetransparency.ctlog.proto.Ct;
import org.certificatetransparency.ctlog.serialization.Deserializer;

import static com.geeksec.nta.certificate.common.CertFormatUtil.bytesToString16;

/**
 * <AUTHOR>
 * @createTime 2025/2/07 10:38
 **/
@Getter
public class X509CertExtension {
    private static final Logger LOG = LogManager.getLogger(X509CertExtension.class);

    private String authorityKeyIdentifier = null;
    private String subjectKeyIdentifier = null;
    private String subjectAltName = null;
    private String issuerAltName = null;
    private String keyUsage = null;
    private String extendedKeyUsage = null;
    private String crlDistributionPoints = null;
    private String freshestCrl = null;
    private String policyConstraints = null;
    private String certificatePolicies = null;
    private String authorityInfoAccess = null;
    private String subjectInfoAccess = null;
    private String basicConstraints = null;
    private String ocspNoCheck = null;
    private String preCertPoison = null;
    private String tlsFeature = null;
    private String inhibitAnyPolicy = null;
    private String nameConstraints = null;
    private String sct = null;
    private String preSct = null;
    @Getter
    private HashMap<String, String> extensionMap = new HashMap<>();
    @Getter
    private List<UncommonOID> uncommonOidList = new ArrayList<>();

    public static final String EXT_OCSP_NO_CHECK_OID = "*******.5.5.7.48.1.5";
    public static final String EXT_PRE_CERT_POISON_OID = "*******.4.1.11129.2.4.3";
    public static final String EXT_TLS_FEATURE_OID = "*******.*******.24";
    public static final String EXT_SCT_OID = "*******.4.1.11129.2.4.5";
    public static final String EXT_PRE_SCT_OID = "*******.4.1.11129.2.4.2";

    public void parseExtension(X509Certificate x509Cert){
        setAuthorityKeyIdentifier(x509Cert);
        setSubjectKeyIdentifier(x509Cert);
        setSubjectAltName(x509Cert);
        setIssuerAltName(x509Cert);
        setKeyUsage(x509Cert);
        setExtendedKeyUsage(x509Cert);
        setCrlDistributionPoints(x509Cert);
        setFreshestCrl(x509Cert);
        setCertificatePolicies(x509Cert);
        setAuthorityInfoAccess(x509Cert);
        setSubjectInfoAccess(x509Cert);
        setBasicConstraints(x509Cert);
        setPolicyConstraints(x509Cert);
        setOcspNoCheck(x509Cert);
        setPreCertPoison(x509Cert);
        setTlsFeature(x509Cert);
        setInhibitAnyPolicy(x509Cert);
        setNameConstraints(x509Cert);
        setSct(x509Cert);
        setPreSct(x509Cert);
        setExtensionMap();
    }

    private void setAuthorityKeyIdentifier(X509Certificate cert) {
        try {
            X509CertificateHolder certHolder = new JcaX509CertificateHolder(cert);
            AuthorityKeyIdentifier authorityKeyIdentifier = AuthorityKeyIdentifier.fromExtensions(certHolder.getExtensions());
            if (authorityKeyIdentifier != null) {
                byte[] keyIdentifier = authorityKeyIdentifier.getKeyIdentifier();
                this.authorityKeyIdentifier = bytesToString16(keyIdentifier);
            } else {
                this.authorityKeyIdentifier = "";
            }

        } catch (Exception e) {
            this.authorityKeyIdentifier = "";
        }
    }

    private void setSubjectKeyIdentifier(X509Certificate cert) {
        try {
            X509CertificateHolder certHolder = new JcaX509CertificateHolder(cert);
            SubjectKeyIdentifier subjectKeyIdentifier = SubjectKeyIdentifier.fromExtensions(certHolder.getExtensions());
            if (subjectKeyIdentifier != null) {
                byte[] keyIdentifier = subjectKeyIdentifier.getKeyIdentifier();
                this.subjectKeyIdentifier = bytesToString16(keyIdentifier);
            } else {
                this.subjectKeyIdentifier = "";
            }
        } catch (Exception e) {
            this.subjectKeyIdentifier = "";
        }
    }

    private void setSubjectAltName(X509Certificate cert) {
        ObjectMapper mapper = new ObjectMapper();
        ArrayNode result = mapper.createArrayNode();

        try {
            X509CertificateHolder certHolder = new JcaX509CertificateHolder(cert);
            Extension extension = certHolder.getExtension(Extension.subjectAlternativeName);
            if (extension != null) {
                byte[] subjectAltNameByte = extension.getExtnValue().getOctets();
                getAltName(mapper, result, subjectAltNameByte);

                this.subjectAltName = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(result);
            }
        } catch (Exception e) {
            this.subjectAltName = mapper.createArrayNode().toString();
        }
    }

    private void setIssuerAltName(X509Certificate cert) {
        ObjectMapper mapper = new ObjectMapper();
        ArrayNode result = mapper.createArrayNode();

        try {
            X509CertificateHolder certHolder = new JcaX509CertificateHolder(cert);
            Extension extension = certHolder.getExtension(Extension.issuerAlternativeName);
            if (extension != null) {
                byte[] issuerAltNameByte = extension.getExtnValue().getOctets();
                getAltName(mapper, result, issuerAltNameByte);
            }

            this.issuerAltName = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(result);
        } catch (Exception e) {
            this.issuerAltName = mapper.createArrayNode().toString();
        }
    }

    private void setKeyUsage(X509Certificate cert) {
        try {
            X509CertificateHolder certHolder = new JcaX509CertificateHolder(cert);

            // 获取 KeyUsage 扩展字段
            KeyUsage keyUsageExtension = KeyUsage.fromExtensions(certHolder.getExtensions());

            if (keyUsageExtension != null) {
                this.keyUsage = CertFormatUtil.parseKeyUsage(keyUsageExtension);
            } else {
                this.keyUsage = "";
            }
        } catch (Exception e) {
            this.keyUsage = "";
        }
    }

    private void setExtendedKeyUsage(X509Certificate cert) {
        ArrayList<String> result = new ArrayList<>();
        try {
            X509CertificateHolder certHolder = new JcaX509CertificateHolder(cert);
            ExtendedKeyUsage extendedKeyUsage = ExtendedKeyUsage.fromExtensions(certHolder.getExtensions());

            if (extendedKeyUsage != null) {
                KeyPurposeId[] usages = extendedKeyUsage.getUsages();
                for (KeyPurposeId usage : usages) {
                    result.add(CertFormatUtil.getExtendedKenUsageKey(usage.getId(), uncommonOidList));
                }

                this.extendedKeyUsage = StringUtils.join(result, ", ");
            }else {
                this.extendedKeyUsage = "";
            }
        } catch (Exception e) {
            this.extendedKeyUsage = "";
        }
    }

    private void setCrlDistributionPoints(X509Certificate cert) {
        ObjectMapper mapper = new ObjectMapper();
        ArrayNode result = mapper.createArrayNode();

        try {
            X509CertificateHolder certHolder = new JcaX509CertificateHolder(cert);
            Extension extension = certHolder.getExtension(Extension.cRLDistributionPoints);
            if (extension == null) {
                // 返回空 JSON 数组
                this.crlDistributionPoints = mapper.createArrayNode().toString();
                return;
            }

            CRLDistPoint crlDistributionPoints = CRLDistPoint.fromExtensions(certHolder.getExtensions());
            getDistributionPointList(mapper, result, crlDistributionPoints);

            // 返回 JSON 数组的字符串表示
            this.crlDistributionPoints = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(result);
        } catch (Exception e) {
            // 发生异常时返回空 JSON 数组
            this.crlDistributionPoints = mapper.createArrayNode().toString();
        }
    }

    private void setFreshestCrl(X509Certificate cert) {
        ObjectMapper mapper = new ObjectMapper();
        ArrayNode result = mapper.createArrayNode();

        try {
            X509CertificateHolder certHolder = new JcaX509CertificateHolder(cert);
            Extension extension = certHolder.getExtension(Extension.freshestCRL);
            if (extension == null) {
                // 返回空 JSON 数组
                this.freshestCrl = mapper.createArrayNode().toString();
                return;
            }

            CRLDistPoint freshestCrlPoints = CRLDistPoint.fromExtensions(certHolder.getExtensions());
            getDistributionPointList(mapper, result, freshestCrlPoints);

            this.freshestCrl = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(result);
        } catch (Exception e) {
            this.freshestCrl = mapper.createArrayNode().toString();
        }
    }

    private void setCertificatePolicies(X509Certificate cert) {
        ObjectMapper mapper = new ObjectMapper();
        ArrayNode result = mapper.createArrayNode();

        try {
            X509CertificateHolder certHolder = new JcaX509CertificateHolder(cert);
            Extension extension = certHolder.getExtension(Extension.certificatePolicies);
            if(extension == null){
                // 如果没有扩展字段，返回空 JSON 数组
                this.certificatePolicies = mapper.createArrayNode().toString();
                return;
            }

            CertificatePolicies policies = CertificatePolicies.fromExtensions(certHolder.getExtensions());
            PolicyInformation[] policyInformation = policies.getPolicyInformation();

            extractCertificatePolicies(policyInformation, mapper, result);

            // 返回 JSON 数组的字符串表示
            this.certificatePolicies = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(result);
        } catch (Exception e) {
            // 发生异常时返回空 JSON 数组
            this.certificatePolicies = mapper.createArrayNode().toString();
        }
    }

    private void setAuthorityInfoAccess(X509Certificate cert) {
        ObjectMapper mapper = new ObjectMapper();
        ArrayNode result = mapper.createArrayNode();

        try {
            X509CertificateHolder certHolder = new JcaX509CertificateHolder(cert);
            AuthorityInformationAccess authority = AuthorityInformationAccess.fromExtensions(certHolder.getExtensions());

            if (authority != null) {
                getAia(mapper, result, authority);

                this.authorityInfoAccess = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(result);
            }else {
                this.authorityInfoAccess = mapper.createArrayNode().toString();
            }
        } catch (Exception e) {
            this.authorityInfoAccess = mapper.createArrayNode().toString();
        }
    }

    //这里看了很多的java依赖，都没有SubjectInfoAccess的拓展字段解析方法
    //使用AuthorityInformationAccess的方法，因为二者的内容是一致的
    private void setSubjectInfoAccess(X509Certificate cert) {
        ObjectMapper mapper = new ObjectMapper();
        ArrayNode result = mapper.createArrayNode();

        try {
            X509CertificateHolder certHolder = new JcaX509CertificateHolder(cert);
            AuthorityInformationAccess authority = AuthorityInformationAccess.fromExtensions(certHolder.getExtensions());
            if (authority!=null){
                getAia(mapper, result, authority);
                this.subjectInfoAccess = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(result);
            }else {
                this.subjectInfoAccess = mapper.createArrayNode().toString();
            }
        } catch (Exception e) {
            this.subjectInfoAccess = mapper.createArrayNode().toString();
        }
    }

    private void setBasicConstraints(X509Certificate cert) {
        try {
            X509CertificateHolder certHolder = new JcaX509CertificateHolder(cert);
            BasicConstraints basicConstraints = BasicConstraints.fromExtensions(certHolder.getExtensions());
            if (basicConstraints!=null){
                if (basicConstraints.isCA()) {
                    this.basicConstraints = "CA:TRUE";
                    if (basicConstraints.getPathLenConstraint()!=null){
                        this.basicConstraints =this.basicConstraints +",PathLenConstraint="+basicConstraints.getPathLenConstraint().toString();
                    }
                } else {
                    this.basicConstraints = "CA:FALSE";
                }
            }else {
                this.basicConstraints = "";
            }
        } catch (Exception e) {
            this.basicConstraints = "";
        }
    }

    private void setPolicyConstraints(X509Certificate cert) {
        try {
            X509CertificateHolder certHolder = new JcaX509CertificateHolder(cert);
            Extension extension = certHolder.getExtension(Extension.policyConstraints);
            if (extension!=null){
                PolicyConstraints policyConstraints = PolicyConstraints.fromExtensions(certHolder.getExtensions());
                BigInteger requireExplicitPolicyMapping = policyConstraints.getRequireExplicitPolicyMapping();
                BigInteger inhibitPolicyMapping = policyConstraints.getInhibitPolicyMapping();
                if (requireExplicitPolicyMapping!=null && inhibitPolicyMapping!=null){
                    this.policyConstraints = String.format("requireExplicitPolicyMapping:%d,inhibitPolicyMapping:%d",requireExplicitPolicyMapping,inhibitPolicyMapping);
                }else {
                    if (requireExplicitPolicyMapping!=null){
                        this.policyConstraints = String.format("requireExplicitPolicyMapping:%d,inhibitPolicyMapping:null",requireExplicitPolicyMapping);
                    } else if (inhibitPolicyMapping!=null) {
                        this.policyConstraints = String.format("requireExplicitPolicyMapping:null,inhibitPolicyMapping:%d",inhibitPolicyMapping);
                    }else {
                        this.policyConstraints = "";
                    }
                }
            }
        } catch (Exception e) {
            this.policyConstraints = "";
        }
    }

    public void setOcspNoCheck(X509Certificate cert) {
        try {
            X509CertificateHolder certHolder = new JcaX509CertificateHolder(cert);
            // 获取 OCSPNoCheck 扩展字段的原始字节
            Extension extension = certHolder.getExtension(new ASN1ObjectIdentifier(EXT_OCSP_NO_CHECK_OID));

            if (extension != null) {
                // 解析 ASN.1 编码
                byte[] ocspNoCheckBytes = extension.getExtnValue().getOctets();
                try (ASN1InputStream ais = new ASN1InputStream(ocspNoCheckBytes)) {
                    if (DERNull.INSTANCE.equals(ais.readObject())){
                        this.ocspNoCheck = "false";
                    }else {
                        ASN1OctetString oct = (ASN1OctetString) ais.readObject();
                        try (ASN1InputStream ais2 = new ASN1InputStream(oct.getOctets())) {
                            ASN1Primitive obj = ais2.readObject();
                            boolean isNoCheck = ASN1Boolean.getInstance(obj).isTrue();
                            this.ocspNoCheck = String.valueOf(isNoCheck);
                        }
                    }
                }
            } else {
                // OCSPNoCheck 扩展字段不存在
                this.ocspNoCheck = "false";
            }
        } catch (Exception e) {
            // 发生异常时返回空字符串
            this.ocspNoCheck = "";
        }
    }

    public void setPreCertPoison(X509Certificate cert) {
        try {
            X509CertificateHolder certHolder = new JcaX509CertificateHolder(cert);
            // 获取 PreCertPoison 扩展字段的原始字节
            Extension extension = certHolder.getExtension(new ASN1ObjectIdentifier(EXT_PRE_CERT_POISON_OID));
            if (extension != null) {
                byte[] preCertPoisonBytes = extension.getExtnValue().getOctets();
                // 解析 ASN.1 编码
                try (ASN1InputStream ais = new ASN1InputStream(preCertPoisonBytes)) {
                    ASN1OctetString oct = (ASN1OctetString) ais.readObject();
                    if(DERNull.INSTANCE.equals(oct.getOctets())){
                        this.preCertPoison = "False";
                    }else {
                        try (ASN1InputStream ais2 = new ASN1InputStream(oct.getOctets())) {
                            ASN1Primitive obj = ais2.readObject();
                            boolean isPreCertPoison = ASN1Boolean.getInstance(obj).isTrue();
                            // PreCertPoison 扩展字段的值通常为空
                            this.preCertPoison = String.valueOf(isPreCertPoison);
                        }
                    }
                }
            } else {
                // PreCertPoison 扩展字段不存在
                this.preCertPoison = "False";
            }
        } catch (Exception e) {
            // 发生异常时返回空字符串
            this.preCertPoison = "";
        }
    }

    public void setTlsFeature(X509Certificate cert) {
        ObjectMapper mapper = new ObjectMapper();
        ArrayNode result = mapper.createArrayNode();

        try {
            X509CertificateHolder certHolder = new JcaX509CertificateHolder(cert);
            Extension tlsFeatureExtension = certHolder.getExtension(new ASN1ObjectIdentifier(EXT_TLS_FEATURE_OID));

            if (tlsFeatureExtension != null) {
                byte[] tlsFeatureBytes = tlsFeatureExtension.getExtnValue().getOctets();
                parseTlsFeature(mapper, result, tlsFeatureBytes);
                this.tlsFeature = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(result);
            } else {
                this.tlsFeature = mapper.createArrayNode().toString();
            }
        } catch (Exception e) {
            this.tlsFeature = mapper.createArrayNode().toString();
        }
    }

    public void setInhibitAnyPolicy(X509Certificate cert) {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode result = mapper.createObjectNode();

        try {
            X509CertificateHolder certHolder = new JcaX509CertificateHolder(cert);
            Extension inhibitAnyPolicyExtension = certHolder.getExtension(Extension.inhibitAnyPolicy);
            if (inhibitAnyPolicyExtension != null) {
                byte[] inhibitAnyPolicyBytes = inhibitAnyPolicyExtension.getExtnValue().getOctets();
                int skipCerts = parseInhibitAnyPolicy(inhibitAnyPolicyBytes);
                result.put("skip_certs", skipCerts);
                this.inhibitAnyPolicy = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(result);
            } else {
                this.inhibitAnyPolicy = mapper.createObjectNode().toString();
            }
        } catch (Exception e) {
            this.inhibitAnyPolicy = mapper.createObjectNode().toString();
        }
    }

    public void setNameConstraints(X509Certificate cert) {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode result = mapper.createObjectNode();

        try {
            X509CertificateHolder certHolder = new JcaX509CertificateHolder(cert);
            Extension nameConstraintsExtension = certHolder.getExtension(Extension.nameConstraints);

            if (nameConstraintsExtension != null) {
                extractNameConstraints(nameConstraintsExtension, mapper, result);
                this.nameConstraints = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(result);
            } else {
                this.nameConstraints = mapper.createObjectNode().toString();
            }
        } catch (Exception e) {
            this.nameConstraints = mapper.createObjectNode().toString();
        }
    }

    private void setSct(X509Certificate cert) {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode result = mapper.createObjectNode();

        try {
            X509CertificateHolder certHolder = new JcaX509CertificateHolder(cert);
            Extension sctExtension = certHolder.getExtension(new ASN1ObjectIdentifier(EXT_SCT_OID));

            if (sctExtension != null) {
                byte[] sctBytes = sctExtension.getExtnValue().getOctets();
                ArrayNode sctList = parseSctBytes(mapper, sctBytes);
                result.set("sct_list", sctList);
            }
            this.sct = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(result);
        } catch (Exception e) {
            this.sct = mapper.createObjectNode().toString();
        }
    }

    private void setPreSct(X509Certificate cert) {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode result = mapper.createObjectNode();

        try {
            X509CertificateHolder certHolder = new JcaX509CertificateHolder(cert);
            Extension preSctExtension = certHolder.getExtension(new ASN1ObjectIdentifier(EXT_PRE_SCT_OID));

            if (preSctExtension != null) {
                byte[] preSctBytes = preSctExtension.getExtnValue().getOctets();
                ArrayNode sctList = parseSctBytes(mapper, preSctBytes);
                result.set("pre_sct_list", sctList);
            }
            this.preSct = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(result);
        } catch (Exception e) {
            this.preSct = mapper.createObjectNode().toString();
        }
    }

    public void setExtensionMap() {
        HashMap<String, String> resultMap = new HashMap<>();

        if (StringUtils.isNotBlank(authorityKeyIdentifier)) {
            resultMap.put("authorityKeyIdentifier", authorityKeyIdentifier);
        }

        if (StringUtils.isNotBlank(subjectKeyIdentifier)) {
            resultMap.put("subjectKeyIdentifier", subjectKeyIdentifier);
        }

        if (StringUtils.isNotBlank(subjectAltName)) {
            resultMap.put("subjectAltName", subjectAltName);
        }

        if (StringUtils.isNotBlank(issuerAltName)) {
            resultMap.put("issuerAltName", issuerAltName);
        }

        if (StringUtils.isNotBlank(freshestCrl)) {
            resultMap.put("freshestCRL", freshestCrl);
        }

        if (StringUtils.isNotBlank(crlDistributionPoints)) {
            resultMap.put("crlDistributionPoints", crlDistributionPoints);
        }

        if (StringUtils.isNotBlank(keyUsage)) {
            resultMap.put("keyUsage", keyUsage);
        }

        if (StringUtils.isNotBlank(extendedKeyUsage)) {
            resultMap.put("extendedKeyUsage", extendedKeyUsage);
        }

        if (StringUtils.isNotBlank(policyConstraints)) {
            resultMap.put("policyConstraints", policyConstraints);
        }

        if (StringUtils.isNotBlank(certificatePolicies)) {
            resultMap.put("certificatePolicies", certificatePolicies);
        }

        if (StringUtils.isNotBlank(authorityInfoAccess)) {
            resultMap.put("authorityInfoAccess", authorityInfoAccess);
        }

        if (StringUtils.isNotBlank(subjectInfoAccess)) {
            resultMap.put("subjectInfoAccess", subjectInfoAccess);
        }

        if (StringUtils.isNotBlank(basicConstraints)) {
            resultMap.put("basicConstraints", basicConstraints);
        }

        if (StringUtils.isNotBlank(ocspNoCheck)) {
            resultMap.put("ocspNoCheck", ocspNoCheck);
        }

        if (StringUtils.isNotBlank(preCertPoison)) {
            resultMap.put("preCertPoison", preCertPoison);
        }

        if (StringUtils.isNotBlank(tlsFeature)) {
            resultMap.put("tlsFeature", tlsFeature);
        }

        if (StringUtils.isNotBlank(inhibitAnyPolicy)) {
            resultMap.put("inhibitAnyPolicy", inhibitAnyPolicy);
        }

        if (StringUtils.isNotBlank(nameConstraints)) {
            resultMap.put("nameConstraints", nameConstraints);
        }

        if (StringUtils.isNotBlank(sct)) {
            resultMap.put("sct", sct);
        }

        if (StringUtils.isNotBlank(preSct)) {
            resultMap.put("preSct", preSct);
        }

        this.extensionMap = resultMap;
    }

    private void getAltName(ObjectMapper mapper, ArrayNode result, byte[] subjectAltNameByte) throws IOException {
        ASN1Primitive obj = ASN1Primitive.fromByteArray(subjectAltNameByte);
        ASN1Sequence seq = ASN1Sequence.getInstance(obj);
        GeneralNames names = GeneralNames.getInstance(seq);

        for (GeneralName name : names.getNames()) {
            ObjectNode nameNode = mapper.createObjectNode();
            String tag = CertFormatUtil.getGeneralNameKey(String.valueOf(name.getTagNo()));
            String nameValue = name.getName().toString();
            nameNode.put("type", tag);
            nameNode.put("value", nameValue);
            result.add(nameNode);
        }
    }

    private void getDistributionPointList(ObjectMapper mapper, ArrayNode result, CRLDistPoint freshestCrlPoints) {
        DistributionPoint[] distributionPoints = freshestCrlPoints.getDistributionPoints();


        for (DistributionPoint distributionPoint : distributionPoints) {
            ObjectNode dpNode = mapper.createObjectNode();

            // 处理 DistributionPointName
            if (distributionPoint.getDistributionPoint() != null) {
                DistributionPointName distributionPointName = distributionPoint.getDistributionPoint();
                int distributionPointNameType = distributionPointName.getType();
                ASN1Encodable name = distributionPointName.getName();

                if (distributionPointNameType == DistributionPointName.FULL_NAME) {
                    ArrayNode fullNameArray = mapper.createArrayNode();
                    GeneralName[] generalNames = GeneralNames.getInstance(name).getNames();
                    for (GeneralName generalName : generalNames) {
                        ObjectNode generalNameNode = mapper.createObjectNode();
                        generalNameNode.put("type", CertFormatUtil.getGeneralNameKey(String.valueOf(generalName.getTagNo())));
                        generalNameNode.put("value", generalName.getName().toString());
                        fullNameArray.add(generalNameNode);
                    }
                    dpNode.set("full_name", fullNameArray);
                } else if (distributionPointNameType == DistributionPointName.NAME_RELATIVE_TO_CRL_ISSUER) {
                    ArrayNode relativeNameArray = mapper.createArrayNode();
                    // 解析为 ASN1Set
                    ASN1Set relativeNameSet = ASN1Set.getInstance(name);
                    for (ASN1Encodable entry : relativeNameSet.toArray()) {
                        AttributeTypeAndValue attribute = AttributeTypeAndValue.getInstance(entry);
                        ObjectNode attributeNode = mapper.createObjectNode();
                        attributeNode.put("type", attribute.getType().toString());
                        attributeNode.put("value", attribute.getValue().toString());
                        relativeNameArray.add(attributeNode);
                    }
                    dpNode.set("relative_name", relativeNameArray);
                }
            }

            // 处理 Reasons
            if (distributionPoint.getReasons() != null) {
                ReasonFlags reasonFlags = distributionPoint.getReasons();
                String reasons = CertFormatUtil.getReasonFlagKey(reasonFlags.intValue());
                if (!reasons.isEmpty()) {
                    dpNode.set("reasons", mapper.valueToTree(reasons));
                }
            }

            // 处理 CRLIssuer
            if (distributionPoint.getCRLIssuer() != null) {
                ArrayNode crlIssuerArray = mapper.createArrayNode();
                GeneralName[] crlIssuers = distributionPoint.getCRLIssuer().getNames();
                for (GeneralName crlIssuer : crlIssuers) {
                    ObjectNode crlIssuerNode = mapper.createObjectNode();
                    crlIssuerNode.put("type", CertFormatUtil.getGeneralNameKey(String.valueOf(crlIssuer.getTagNo())));
                    crlIssuerNode.put("value", crlIssuer.getName().toString());
                    crlIssuerArray.add(crlIssuerNode);
                }
                if (!crlIssuerArray.isEmpty()) {
                    dpNode.set("crl_issuer", crlIssuerArray);
                }
            }

            result.add(dpNode);
        }
    }


    private void getAia(ObjectMapper mapper, ArrayNode result, AuthorityInformationAccess authority) {
        for (AccessDescription aDesc : authority.getAccessDescriptions()) {
            ObjectNode aDescNode = mapper.createObjectNode();

            // 处理 AccessMethod
            String oid = aDesc.getAccessMethod().getId();
            String methodName = CertFormatUtil.getAuthorityInfoAccessKey(oid, uncommonOidList);
            aDescNode.put("Access Method OID", oid);
            aDescNode.put("Access Method Name", methodName);

            // 处理 AccessLocation
            GeneralName accessLocation = aDesc.getAccessLocation();
            ObjectNode locationNode = mapper.createObjectNode();
            String locationType = CertFormatUtil.getGeneralNameKey(String.valueOf(accessLocation.getTagNo()));
            String locationValue = accessLocation.getName().toString();
            locationNode.put("type", locationType);
            locationNode.put("value", locationValue);
            aDescNode.set("Access Location", locationNode);

            result.add(aDescNode);
        }
    }

    private void parseTlsFeature(ObjectMapper mapper, ArrayNode result, byte[] tlsFeatureBytes) throws Exception {
        try (ASN1InputStream ais = new ASN1InputStream(tlsFeatureBytes)) {
            DLSequence seq = (DLSequence) ais.readObject();
            for (int i = 0; i < seq.size(); i++) {
                String oid = seq.getObjectAt(i).toString();
                ObjectNode featureNode = mapper.createObjectNode();

                // 将短 OID 转换为完整 OID
                String fullOid;
                switch (oid) {
                    case "5":
                        fullOid = "*******.*******.24.5";
                        featureNode.put("feature", "status_request");
                        featureNode.put("description", "OCSP Must-Staple (RFC 6066)");
                        break;
                    case "17":
                        fullOid = "*******.*******.24.17";
                        featureNode.put("feature", "status_request_v2");
                        featureNode.put("description", "Multiple OCSP Responses (RFC 6961)");
                        break;
                    case "*******.4.1.11129.2.4.2":
                        fullOid = oid;
                        featureNode.put("feature", "signed_certificate_timestamp");
                        featureNode.put("description", "Certificate Transparency (RFC 6962)");
                        break;
                    case "*******.4.1.44363.44.1":
                        fullOid = oid;
                        featureNode.put("feature", "delegated_credential");
                        featureNode.put("description", "Delegated Credentials (RFC 9345)");
                        break;
                    case "*******.4.1.44363.44.2":
                        fullOid = oid;
                        featureNode.put("feature", "tls_certificate_compression");
                        featureNode.put("description", "TLS Certificate Compression (RFC 8879)");
                        break;
                    case "*******.4.1.44363.44.3":
                        fullOid = oid;
                        featureNode.put("feature", "tls_early_data");
                        featureNode.put("description", "TLS 1.3 Early Data (RFC 8446)");
                        break;
                    case "*******.4.1.44363.44.4":
                        fullOid = oid;
                        featureNode.put("feature", "tls_key_share");
                        featureNode.put("description", "TLS 1.3 Key Share (RFC 8446)");
                        break;
                    default:
                        fullOid = oid;
                        featureNode.put("feature", "unknown");
                        featureNode.put("description", "Unsupported TLS Feature");
                        break;
                }

                // 将完整 OID 写入结果
                featureNode.put("oid", fullOid);

                result.add(featureNode);
            }
        }
    }

    private int parseInhibitAnyPolicy(byte[] inhibitAnyPolicyBytes) throws Exception {
        try (ASN1InputStream ais = new ASN1InputStream(inhibitAnyPolicyBytes)) {
            ASN1Integer oct = (ASN1Integer) ais.readObject();
            return new BigInteger(oct.toString()).intValue();
        }
    }

    private void extractCertificatePolicies(PolicyInformation[] policyInformation, ObjectMapper mapper, ArrayNode result) {
        for (PolicyInformation pInfo : policyInformation) {
            ObjectNode policyNode = mapper.createObjectNode();

            // 处理 PolicyIdentifier
            String pid = pInfo.getPolicyIdentifier().getId();
            policyNode.put("policy_identifier", CertFormatUtil.getCpsOID(pid, uncommonOidList));

            // 处理 PolicyQualifiers
            if (pInfo.getPolicyQualifiers() != null) {
                ArrayNode qualifiersArray = mapper.createArrayNode();
                ASN1Sequence qualifiers = pInfo.getPolicyQualifiers();

                for (int i = 0; i < qualifiers.size(); i++) {
                    ASN1Sequence policyQualifiers = (ASN1Sequence) qualifiers.getObjectAt(i);
                    ObjectNode qualifierNode = mapper.createObjectNode();

                    // 解析 Qualifier 类型和值
                    String qualifierType = CertFormatUtil.getCpsOID(policyQualifiers.getObjectAt(0).toString(), uncommonOidList);
                    ASN1Encodable qualifierValue = policyQualifiers.getObjectAt(1);

                    qualifierNode.put("type", qualifierType);

                    // 处理 UserNotice
                    if (qualifierValue instanceof ASN1Sequence) {
                        ASN1Sequence userNoticeSeq = (ASN1Sequence) qualifierValue;
                        ObjectNode userNoticeNode = mapper.createObjectNode();

                        // 解析 NoticeReference
                        if (userNoticeSeq.size() > 0 && userNoticeSeq.getObjectAt(0) instanceof ASN1Sequence) {
                            ASN1Sequence noticeRefSeq = (ASN1Sequence) userNoticeSeq.getObjectAt(0);
                            ObjectNode noticeRefNode = mapper.createObjectNode();

                            // 解析 Organization
                            if (noticeRefSeq.size() > 0) {
                                noticeRefNode.put("organization", noticeRefSeq.getObjectAt(0).toString());
                            }

                            // 解析 NoticeNumbers
                            if (noticeRefSeq.size() > 1) {
                                ASN1Sequence noticeNumbersSeq = (ASN1Sequence) noticeRefSeq.getObjectAt(1);
                                ArrayNode noticeNumbersArray = mapper.createArrayNode();
                                for (int j = 0; j < noticeNumbersSeq.size(); j++) {
                                    noticeNumbersArray.add(noticeNumbersSeq.getObjectAt(j).toString());
                                }
                                noticeRefNode.set("notice_numbers", noticeNumbersArray);
                            }

                            userNoticeNode.set("notice_reference", noticeRefNode);
                        }

                        // 解析 ExplicitText
                        if (userNoticeSeq.size() > 1) {
                            userNoticeNode.put("explicit_text", userNoticeSeq.getObjectAt(1).toString());
                        }

                        qualifierNode.set("value", userNoticeNode);
                    } else {
                        // 处理普通字符串值
                        qualifierNode.put("value", qualifierValue.toString());
                    }

                    qualifiersArray.add(qualifierNode);
                }

                policyNode.set("policy_qualifiers", qualifiersArray);
            }

            // 将当前 PolicyInformation 的 JSON 对象添加到结果数组中
            result.add(policyNode);
        }
    }

    private static void extractNameConstraints(Extension nameConstraintsExtension, ObjectMapper mapper, ObjectNode result) {
        byte[] nameConstraintsBytes = nameConstraintsExtension.getExtnValue().getOctets();
        NameConstraints nameConstraints = NameConstraints.getInstance(nameConstraintsBytes);

        // 处理 permittedSubtrees
        ArrayNode permittedSubtrees = mapper.createArrayNode();
        GeneralSubtree[] permitted = nameConstraints.getPermittedSubtrees();
        extractNameConstraintsGeneralName(mapper, permittedSubtrees, permitted);

        // 处理 excludedSubtrees
        ArrayNode excludedSubtrees = mapper.createArrayNode();
        GeneralSubtree[] excluded = nameConstraints.getExcludedSubtrees();
        extractNameConstraintsGeneralName(mapper, excludedSubtrees, excluded);

        // 将结果写入 JSON
        result.set("permitted_subtrees", permittedSubtrees);
        result.set("excluded_subtrees", excludedSubtrees);
    }

    private static void extractNameConstraintsGeneralName(ObjectMapper mapper, ArrayNode permittedSubtrees, GeneralSubtree[] permitted) {
        if (permitted != null) {
            for (GeneralSubtree subtree : permitted) {
                ObjectNode subtreeNode = mapper.createObjectNode();
                subtreeNode.put("type", CertFormatUtil.getGeneralNameKey(String.valueOf(subtree.getBase().getTagNo())));
                subtreeNode.put("value", subtree.getBase().getName().toString());
                permittedSubtrees.add(subtreeNode);
            }
        }
    }

    private ArrayNode parseSctBytes(ObjectMapper mapper, byte[] sctBytes) throws Exception {
        ArrayNode sctArray = mapper.createArrayNode();
        try (ASN1InputStream ais = new ASN1InputStream(sctBytes)) {
            ASN1OctetString oct = (ASN1OctetString) ais.readObject();
            String sctListString = oct.toString().substring(5);
            List<String> sctEntries = getSctList(sctListString);

            for (String entryHex : sctEntries) {
                ByteArrayInputStream bis = new ByteArrayInputStream(hexStringToByteArray(entryHex));
                Ct.SignedCertificateTimestamp sct = Deserializer.parseSCTFromBinary(bis);
                sctArray.add(createSctNode(mapper, sct));
            }
        }
        return sctArray;
    }

    private ObjectNode createSctNode(ObjectMapper mapper, Ct.SignedCertificateTimestamp sct) {
        ObjectNode node = mapper.createObjectNode();
        try {
            node.put("version", String.valueOf(sct.getVersion()));
            node.put("log_id", CertFormatUtil.bytesToString16(sct.getId().getKeyId().toByteArray()));
            node.put("timestamp", sct.getTimestamp());
            node.put("timestamp_formatted", formatTimestamp(sct.getTimestamp()));
            node.put("extensions", CertFormatUtil.bytesToString16(sct.getExtensions().toByteArray()));

            Ct.DigitallySigned signature = sct.getSignature();
            node.put("hash_algorithm", signature.getHashAlgorithm().name());
            node.put("signature_algorithm", signature.getSigAlgorithm().name());
            node.put("signature", CertFormatUtil.bytesToString16(signature.getSignature().toByteArray()));
        } catch (Exception e) {
            // 处理个别字段解析异常
            LOG.error("Error creating sct node", e);
        }
        return node;
    }

    // 优化时间格式化方法
    private String formatTimestamp(long timestamp) {
        return Instant.ofEpochMilli(timestamp)
                .atZone(ZoneId.systemDefault())
                .format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
    }

    /**
     * 将十六进制字符串转换为字节数组
     *
     * @param hexString 十六进制字符串
     * @return 字节数组
     */
    public static byte[] hexStringToByteArray(String hexString) {
        int length = hexString.length();
        if (length % 2 != 0) {
            throw new IllegalArgumentException("十六进制字符串长度必须是偶数");
        }

        byte[] byteArray = new byte[length / 2];
        for (int i = 0; i < length; i += 2) {
            // 每两位转换为一个字节
            String hexByte = hexString.substring(i, i + 2);
            byteArray[i / 2] = (byte) Integer.parseInt(hexByte, 16);
        }
        return byteArray;
    }

    private static List<String> getSctList(String sctListString){
        List<String> sctList = new ArrayList<>();
        long count = 0L;
        while(count<sctListString.length()){
            long end = count+4;
            long tmpLen = Math.toIntExact(Long.parseLong(sctListString.substring(Math.toIntExact(count), Math.toIntExact(end)), 16));
            sctList.add(sctListString.substring(Math.toIntExact(end), Math.toIntExact(end+tmpLen*2)));
            count+=(4+tmpLen*2);
        }
        return sctList;
    }
}
