package com.geeksec.nta.certificate.util.db.minio;

import com.geeksec.utils.FileUtil;
import com.geeksec.utils.MinioClientUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * MinIO客户端，用于从MinIO获取证书
 * 替代原有的LmdbClient
 */
public class MinioClient {
    private static final Logger log = LoggerFactory.getLogger(MinioClient.class);

    private final Properties properties;
    private final String endpoint;
    private final String accessKey;
    private final String secretKey;

    /**
     * 构造函数，初始化MinIO客户端
     */
    public MinioClient() {
        properties = FileUtil.getProperties("/config.properties");
        endpoint = properties.getProperty("minio.endpoint", "http://minio:9000");
        accessKey = properties.getProperty("minio.access.key", "minioadmin");
        secretKey = properties.getProperty("minio.secret.key", "minioadmin");
        log.info("MinIO client initialized with endpoint: {}", endpoint);
    }

    /**
     * 获取MinIO端点
     *
     * @return MinIO端点URL
     */
    public String getEndpoint() {
        return endpoint;
    }

    /**
     * 根据SHA1哈希值获取证书
     *
     * @param sha1  证书的SHA1哈希值
     * @param isUser 是否是用户证书
     * @return 证书数据，如果不存在则返回null
     */
    public byte[] getCertByteBySha1(String sha1, boolean isUser) {
        try {
            return MinioClientUtil.getCertificate(sha1, isUser);
        } catch (Exception e) {
            log.error("Failed to get certificate from MinIO: {}", sha1, e);
            return null;
        }
    }

    /**
     * 查询多个证书
     *
     * @param sha1List SHA1哈希值列表
     * @param isUser   是否是用户证书
     * @return 包含SHA1和对应证书数据的Map
     */
    public Map<String, byte[]> queryDataList(List<String> sha1List, boolean isUser) {
        Map<String, byte[]> result = new HashMap<>(sha1List.size());
        
        for (String sha1 : sha1List) {
            byte[] certData = getCertByteBySha1(sha1, isUser);
            if (certData != null) {
                result.put(sha1, certData);
            }
        }
        
        return result;
    }

    /**
     * 检查证书是否存在
     *
     * @param sha1  证书的SHA1哈希值
     * @param isUser 是否是用户证书
     * @return 如果存在则返回true，否则返回false
     */
    public boolean certificateExists(String sha1, boolean isUser) {
        try {
            return MinioClientUtil.certificateExists(sha1, isUser);
        } catch (Exception e) {
            log.error("Failed to check if certificate exists in MinIO: {}", sha1, e);
            return false;
        }
    }
}
