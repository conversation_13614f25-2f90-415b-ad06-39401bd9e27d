package com.geeksec.nta.certificate.io.sink.mysql;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.utils.MysqlUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2023/7/31
 */

public class MysqlErrorSink extends RichSinkFunction<X509Cert> implements SinkFunction<X509Cert> {
    private static final Logger LOG = LoggerFactory.getLogger(MysqlErrorSink.class);
    public String ErrorType = null;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void invoke(X509Cert x509Cert, Context context) throws Exception {
        ErrorType = x509Cert.getTagList().get(0);
        String OriginSHA1 = x509Cert.getASN1SHA1();
        String CorrectSHA1 = x509Cert.getASN1SHA1();
        boolean result = MysqlUtils.setErrorCertInfo(OriginSHA1,CorrectSHA1,ErrorType);
        LOG.info("纠错信息插入结果——{}——",result);
    }

    @Override
    public void close() throws Exception {
        super.close();
    }
}
