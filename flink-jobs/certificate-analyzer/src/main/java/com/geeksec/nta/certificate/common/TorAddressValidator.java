package com.geeksec.nta.certificate.common;

import java.util.regex.Pattern;

/**
 * Tor地址验证工具类
 *
 * <AUTHOR>
 * @date 2024/8/6
 */
public class TorAddressValidator {
    // 正则表达式，用于匹配 Tor v2 和 v3 地址
    private static final String TOR_V2_REGEX = "^[2-7]{16}\\.onion$";
    private static final String TOR_V3_REGEX = "^[a-z2-7]{56}\\.onion$";
    private static final String TOR_VHOST_REGEX = "^[a-z2-7]{56}\\.[a-z2-7]{16}\\.onion$";

    public static final String TOR_V2 = "TOR_V2";
    public static final String TOR_V3 = "TOR_V3";
    public static final String NOT_TOR = "NOT_TOR";

    public static String validateTorAddress(String address) {
        if (isTorV3Address(address)) {
            return TOR_V3;
        } else if (isTorV2Address(address)) {
            return TOR_V2;
        } else {
            return NOT_TOR;
        }
    }

    private static boolean isTorV2Address(String address) {
        return Pattern.matches(TOR_V2_REGEX, address);
    }

    private static boolean isTorV3Address(String address) {
        return Pattern.matches(TOR_V3_REGEX, address) || Pattern.matches(TOR_VHOST_REGEX, address);
    }
}
