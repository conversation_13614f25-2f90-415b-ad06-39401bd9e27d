package com.geeksec.nta.certificate.io.sink.mysql;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.utils.MysqlUtils;
import java.util.ArrayList;
import java.util.List;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2023/4/24
 */

public class mysqlSinkBatch extends RichSinkFunction<Row> implements SinkFunction<Row> {
    private static final Logger LOG = LoggerFactory.getLogger(mysqlSinkBatch.class);


    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void invoke(Row x509Cert_row, Context context) throws Exception {
        X509Cert x509Cert = (X509Cert) x509Cert_row.getField(1);

        List<String> task_batch_info = (List<String>) x509Cert_row.getField(0);
        List<String> task_batch_info_not_stop = new ArrayList<>();
        for(String task_batch:task_batch_info){
            boolean stop_import = MysqlUtils.mysql_stop_import(task_batch,x509Cert.getASN1SHA1());
            if (!stop_import){
                task_batch_info_not_stop.add(task_batch);
            }else {
                LOG.info("写入步骤中，是用户导入证书,但是终止导入的批次为——{}——",task_batch);
            }
        }
        LOG.info("是用户导入证书,且未终止导入的批次为——{}——",task_batch_info_not_stop);
        // 操作tb_cert_batch表
        try{
            int rowsAffected = MysqlUtils.setMysqlCertInfo(x509Cert.getASN1SHA1(),x509Cert.getIsDedupCert(),!x509Cert.getParseStatus(),task_batch_info_not_stop);
            if (rowsAffected==0){
                LOG.info("redis中有当前批次信息，mysql中没有");
            } else if (rowsAffected==-1) {
                LOG.error("证书在查询redis和mysql时失败");
            } else {
                LOG.info("成功修改系统证书索引数量:--{}--",rowsAffected);
            }
        }catch (Exception e){
            LOG.error("写入batch表错误，信息如下：{}，{}，{}，{}",x509Cert.getASN1SHA1(),x509Cert.getIsDedupCert(),!x509Cert.getParseStatus(),task_batch_info_not_stop);
        }

    }

    @Override
    public void close() throws Exception {
        super.close();
    }
}
