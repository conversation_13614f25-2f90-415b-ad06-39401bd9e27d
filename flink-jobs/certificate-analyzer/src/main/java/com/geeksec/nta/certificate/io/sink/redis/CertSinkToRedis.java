package com.geeksec.nta.certificate.io.sink.redis;

import static com.geeksec.analysisFunction.certSplitAll.CertSplit.BLACK_SCORE_MAP_tag;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.utils.RedisUtils;
import java.util.ArrayList;
import java.util.List;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * <AUTHOR>
 * @Date 2024/9/12
 */

public class CertSinkToRedis extends RichSinkFunction<JSONObject> {
    private static transient JedisPool jedisPool = null;
    private final static Logger logger = LoggerFactory.getLogger(CertSinkToRedis.class);

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // redis池 初始化.
        jedisPool = RedisUtils.initJedisPool();
        logger.info("生成jedisPool成功! {}", jedisPool.getNumIdle(),jedisPool.hashCode());
    }

    @Override
    public void close() throws Exception {
        super.close();
        if(!jedisPool.isClosed()){
            jedisPool.close();
        }
    }

    @Override
    public void invoke(JSONObject jsonObject, Context context) throws Exception {
        Jedis jedis = null;
        try{
            jedis = RedisUtils.getJedis(jedisPool);
            jedis.select(8);

            List<String> tags = (List<String>) jsonObject.get("Labels");
            List<String> threatTags = getThreatTags(tags);
            Integer BlackList = (Integer) jsonObject.get("BlackList");
            List<String> AssociateDomain = (List<String>) jsonObject.getOrDefault("AssociateDomain",new ArrayList<>());
            if(!threatTags.isEmpty() && BlackList>80){
                JSONObject simpleJson = new JSONObject();
                simpleJson.put("ASN1SHA1",jsonObject.get("ASN1SHA1"));
                simpleJson.put("ThreatTags",threatTags);
                simpleJson.put("BlackList",BlackList);
                simpleJson.put("AssociateDomain",AssociateDomain);

                String simpleCertInfo = simpleJson.toJSONString();

                String key = "pushDataCert_" + jsonObject.get("ASN1SHA1");

                jedis.setex(key, 86400, simpleCertInfo);
            }
        }catch (Exception e){
            logger.error("证书简单信息写入redis，读取redis失败，error:——{}——",e.toString());
        }finally {
            if (jedis != null){
                jedis.close();
            }
        }
    }

    private List<String> getThreatTags(List<String> tags) {
        List<String> threatTags = new ArrayList<>();
        for(String tag:tags){
            if (BLACK_SCORE_MAP_tag.getOrDefault(tag,0)>0){
                threatTags.add(tag);
            }
        }
        return threatTags;
    }
}
