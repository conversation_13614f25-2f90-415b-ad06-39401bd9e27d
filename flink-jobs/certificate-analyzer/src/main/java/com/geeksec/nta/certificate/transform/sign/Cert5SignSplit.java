package com.geeksec.nta.certificate.transform.sign;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.flinkTool.sideOutputTag.signOutPutTag;
import java.util.*;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2023/1/18
 */

public class Cert5SignSplit extends ProcessFunction<Row, Row> {
    protected static final Logger LOG = LoggerFactory.getLogger(Cert5SignSplit.class);

    @Override
    public void open(Configuration parameters) throws Exception {
        LOG.info("Initialization of global parameters starts.");
    }

    @Override
    public void close() throws Exception {
        super.close();
    }

    @Override//应保持x509子证书的parentList字段与parentListMap的key相匹配
    public void processElement(Row signRow, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
        X509Cert x509Cert = (X509Cert) signRow.getField(0);
        List<String> tagList = x509Cert.getTagList();
        tagList.add("Long CertList");
        x509Cert.setTagList(tagList);
        signRow.setField(0,x509Cert);
        context.output(signOutPutTag.stop_sign5,signRow);
    }
}
