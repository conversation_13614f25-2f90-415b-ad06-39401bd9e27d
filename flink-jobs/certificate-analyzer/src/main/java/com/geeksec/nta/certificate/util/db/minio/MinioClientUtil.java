package com.geeksec.nta.certificate.util.db.minio;

import io.minio.*;
import io.minio.errors.*;
import io.minio.messages.Item;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

/**
 * MinIO客户端工具类，用于操作MinIO对象存储
 *
 * <AUTHOR>
 */
public class MinioClientUtil {
    private static final Logger logger = LoggerFactory.getLogger(MinioClientUtil.class);

    /**
     * 证书文件名前缀
     */
    private static final String PREFIX = "cert_";

    /**
     * SHA1哈希值的前缀长度，用于创建子目录
     */
    private static final int SHA1_PREFIX_LENGTH = 2;

    /**
     * MinIO客户端
     */
    private static MinioClient minioClient;

    /**
     * 存储桶名称
     */
    private static String bucketName;

    static {
        try {
            Properties properties = FileUtil.getProperties("/config.properties");
            String endpoint = properties.getProperty("minio.endpoint", "http://minio:9000");
            String accessKey = properties.getProperty("minio.access.key", "minioadmin");
            String secretKey = properties.getProperty("minio.secret.key", "minioadmin");
            bucketName = properties.getProperty("minio.bucket.name", "certificates");

            minioClient = MinioClient.builder()
                    .endpoint(endpoint)
                    .credentials(accessKey, secretKey)
                    .build();

            // 确保存储桶存在
            ensureBucketExists(bucketName);

            logger.info("MinIO client initialized successfully with endpoint: {}", endpoint);
        } catch (Exception e) {
            logger.error("Failed to initialize MinIO client", e);
            throw new RuntimeException("Failed to initialize MinIO client", e);
        }
    }

    /**
     * 确保存储桶存在，如果不存在则创建
     *
     * @param bucket 存储桶名称
     * @throws Exception 如果创建存储桶失败
     */
    private static void ensureBucketExists(String bucket) throws Exception {
        boolean exists = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucket).build());
        if (!exists) {
            minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucket).build());
            logger.info("Created bucket: {}", bucket);
        }
    }

    /**
     * 错误码常量
     */
    private static final String ERROR_NO_SUCH_KEY = "NoSuchKey";

    /**
     * 上传证书到MinIO
     *
     * @param sha1 证书的SHA1哈希值
     * @param data 证书数据
     * @throws Exception 如果上传失败
     */
    public static void uploadCertificate(String sha1, byte[] data) throws Exception {
        // 构建对象名称
        String objectName = buildObjectName(sha1);

        try (ByteArrayInputStream bais = new ByteArrayInputStream(data)) {
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .stream(bais, data.length, -1)
                            .contentType("application/octet-stream")
                            .build());
            logger.debug("Certificate uploaded to MinIO: bucket={}, object={}", bucketName, objectName);
        }
    }

    /**
     * 从MinIO获取证书
     *
     * @param sha1 证书的SHA1哈希值
     * @return 证书数据，如果不存在则返回null
     */
    public static byte[] getCertificate(String sha1) {
        // 构建对象名称
        String objectName = buildObjectName(sha1);

        try {
            GetObjectResponse response = minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build());

            return readAllBytes(response);
        } catch (ErrorResponseException e) {
            if (ERROR_NO_SUCH_KEY.equals(e.errorResponse().code())) {
                logger.debug("Certificate not found in MinIO: bucket={}, object={}", bucketName, objectName);
                return null;
            }
            logger.error("Error getting certificate from MinIO", e);
            throw new RuntimeException("Error getting certificate from MinIO", e);
        } catch (Exception e) {
            logger.error("Error getting certificate from MinIO", e);
            throw new RuntimeException("Error getting certificate from MinIO", e);
        }
    }

    /**
     * 检查证书是否存在
     *
     * @param sha1 证书的SHA1哈希值
     * @return 如果存在则返回true，否则返回false
     */
    public static boolean certificateExists(String sha1) {
        // 构建对象名称
        String objectName = buildObjectName(sha1);

        try {
            minioClient.statObject(
                    StatObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build());
            return true;
        } catch (ErrorResponseException e) {
            if (ERROR_NO_SUCH_KEY.equals(e.errorResponse().code())) {
                return false;
            }
            logger.error("Error checking if certificate exists in MinIO", e);
            throw new RuntimeException("Error checking if certificate exists in MinIO", e);
        } catch (Exception e) {
            logger.error("Error checking if certificate exists in MinIO", e);
            throw new RuntimeException("Error checking if certificate exists in MinIO", e);
        }
    }

    /**
     * 列出所有证书
     *
     * @return 证书对象名称列表
     */
    public static List<String> listCertificates() {
        List<String> certificates = new ArrayList<>();
        try {
            Iterable<Result<Item>> results = minioClient.listObjects(
                    ListObjectsArgs.builder()
                            .bucket(bucketName)
                            .prefix("certificates/")
                            .recursive(true)
                            .build());

            for (Result<Item> result : results) {
                Item item = result.get();
                certificates.add(item.objectName());
            }

            return certificates;
        } catch (Exception e) {
            logger.error("Error listing certificates from MinIO", e);
            throw new RuntimeException("Error listing certificates from MinIO", e);
        }
    }

    /**
     * 删除证书
     *
     * @param sha1 证书的SHA1哈希值
     * @throws Exception 如果删除失败
     */
    public static void deleteCertificate(String sha1) throws Exception {
        // 构建对象名称
        String objectName = buildObjectName(sha1);

        minioClient.removeObject(
                RemoveObjectArgs.builder()
                        .bucket(bucketName)
                        .object(objectName)
                        .build());
        logger.debug("Certificate deleted from MinIO: bucket={}, object={}", bucketName, objectName);
    }

    /**
     * 构建对象名称
     *
     * @param sha1 证书的SHA1哈希值
     * @return 对象名称
     */
    private static String buildObjectName(String sha1) {
        // 使用SHA1的前两个字符作为子目录，避免单个目录下文件过多
        String subDir = sha1.length() >= SHA1_PREFIX_LENGTH ? sha1.substring(0, SHA1_PREFIX_LENGTH) : "unknown";
        return "certificates/" + subDir + "/" + PREFIX + sha1;
    }

    /**
     * 从输入流读取所有字节
     *
     * @param inputStream 输入流
     * @return 字节数组
     * @throws IOException 如果读取失败
     */
    private static byte[] readAllBytes(InputStream inputStream) throws IOException {
        try {
            return inputStream.readAllBytes();
        } finally {
            inputStream.close();
        }
    }
}
