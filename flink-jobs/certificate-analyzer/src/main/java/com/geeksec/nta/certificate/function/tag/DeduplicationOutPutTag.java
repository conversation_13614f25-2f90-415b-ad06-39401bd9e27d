package com.geeksec.nta.certificate.function.tag;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.util.OutputTag;

/**
 * <AUTHOR>
 * @Date 2023/8/17
 */

public class DeduplicationOutPutTag {
    public static final OutputTag<X509Cert> Error_Dedup_cert = new OutputTag<>("Error_Dedup_cert", TypeInformation.of(X509Cert.class));
    public static final OutputTag<X509Cert> Error_Not_Dedup_cert = new OutputTag<>("Error_Not_Dedup_cert",TypeInformation.of(X509Cert.class));
    public static final OutputTag<X509Cert> Dedup_cert = new OutputTag<>("Dedup_cert", TypeInformation.of(X509Cert.class));
    public static final OutputTag<X509Cert> Not_Dedup_cert = new OutputTag<>("Not_Dedup_cert",TypeInformation.of(X509Cert.class));
}
