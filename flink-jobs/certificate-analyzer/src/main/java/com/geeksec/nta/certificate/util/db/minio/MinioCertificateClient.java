package com.geeksec.nta.certificate.util.db.minio;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MinIO证书客户端
 * 
 * <AUTHOR>
 * @date 2024/10/15
 */
public class MinioCertificateClient {
    private static final Logger logger = LoggerFactory.getLogger(MinioCertificateClient.class);

    /**
     * 查询多个证书
     *
     * @param sha1List 证书SHA1哈希值列表
     * @return 包含SHA1和对应证书数据的Map
     */
    public static Map<String, byte[]> queryDataList(List<String> sha1List) {
        Map<String, byte[]> queryData = new HashMap<>(sha1List.size());

        for (String sha1 : sha1List) {
            byte[] value = getCertByteBySha1(sha1);
            if (value != null) {
                queryData.put(sha1, value);
            }
        }

        return queryData;
    }

    /**
     * 根据SHA1获取证书数据
     *
     * @param sha1 证书SHA1哈希值
     * @return 证书数据，如果不存在则返回null
     */
    public static byte[] getCertByteBySha1(String sha1) {
        try {
            byte[] data = MinioClientUtil.getCertificate(sha1);
            if (data != null) {
                logger.debug("查询 SHA1: {} 的证书成功", sha1);
            } else {
                logger.debug("未找到 SHA1: {} 的证书", sha1);
            }
            return data;
        } catch (Exception e) {
            logger.error("查询 SHA1: {} 的证书失败: {}", sha1, e.getMessage());
            return null;
        }
    }

    /**
     * 检查证书是否存在
     *
     * @param sha1 证书SHA1哈希值
     * @return 如果存在则返回true，否则返回false
     */
    public static boolean isCertExists(String sha1) {
        try {
            return MinioClientUtil.certificateExists(sha1);
        } catch (Exception e) {
            logger.error("检查证书 SHA1: {} 是否存在失败: {}", sha1, e.getMessage());
            return false;
        }
    }

    /**
     * 上传证书
     *
     * @param sha1 证书SHA1哈希值
     * @param data 证书数据
     * @return 上传是否成功
     */
    public static boolean uploadCertificate(String sha1, byte[] data) {
        try {
            MinioClientUtil.uploadCertificate(sha1, data);
            return true;
        } catch (Exception e) {
            logger.error("上传证书 SHA1: {} 失败: {}", sha1, e.getMessage());
            return false;
        }
    }

    /**
     * 删除证书
     *
     * @param sha1 证书SHA1哈希值
     * @return 删除是否成功
     */
    public static boolean deleteCertificate(String sha1) {
        try {
            MinioClientUtil.deleteCertificate(sha1);
            return true;
        } catch (Exception e) {
            logger.error("删除证书 SHA1: {} 失败: {}", sha1, e.getMessage());
            return false;
        }
    }
}
