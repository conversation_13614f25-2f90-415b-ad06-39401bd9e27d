package com.geeksec.nta.certificate.transform.sign;

import static com.geeksec.analysisFunction.certSign.Cert1SignSplit.*;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.dbConnect.minio.MinioClient;
import com.geeksec.flinkTool.sideOutputTag.signOutPutTag;
import com.geeksec.utils.EsUtils;
import com.geeksec.utils.FileUtil;

import java.util.*;

import org.apache.commons.pool.impl.GenericObjectPool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.elasticsearch.client.RestHighLevelClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2023/1/18
 */

public class Cert2SignSplit extends ProcessFunction<Row, Row> {
    protected static final Logger LOG = LoggerFactory.getLogger(Cert2SignSplit.class);
    private static transient GenericObjectPool<RestHighLevelClient> esPool = null;
    private final Properties properties = FileUtil.getProperties("/config.properties");
    private static MinioClient minioClient = null;

    public static String SYSTEM_CERT_INDEX = null;

    @Override
    public void open(Configuration parameters) throws Exception {
        LOG.info("Initialization of global parameters starts.");
        Map<String, String> globalParam = getRuntimeContext().getExecutionConfig().getGlobalJobParameters().toMap();

        // 系统证书Index
        SYSTEM_CERT_INDEX = globalParam.get("es.system.index");
        LOG.info("cert system index:[ {} ].", SYSTEM_CERT_INDEX);

        // ES 初始化
        esPool = EsUtils.initEsPool();
        LOG.info("生成EsPool成功! {}", esPool.getNumIdle(), esPool.hashCode());

        // 初始化 MinIO 客户端
        minioClient = new MinioClient();
        LOG.info("MinIO client initialized with endpoint: {}", minioClient.getEndpoint());
    }

    @Override
    public void close() throws Exception {
        if (esPool != null) {
            esPool.close();
        }
        super.close();
    }

    /**
     * 处理证书验签
     * 应保持x509子证书的parentList字段与parentListMap的key相匹配
     */
    @Override
    public void processElement(Row signRow, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
        X509Cert x509Cert = (X509Cert) signRow.getField(0);
        Map<String,X509Cert> parentListMap = (Map<String, X509Cert>) signRow.getField(1);
        List<String> parentList = x509Cert.getParentCertIDList();
        List<String> tagList = x509Cert.getTagList();
        X509Cert testCert = parentListMap.get(parentList.get(parentList.size()-1));
        OutputTag<Row> signStop = signOutPutTag.stop_sign2;
        OutputTag<Row> signContinue = signOutPutTag.continue_sign2;

        // 注意：这里需要修改comprehensiveCertSign方法的参数，移除LMDB相关参数
        // 暂时使用null替代，后续需要完全重构该方法
        comprehensiveCertSign(signRow, context, testCert, tagList, x509Cert, signStop, parentList,
                             parentListMap, signContinue, esPool, null, null, null, null);
    }
}
