package com.geeksec.nta.certificate.transform.deduplication;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.utils.RedisBloomFilterUtils;
import org.apache.flink.api.common.functions.RichWindowFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.ArrayList;
import java.util.List;

/**
 * 使用Redis布隆过滤器进行批量证书去重的WindowFunction
 * 
 * <AUTHOR>
 * @date 2024/10/15
 */
public class BatchRedisBloomFilterDeduplicationFunction 
        extends RichWindowFunction<X509Cert, X509Cert, Integer, TimeWindow> {
    
    protected static final Logger LOG = LoggerFactory.getLogger(BatchRedisBloomFilterDeduplicationFunction.class);
    
    private transient JedisPool jedisPool;

    @Override
    public void open(Configuration parameters) throws Exception {
        // 初始化Redis连接池
        jedisPool = RedisBloomFilterUtils.initJedisPool();
        LOG.info("生成jedisPool成功! 活跃连接数——{}——，空闲数——{}——，等待数——{}——", 
                jedisPool.getNumActive(), jedisPool.getNumIdle(), jedisPool.getNumWaiters());
        
        // 初始化布隆过滤器
        try (Jedis jedis = RedisBloomFilterUtils.getJedis(jedisPool)) {
            RedisBloomFilterUtils.initBloomFilter(jedis);
        } catch (Exception e) {
            LOG.error("初始化布隆过滤器失败", e);
        }
        
        super.open(parameters);
    }

    @Override
    public void close() throws Exception {
        if (jedisPool != null) {
            jedisPool.close();
        }
        super.close();
    }

    @Override
    public void apply(Integer key, TimeWindow window, Iterable<X509Cert> input, Collector<X509Cert> out) {
        // 收集窗口中的所有证书
        List<X509Cert> certs = new ArrayList<>();
        List<String> certHashes = new ArrayList<>();
        
        for (X509Cert cert : input) {
            certs.add(cert);
            certHashes.add(cert.getASN1SHA1());
        }
        
        if (certs.isEmpty()) {
            return;
        }
        
        Jedis jedis = null;
        try {
            jedis = RedisBloomFilterUtils.getJedis(jedisPool);
            
            // 批量检查布隆过滤器
            boolean[] bloomResults = RedisBloomFilterUtils.batchCheckBloomFilter(certHashes, jedis);
            
            // 处理每个证书
            for (int i = 0; i < certs.size(); i++) {
                X509Cert cert = certs.get(i);
                String sourceType = cert.getCertSource();
                
                // 对于User类型的证书，直接通过（与原有逻辑保持一致）
                if ("User".equals(sourceType)) {
                    out.collect(cert);
                    continue;
                }
                
                if (!bloomResults[i]) {
                    // 布隆过滤器表明证书肯定不存在
                    RedisBloomFilterUtils.addToBloomFilter(cert, jedis);
                    RedisBloomFilterUtils.addExactKey(cert, jedis);
                    out.collect(cert);
                } else {
                    // 布隆过滤器表明证书可能存在，检查精确键
                    boolean exactExists = RedisBloomFilterUtils.checkExactKey(cert, jedis);
                    
                    if (!exactExists) {
                        // 布隆过滤器误判，添加精确键
                        RedisBloomFilterUtils.addExactKey(cert, jedis);
                        out.collect(cert);
                    } else {
                        // 确认是重复的
                        LOG.debug("检测到重复证书: {}", cert.getASN1SHA1());
                    }
                }
            }
        } catch (Exception e) {
            LOG.error("批量布隆过滤器去重失败: {}", e.getMessage());
            // 出错时，为了安全起见，继续处理所有证书
            for (X509Cert cert : certs) {
                out.collect(cert);
            }
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }
}
