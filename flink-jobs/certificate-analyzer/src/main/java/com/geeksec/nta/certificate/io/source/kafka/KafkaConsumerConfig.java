package com.geeksec.nta.certificate.io.source.kafka;

import java.util.Properties;
import org.apache.flink.configuration.Configuration;
import org.apache.kafka.clients.consumer.ConsumerConfig;

/**
 * Kafka消费者配置类
 *
 * @Author: <PERSON>uanHao
 * @Date: 2022/6/13 10:03
 * @Description： Kafka消费者配置
 */
public class KafkaConsumerConfig {

    /**
     * 获取Kafka消费者配置
     *
     * @param conf Flink配置对象
     * @return Kafka消费者配置属性
     */
    public static Properties getKafkaConsumerConfig(Configuration conf) {
        Properties properties = new Properties();

        //封装kafka的连接地址
        properties.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, conf.getString("kafka.host","localhost") + ":" + conf.getString("kafka.port","9092"));
        //指定消费者id
        properties.put(ConsumerConfig.GROUP_ID_CONFIG, conf.getString("kafka.group.id","error_group"));
        // 设置从最早offset开始读取
        properties.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG,conf.getString("auto.offset.reset","latest"));
        // 其他参数
        properties.put(ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG, "60000");
        properties.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG,5000);

        return properties;
    }
}
