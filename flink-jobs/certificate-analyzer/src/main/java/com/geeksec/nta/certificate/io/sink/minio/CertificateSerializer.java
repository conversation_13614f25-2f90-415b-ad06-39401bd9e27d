package com.geeksec.nta.certificate.io.sink.minio;

import com.geeksec.nta.certificate.model.cert.X509Cert;
import org.apache.flink.api.common.serialization.SimpleStringEncoder;
import org.apache.flink.core.fs.Path;
import org.apache.flink.streaming.api.functions.sink.filesystem.StreamingFileSink;
import org.apache.flink.streaming.api.functions.sink.filesystem.bucketassigners.BasePathBucketAssigner;
import org.apache.flink.streaming.api.functions.sink.filesystem.rollingpolicies.DefaultRollingPolicy;

import java.util.concurrent.TimeUnit;

/**
 * 证书序列化工具类，用于配置Flink的FileSink
 *
 * <AUTHOR>
 */
public class CertificateSerializer {

    /**
     * 证书文件名前缀
     */
    private static final String PREFIX = "cert_";

    /**
     * SHA1哈希值的前缀长度，用于创建子目录
     */
    private static final int SHA1_PREFIX_LENGTH = 2;

    /**
     * 创建用于存储证书的FileSink
     *
     * @param basePath MinIO基础路径，例如：s3://bucket-name/certificates
     * @return 配置好的StreamingFileSink
     */
    public static StreamingFileSink<X509Cert> createCertificateSink(String basePath) {
        // 设置滚动策略
        DefaultRollingPolicy<X509Cert, String> rollingPolicy = DefaultRollingPolicy
                .builder()
                .withRolloverInterval(TimeUnit.MINUTES.toMillis(15))
                .withInactivityInterval(TimeUnit.MINUTES.toMillis(5))
                .withMaxPartSize(1024 * 1024 * 128)
                .build();

        // 创建FileSink
        return StreamingFileSink
                .forRowFormat(new Path(basePath), new CertificateEncoder())
                .withBucketAssigner(new CertificateBucketAssigner())
                .withRollingPolicy(rollingPolicy)
                .build();
    }

    /**
     * 证书编码器，将X509Cert对象转换为字节数组
     */
    private static class CertificateEncoder extends SimpleStringEncoder<X509Cert> {
        public CertificateEncoder() {
            super("UTF-8");
        }

        @Override
        public byte[] encode(X509Cert cert) {
            // 直接返回证书的二进制数据
            return cert.getCert();
        }
    }

    /**
     * 证书桶分配器，根据证书的SHA1哈希值分配存储路径
     */
    private static class CertificateBucketAssigner extends BasePathBucketAssigner<X509Cert> {
        @Override
        public String getBucketId(X509Cert element, Context context) {
            // 使用证书的SHA1哈希值作为文件名
            String sha1 = element.getASN1SHA1();

            // 使用SHA1的前两个字符作为子目录，避免单个目录下文件过多
            if (sha1 != null && sha1.length() >= SHA1_PREFIX_LENGTH) {
                String subDir = sha1.substring(0, SHA1_PREFIX_LENGTH);
                return subDir + "/" + PREFIX + sha1;
            }

            // 如果SHA1为空或长度不足，使用时间戳作为文件名
            return "unknown/" + PREFIX + System.currentTimeMillis();
        }
    }
}
