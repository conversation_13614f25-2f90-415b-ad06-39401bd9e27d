package com.geeksec.nta.certificate.function.tag;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.util.OutputTag;

/**
 * <AUTHOR>
 * @Date 2023/7/5
 */

public class ErrorCorrectingOutPutTag {
    public static final OutputTag<X509Cert> errorUserCert = new OutputTag<>("errorUserCert", TypeInformation.of(X509Cert.class));
    public static final OutputTag<X509Cert> normalUserCert = new OutputTag<>("normalUserCert", TypeInformation.of(X509Cert.class));

    public static final OutputTag<X509Cert> SuccessNumNegativeCert = new OutputTag<>("SuccessNumNegativeCert", TypeInformation.of(X509Cert.class));
    public static final OutputTag<X509Cert> FailNumNegativeCert = new OutputTag<>("FailNumNegativeCert", TypeInformation.of(X509Cert.class));
    public static final OutputTag<X509Cert> SuccessNumPositiveCert = new OutputTag<>("SuccessNumPositiveCert", TypeInformation.of(X509Cert.class));
    public static final OutputTag<X509Cert> FailNumPositiveCert = new OutputTag<>("FailNumPositiveCert", TypeInformation.of(X509Cert.class));
    public static final OutputTag<X509Cert> SuccessReverseCert = new OutputTag<>("SuccessReverseCert", TypeInformation.of(X509Cert.class));
    public static final OutputTag<X509Cert> FailReverseCert = new OutputTag<>("FailReverseCert", TypeInformation.of(X509Cert.class));

    public static final OutputTag<X509Cert> normalSystemCert = new OutputTag<>("normalSystemCert", TypeInformation.of(X509Cert.class));
}
