package com.geeksec.nta.certificate.transform.deduplication;

import static com.geeksec.analysisFunction.deduplication.RedisDeduplicationFlatMapFunction.EsPool;
import static com.geeksec.flinkTool.sideOutputTag.DeduplicationOutPutTag.Error_Dedup_cert;
import static com.geeksec.flinkTool.sideOutputTag.DeduplicationOutPutTag.Error_Not_Dedup_cert;
import static com.geeksec.nta.pipeline.CertificateAnalysisPipeline.PA16;
import static com.geeksec.nta.pipeline.CertificateAnalysisPipeline.PA4;
import static com.geeksec.utils.EsUtils.MultiMatchQuery;
import static com.geeksec.utils.EsUtils.logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.pool.impl.GenericObjectPool;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;
import org.elasticsearch.client.RestHighLevelClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.flinkTool.labelKeySelector.CertKeySelector;
import com.geeksec.utils.EsUtils;

/**
 * <AUTHOR>
 * @Date 2023/8/17
 */

public class ErrorCertDeduplicationWindow {
    protected static final Logger LOG = LoggerFactory.getLogger(ErrorCertDeduplicationWindow.class);
    public static SingleOutputStreamOperator<X509Cert> errorCertDeduplicationWindow(DataStream<X509Cert> systemStream){
        SingleOutputStreamOperator<X509Cert> systemStreamWithTime = systemStream
                .assignTimestampsAndWatermarks(WatermarkStrategy.forMonotonousTimestamps()).name("系统时间戳水印").setParallelism(PA4)
                .keyBy(new CertKeySelector(4))
                .window(TumblingProcessingTimeWindows.of(Time.milliseconds(500)))
                .process(new ProcessWindowFunction<X509Cert, X509Cert, Integer, TimeWindow>() {
                    @Override
                    public void process(Integer integer, ProcessWindowFunction<X509Cert, X509Cert, Integer, TimeWindow>.Context context, Iterable<X509Cert> iterable, Collector<X509Cert> collector) throws Exception {
                        List<String> UserAsnSha1List = new ArrayList<>();
                        for (X509Cert x509Cert:iterable){
                            String certType = x509Cert.getCertSource();
                            if ("User".equals(certType)) {
                                UserAsnSha1List.add(x509Cert.getASN1SHA1());
                            } else {
                                logger.error("出现了预期外的证书来源类型");
                            }
                        }
                        RestHighLevelClient esClient = null;
                        try{
                            esClient = EsUtils.getClient(EsPool);
                            //此处匹配所有的证书index，合并去重
                            HashMap<String, Map> userResult = new HashMap<>();
                            if (!UserAsnSha1List.isEmpty()){
                                userResult =  MultiMatchQuery("cert_user", "ASN1SHA1", UserAsnSha1List, null, null,esClient);
                            }
                            for (X509Cert x509Cert:iterable){
                                String certType = x509Cert.getCertSource();
                                switch (certType){
                                    case "User":
                                        if (!userResult.containsKey(x509Cert.getASN1SHA1())){
                                            x509Cert.setIsDedupCert(false);
                                            context.output(Error_Not_Dedup_cert,x509Cert);
                                        }else {
                                            //这一步是从User重复证书中提取所需要的数据，后续可以再加
                                            getDedupCertInfo(x509Cert, userResult);
                                            getNewUserIDList(x509Cert, userResult);
                                            context.output(Error_Dedup_cert,x509Cert);
                                        }
                                        break;
                                    default:
                                        logger.error("出现了预期外的证书来源类型");
                                        break;
                                }
                            }
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }finally {
                            if(esClient != null){
                                EsUtils.returnClient(esClient, EsPool);
                            }
                        }
                    }
                }).name("处理时间窗口函数去重错误证书").setParallelism(PA16);

        return systemStreamWithTime;
    }

    public static void getDedupCertInfo(X509Cert x509Cert, Map<String,Map> Result){
        String SHA1 = x509Cert.getASN1SHA1();
        Map<String,Object> result_cert = Result.get(SHA1);
        x509Cert.setIsDedupCert(true);
        x509Cert.setSubject(new LinkedHashMap<>((HashMap)result_cert.getOrDefault("Subject","unk")));
        x509Cert.setBlackList((Integer) result_cert.getOrDefault("BlackList",0));
        x509Cert.setWhiteList((Integer) result_cert.getOrDefault("WhiteList",0));
        x509Cert.setTagList((List<String>) result_cert.getOrDefault("Labels",new ArrayList<>()));
    }

    public static void updateESUserList(GenericObjectPool<RestHighLevelClient> EsPool, String index, List<String> UserIDList, String SHA1) throws Exception {
        RestHighLevelClient EsClient = EsUtils.getClient(EsPool);
        try{
            EsUtils.updateAndSearchDocuments(index,SHA1,UserIDList,EsClient);
        }catch (Exception e){
            LOG.error("获取EsClient失败，error--->{}",e);
        }finally {
            if(EsClient != null){
                EsUtils.returnClient(EsClient,EsPool);
            }
        }
    }

    public static void getNewUserIDList(X509Cert x509Cert,Map<String,Map> Result) throws Exception {
        String SHA1 = x509Cert.getASN1SHA1();
        Map<String,Object> result_cert = Result.get(SHA1);
        List<String> ESUserIDList = (List<String>) result_cert.get("UserIDList");
        List<String> NowUserIDList = x509Cert.getUserIDList();
        Set<String> NewUserIDSet = new HashSet<>();
        if (ESUserIDList!=null){
            NewUserIDSet.addAll(ESUserIDList);
        }
        NewUserIDSet.addAll(NowUserIDList);
        List<String> NewUserIDList = new ArrayList<>(NewUserIDSet);
        if (!NewUserIDList.equals(ESUserIDList)){
            updateESUserList(EsPool,"cert_user",NewUserIDList, x509Cert.getASN1SHA1());
            LOG.info("向SHA1为——{}——的错误证书更新用户ID",SHA1);
        }else {
            LOG.info("无需更新错误证书的用户ID");
        }
    }
}
