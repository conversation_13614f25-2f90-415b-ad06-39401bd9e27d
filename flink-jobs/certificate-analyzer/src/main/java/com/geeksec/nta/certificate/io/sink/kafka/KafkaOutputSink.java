package com.geeksec.nta.certificate.io.sink.kafka;

import static com.geeksec.analysisFunction.certInfoAll.CertScoreMapRichFunction.LABEL_INFO_LIST;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.nta.certificate.util.FileUtil;
import java.util.*;
import javax.annotation.Nullable;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.apache.flink.streaming.connectors.kafka.KafkaSerializationSchema;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Kafka输出接收器，用于将数据写入Kafka
 *
 * <AUTHOR>
 * @Date 2023/6/5
 */

public class KafkaOutputSink {

    private static final Logger LOGGER = LoggerFactory.getLogger(KafkaOutputSink.class);
    private static final Properties KAFKA_SINK_PROPERTIES = new Properties();

    /**
     * 配置属性
     */
    private static final Properties PROPERTIES = FileUtil.getProperties("/config.properties");
    
    /**
     * Kafka输出主题
     */
    public static final String OUTPUT_TOPIC = PROPERTIES.getProperty("kafka.output.topic");
    
    /**
     * Kafka代理服务器列表
     */
    private static String outputBrokerList = "";

    /**
     * 将告警数据写入Kafka
     *
     * @param alarmJsonStream 告警JSON数据流
     * @param kafkaInfo Kafka连接信息
     * @throws Exception 如果写入过程中发生错误
     */
    public static void alarmKafkaSink(DataStream<JSONObject> alarmJsonStream, Map<String,Object> kafkaInfo) throws Exception {
        outputBrokerList = kafkaInfo.get("ip") + ":" + kafkaInfo.get("port");
        LOGGER.info("Kafka broker list: {}", outputBrokerList);

        KafkaSerializationSchema<String> kafkaSerializationSchema = new KafkaSerializationSchema<String>() {
            @Override
            public ProducerRecord<byte[], byte[]> serialize(String s, @Nullable Long aLong) {
                String time = String.valueOf(System.currentTimeMillis());
                return new ProducerRecord<>(
                        OUTPUT_TOPIC,
                        time.getBytes(),
                        s.getBytes()
                );
            }
        };
        
        KAFKA_SINK_PROPERTIES.put("bootstrap.servers", outputBrokerList);
        KAFKA_SINK_PROPERTIES.put("transaction.timeout.ms", "5000");
        
        FlinkKafkaProducer<String> kafkaProducer = new FlinkKafkaProducer<>(
                OUTPUT_TOPIC,
                kafkaSerializationSchema,
                KAFKA_SINK_PROPERTIES,
                FlinkKafkaProducer.Semantic.AT_LEAST_ONCE
        );
        DataStream<String> alarmStringStream = alarmJsonStream.map(new MapFunction<JSONObject, String>() {
            @Override
            public String map(JSONObject jsonObject) throws Exception {
                return jsonObject.toString();
            }
        });
        alarmStringStream.addSink(kafkaProducer).name("kafka 外发告警日志").setParallelism(2);
    }



    /**
     * 将标签值转换为威胁标签
     *
     * @param labelsValue 标签值列表
     * @return 威胁标签列表
     */
    private static List<Map<String, Object>> tranTagsToThreatTag(List<String> labelsValue) {
        List<Map<String, Object>> threatCertTagList = new ArrayList<>(labelsValue.size());
        for (String tag : labelsValue) {
            Map<String, Object> threatCertTag = new HashMap<>(4);
            Integer tagId = Integer.parseInt(tag);
            Map<String, String> tagInfo = LABEL_INFO_LIST.getOrDefault(tag, new HashMap<>(0));
            Integer blackList = Integer.parseInt(tagInfo.get("Black_List"));
            String tagLevel = "";
            
            if (0 < blackList && blackList < 40) {
                tagLevel = "low";
            } else if (40 <= blackList && blackList < 70) {
                tagLevel = "medium";
            } else if (70 <= blackList && blackList <= 100) {
                tagLevel = "high";
            }
            
            if (!"".equals(tagLevel)) {
                threatCertTag.put("tag_id", tagId);
                threatCertTag.put("tag_name", tagInfo.getOrDefault("Tag_Text", "unk"));
                threatCertTag.put("tag_level", tagLevel);
                threatCertTagList.add(threatCertTag);
            }
        }
        return threatCertTagList;
    }
}
